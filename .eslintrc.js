module.exports = {
	root: true,
	env: {
		browser: true,
		node: true,
	},
	parserOptions: {
		parser: '@babel/eslint-parser',
		requireConfigFile: false,
	},
	extends: ['@nuxtjs', 'plugin:nuxt/recommended'],
	plugins: [],
	// add your custom rules here
	rules: {
		'vue/no-v-html': 'off',
		'nuxt/no-cjs-in-config': 'off',
		'no-tabs': 'off',
		indent: 'off',
		camelcase: [
			'error',
			{
				properties: 'never',
			},
		],
		"eol-last": [2, "always"],
		'no-console': 'off',
		'vue/multi-word-component-names': 'off',
		'vue/valid-v-slot': ['error', { allowModifiers: true }],
		'space-before-function-paren': 'off',
		'vue/html-indent': 'off',
		'comma-dangle': ["error", "always-multiline"],
		quotes: 'off',
		'vue/max-attributes-per-line': 'off',
		'vue/first-attribute-linebreak': 'off',
		'vue/html-closing-bracket-newline': 'off',
	},
}
