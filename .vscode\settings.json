{
	"nuxt.isNuxtApp": true,
	"better-comments.tags": [
		{
			"tag": "!",
			"color": "#FF2D00",
			"strikethrough": false,
			"underline": false,
			"backgroundColor": "transparent",
			"bold": false,
			"italic": false
		},
		{
			"tag": "?",
			"color": "#3498DB",
			"strikethrough": false,
			"underline": false,
			"backgroundColor": "transparent",
			"bold": false,
			"italic": false
		},
		{
			"tag": "//",
			"color": "#474747",
			"strikethrough": true,
			"underline": false,
			"backgroundColor": "transparent",
			"bold": false,
			"italic": false
		},
		{
			"tag": "todo",
			"color": "#FF8C00",
			"strikethrough": false,
			"underline": false,
			"backgroundColor": "transparent",
			"bold": false,
			"italic": false
		},
		{
			"tag": "*",
			"color": "#98C379",
			"strikethrough": false,
			"underline": false,
			"backgroundColor": "transparent",
			"bold": false,
			"italic": false
		},
		{
			"tag": "@",
			"color": "#ffffff",
			"strikethrough": false,
			"underline": false,
			"backgroundColor": "#2196F3",
			"bold": true,
			"italic": true
		}
	],
	"i18n-ally.localesPaths": [
		"locales"
	],
	"i18n-ally.enabledFrameworks": [
		"vue"
	],
	"i18n-ally.pathMatcher": "{locale}.json",
	"i18n-ally.keystyle": "nested",
	"i18n-ally.extract.autoDetect": true,
	"i18n-ally.displayLanguage": "en",
	"i18n-ally.sourceLanguage": "en",
	"cSpell.words": [
		"Analitics",
		"lazyimg",
		"tiktok",
		"walletable",
		"Wishlisted",
		"Wishlisting"
	],
	"vetur.format.defaultFormatterOptions": {
		"js-beautify-html": {
			"wrap_attributes": "force-expand-multiline"
		},
		"prettyhtml": {
			"printWidth": 100,
			"singleQuote": false,
			"wrapAttributes": false,
			"sortAttributes": false
		}
	},
	"editor.detectIndentation": false,
	"editor.tabSize": 3,
	"editor.insertSpaces": false,
	"editor.wordBasedSuggestions": "off",
	"html.autoClosingTags": true,
	"vetur.format.defaultFormatter.less": "none",
	"vetur.format.defaultFormatter.css": "prettier",
	"vetur.format.defaultFormatter.html": "prettier",
	"vetur.format.defaultFormatter.postcss": "prettier",
	"vetur.format.defaultFormatter.js": "prettier",
	"vetur.format.defaultFormatter.scss": "prettier",
	"vetur.format.defaultFormatter.stylus": "stylus-supremacy",
	"vetur.format.defaultFormatter.ts": "prettier",
	"vetur.format.options.tabSize": 2,
	"vetur.format.options.useTabs": false,
	"vetur.format.scriptInitialIndent": false,
	"vetur.format.styleInitialIndent": false,
	"vetur.underline.refValue": false,
	"vetur.validation.interpolation": false,
	"vetur.validation.script": false,
	"vetur.validation.style": false,
	"vetur.validation.template": false,
	"vetur.validation.templateProps": false,
	"workbench.colorCustomizations": {
		"commandCenter.border": "#e7e7e799",
		"sash.hoverBorder": "#893477",
		"titleBar.activeBackground": "#893477",
		"titleBar.activeForeground": "#e7e7e7",
		"titleBar.inactiveBackground": "#893477",
		"titleBar.inactiveForeground": "#e7e7e799"
	},
	"nuxt.portNumber": 3002,
	"path-intellisense.showHiddenFiles": true,
	"diffEditor.ignoreTrimWhitespace": true,
	"[vue]": {
		"editor.defaultFormatter": "dbaeumer.vscode-eslint"
	},
	"translator.createURL": "http://localhost:3001/api/v1/admin/portal/translations",
	"translator.username": "<EMAIL>",
	"translator.password": "Trans@2023",
	"translator.tags": "9",
	"translator.translationsURL": "http://localhost:3001/v1/lookups/translations/en/",
	"translator.isLoginRequired": true,
	"translator.loginURL": "https://localhost:8002/v1/auth/login",
	"translator.extraHeaders": {
		"Content-Type": "application/json",
		"Accept": "application/json",
		"Entity": "portal.ingotbrokers.com",
		"Language": "en"
	},
	// "vue.format.template.initialIndent": false,
	"eslint.format.enable": true,
	"editor.formatOnSave": true,
}