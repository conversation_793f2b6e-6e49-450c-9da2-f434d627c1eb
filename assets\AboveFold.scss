/* arabic */
@font-face {
    font-family: "Almarai";
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url("~/assets/fonts/Almarai-Light.woff2") format("woff2");
    unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC, U+0000-00FF, U+0131,
        U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
        U+2215, U+FEFF, U+FFFD;
}
/* arabic */
@font-face {
    font-family: "Almarai";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url("~/assets/fonts/Almarai-Regular.woff2") format("woff2");
    unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC, U+0000-00FF, U+0131,
        U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
        U+2215, U+FEFF, U+FFFD;
}
/* arabic */
@font-face {
    font-family: "Almarai";
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url("~/assets/fonts/Almarai-Bold.woff2") format("woff2");
    unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC, U+0000-00FF, U+0131,
        U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
        U+2215, U+FEFF, U+FFFD;
}

$main-bar-height: 72px;
$secondary-bar-height: 42px;
$main-and-secondary-bar-height: $main-bar-height + $secondary-bar-height;
:root {
    --main-bar-height: #{$main-bar-height};
    --secondary-bar-height: #{$secondary-bar-height};
    --main-and-secondary-bar-height: #{$main-and-secondary-bar-height};
}

// mobile
@media screen and (max-width: 600px) {
    :root {
        --main-bar-height: #{$main-bar-height};
        --secondary-bar-height: 0;
        --main-and-secondary-bar-height: #{$main-bar-height};
    }
    
}
.main-header {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: transparent !important;

  
	&.v-app-bar--is-scrolled {       
        transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);		
	}


    

    &.white:not(.v-app-bar--is-scrolled),
    &.white:not(.v-app-bar--is-scrolled) > div {
        background-color: transparent !important;
    }
    &.v-app-bar--is-scrolled {

        background-color: rgba(255, 255, 255, 0.9) !important;
        @supports (-webkit-backdrop-filter: none) or (backdrop-filter: none) {
            -webkit-backdrop-filter: blur(20px);
            backdrop-filter: blur(20px);
            background-color: rgba(255, 255, 255, 0.5) !important;
        }

    }
}
.v-main {
    padding: 64px 0px 0px;
}
.slide-show {
    width: 100vw;
    height: 100vh;
    // margin-top: -$main-header-max-height; //64 app bar + 32 system bar
    // padding: $main-header-max-height 0;
    background-size: cover;
    .slides-wrapper {
        padding-top: 44px;
    }
}
.discount-label,.pre-order-label {
    background: linear-gradient(39deg, rgba(136, 31, 31, 1) 4%, rgba(245, 26, 79, 1) 100%) !important;
    color: #fff;
    min-height: 24px;
    width: auto;
    font-size: 12px;
    padding: 4px 16px;
    // letter-spacing: 1.05px;
    display: flex;
    flex-wrap: wrap;
    // font-family: Helvetica, Arial, sans-serif;
}
.p-label {
    display: inline-block;
    border-radius: 4px;
    padding-inline: 12px;
    font-size: 12px;
    padding-top: 2px;
}
.emphasis {
    text-shadow: 2px 1px #fff;
}
.number {
    font-variant-numeric: tabular-nums;
    font-family: Helvetica, Arial, sans-serif;
    &.enlarge {
        font-size: 105%;
        line-height: 105%;
    }
}
h1{
    font-weight: 400;
}

.main-search {
	.v-text-field__slot {
		label {
			top: 50% !important;
			transform: translateY(-50%);
		}
	}
	.v-input__append-inner {
		margin-top: auto !important;
		margin-bottom: auto !important;
	}
}

#main-search-categories-menu {
	.v-list-item {
		min-height: 32px !important;
	}
}

ul.v-breadcrumbs {
    padding-left: 0;
}

.buy-card{
    .v-list-item {
		min-height: 32px !important;
        .v-list-item__icon{
            margin-block:0
        }
	}
}