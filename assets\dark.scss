@import "~/node_modules/vuetify/src/styles/settings/colors";
$primary: #b71f23;
$accent: map-get($grey, "darken-3");
$secondary: #1a9f72;
$info: map-get($blue, "lighten-3");
$warning: map-get($amber, "base");
$error: map-get($deep-orange, "accent-4");
$success: map-get($green, "accent-3");
$sidebar: #363636;
$gold: #f3c405;
$inactive-tab: map-get($shades, "black");
$active-tab: #272727;
$background: #121212;
$deposit: map-get($teal, "base");

:export {
    primary: $primary;
    accent: $accent;
    secondary: $secondary;
    info: $info;
    error: $error;
    success: $success;
    sidebar: $sidebar;
    gold: $gold;
    inactive-tab: $inactive-tab;
    active-tab: $active-tab;
    background: $background;
    deposit: $deposit;
}

.inactive-item {
    background-color: rgb(251 140 0 / 14%);
    opacity: 0.3;
}
@import "./scrollbar.scss";
