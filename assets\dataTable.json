[{"id": 1, "transactionId": "***************************", "username": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 672, "currency": "JOD", "date": "21-28-2023", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:100", "balance": 3089, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "29-24-2022"}, {"id": 2, "transactionId": "************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 3134, "currency": "EUR", "date": "03-06-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:500", "balance": 669, "group": "Professional-USD", "platform": "MT5", "createdAt": "05-50-2022"}, {"id": 3, "transactionId": "*************************", "username": "<PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 3593, "currency": "JOD", "date": "30-00-2023", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:200", "balance": 3282, "group": "Professional-USD", "platform": "MT5", "createdAt": "12-19-2022"}, {"id": 4, "transactionId": "*******************", "username": "<PERSON><PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 6341, "currency": "JOD", "date": "17-12-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:100", "balance": 7487, "group": "ECN-EUR", "platform": "MT5", "createdAt": "02-44-2023"}, {"id": 5, "transactionId": "***************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 7629, "currency": "EUR", "date": "25-19-2021", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:800", "balance": 22, "group": "ECN-EUR", "platform": "MT4", "createdAt": "19-58-2021"}, {"id": 6, "transactionId": "***************************", "username": "Aquila Tyagi", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 9197, "currency": "EUR", "date": "22-15-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:500", "balance": 372, "group": "ECN-EUR", "platform": "MT4", "createdAt": "09-19-2022"}, {"id": 7, "transactionId": "***************************", "username": "<PERSON><PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 7608, "currency": "USD", "date": "29-17-2022", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:500", "balance": 3063, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "26-41-2022"}, {"id": 8, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 6695, "currency": "EUR", "date": "19-51-2021", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:100", "balance": 4526, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "23-21-2021"}, {"id": 9, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 7271, "currency": "EUR", "date": "18-50-2023", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:200", "balance": 6446, "group": "ECN-EUR", "platform": "MT5", "createdAt": "19-12-2023"}, {"id": 10, "transactionId": "****************************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 4735, "currency": "JOD", "date": "24-53-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:800", "balance": 2211, "group": "ECN-EUR", "platform": "MT4", "createdAt": "22-05-2022"}, {"id": 11, "transactionId": "***************************", "username": "<PERSON><PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 6840, "currency": "USD", "date": "26-36-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:100", "balance": 304, "group": "ECN-EUR", "platform": "MT5", "createdAt": "26-09-2023"}, {"id": 12, "transactionId": "***************************", "username": "<PERSON><PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 7950, "currency": "JOD", "date": "01-12-2023", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:500", "balance": 8296, "group": "Professional-USD", "platform": "MT4", "createdAt": "11-09-2023"}, {"id": 13, "transactionId": "*********************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 3250, "currency": "USD", "date": "24-25-2022", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:400", "balance": 4793, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "09-51-2022"}, {"id": 14, "transactionId": "****************************", "username": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 245, "currency": "EUR", "date": "01-55-2021", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:800", "balance": 7078, "group": "ECN-EUR", "platform": "MT4", "createdAt": "09-10-2022"}, {"id": 15, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 2171, "currency": "USD", "date": "01-50-2023", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:800", "balance": 8684, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "15-30-2021"}, {"id": 16, "transactionId": "***************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 2451, "currency": "JOD", "date": "18-24-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:500", "balance": 8779, "group": "Professional-USD", "platform": "MT4", "createdAt": "19-08-2022"}, {"id": 17, "transactionId": "**********************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 4799, "currency": "USD", "date": "29-42-2023", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:500", "balance": 6016, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "18-37-2021"}, {"id": 18, "transactionId": "************************", "username": "<PERSON><PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 5021, "currency": "USD", "date": "11-13-2021", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:100", "balance": 1932, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "01-22-2022"}, {"id": 19, "transactionId": "********************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 7643, "currency": "USD", "date": "02-53-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:400", "balance": 8188, "group": "Professional-USD", "platform": "MT5", "createdAt": "07-05-2022"}, {"id": 20, "transactionId": "********************", "username": "<PERSON><PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 2226, "currency": "USD", "date": "19-01-2023", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:200", "balance": 9061, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "03-28-2022"}, {"id": 21, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 4088, "currency": "EUR", "date": "22-16-2021", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:800", "balance": 7481, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "31-53-2022"}, {"id": 22, "transactionId": "**********************", "username": "Indigo Ellis", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 9247, "currency": "EUR", "date": "05-33-2023", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:400", "balance": 5438, "group": "ECN-EUR", "platform": "MT4", "createdAt": "23-55-2021"}, {"id": 23, "transactionId": "************************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 5173, "currency": "JOD", "date": "03-57-2021", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:800", "balance": 6179, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "03-08-2021"}, {"id": 24, "transactionId": "********************", "username": "<PERSON><PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 4220, "currency": "JOD", "date": "20-57-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:400", "balance": 7692, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "16-24-2023"}, {"id": 25, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 5478, "currency": "EUR", "date": "31-40-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:800", "balance": 5611, "group": "ECN-EUR", "platform": "MT4", "createdAt": "24-13-2023"}, {"id": 26, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 2530, "currency": "JOD", "date": "17-01-2023", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:500", "balance": 6319, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "27-54-2023"}, {"id": 27, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 4135, "currency": "JOD", "date": "30-02-2021", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:800", "balance": 6154, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "08-28-2022"}, {"id": 28, "transactionId": "************************", "username": "<PERSON><PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 1134, "currency": "EUR", "date": "15-58-2023", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:800", "balance": 7810, "group": "Professional-USD", "platform": "MT4", "createdAt": "25-41-2021"}, {"id": 29, "transactionId": "***************************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 3527, "currency": "JOD", "date": "07-02-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:100", "balance": 6747, "group": "ECN-EUR", "platform": "MT4", "createdAt": "21-25-2022"}, {"id": 30, "transactionId": "************************", "username": "Bell Morris", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 9305, "currency": "EUR", "date": "30-09-2021", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:800", "balance": 4555, "group": "Professional-USD", "platform": "MT5", "createdAt": "27-12-2022"}, {"id": 31, "transactionId": "****************************", "username": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 3037, "currency": "JOD", "date": "11-32-2022", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:500", "balance": 7749, "group": "ECN-EUR", "platform": "MT4", "createdAt": "24-55-2021"}, {"id": 32, "transactionId": "******************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 5811, "currency": "EUR", "date": "31-12-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:200", "balance": 6785, "group": "ECN-EUR", "platform": "MT5", "createdAt": "08-53-2022"}, {"id": 33, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 6184, "currency": "USD", "date": "30-07-2021", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:200", "balance": 4137, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "11-55-2022"}, {"id": 34, "transactionId": "******************", "username": "<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 5600, "currency": "JOD", "date": "23-37-2021", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:500", "balance": 8201, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "03-45-2022"}, {"id": 35, "transactionId": "********************", "username": "<PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 6850, "currency": "JOD", "date": "30-13-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:200", "balance": 4888, "group": "Professional-USD", "platform": "MT4", "createdAt": "27-46-2023"}, {"id": 36, "transactionId": "*********************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 8469, "currency": "JOD", "date": "28-35-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:200", "balance": 7960, "group": "ECN-EUR", "platform": "MT4", "createdAt": "13-27-2021"}, {"id": 37, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 3275, "currency": "JOD", "date": "15-38-2022", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:500", "balance": 572, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "02-40-2022"}, {"id": 38, "transactionId": "************************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 5102, "currency": "USD", "date": "24-17-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:800", "balance": 647, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "26-23-2022"}, {"id": 39, "transactionId": "**********************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 4569, "currency": "EUR", "date": "24-27-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:200", "balance": 6717, "group": "Professional-USD", "platform": "MT4", "createdAt": "08-17-2022"}, {"id": 40, "transactionId": "****************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 7654, "currency": "EUR", "date": "27-36-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:500", "balance": 9367, "group": "Professional-USD", "platform": "MT5", "createdAt": "13-20-2022"}, {"id": 41, "transactionId": "****************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 4503, "currency": "JOD", "date": "27-54-2021", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:500", "balance": 4360, "group": "ECN-EUR", "platform": "MT4", "createdAt": "18-08-2022"}, {"id": 42, "transactionId": "******************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 2227, "currency": "USD", "date": "11-37-2021", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:100", "balance": 6829, "group": "Professional-USD", "platform": "MT5", "createdAt": "08-44-2021"}, {"id": 43, "transactionId": "*********************", "username": "Minerva Taylor", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 3549, "currency": "EUR", "date": "23-30-2021", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:200", "balance": 6538, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "15-33-2022"}, {"id": 44, "transactionId": "***************", "username": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 7554, "currency": "USD", "date": "17-26-2023", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:500", "balance": 3582, "group": "Professional-USD", "platform": "MT4", "createdAt": "18-02-2022"}, {"id": 45, "transactionId": "************************", "username": "Barclay Engineer", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 3097, "currency": "USD", "date": "20-56-2021", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:800", "balance": 4793, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "27-28-2022"}, {"id": 46, "transactionId": "************************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 1602, "currency": "EUR", "date": "16-01-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:200", "balance": 6902, "group": "ECN-EUR", "platform": "MT5", "createdAt": "26-03-2021"}, {"id": 47, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 125, "currency": "EUR", "date": "06-24-2022", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:500", "balance": 1592, "group": "Professional-USD", "platform": "MT5", "createdAt": "22-04-2022"}, {"id": 48, "transactionId": "******************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 6005, "currency": "USD", "date": "10-22-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:200", "balance": 6712, "group": "Professional-USD", "platform": "MT5", "createdAt": "27-03-2022"}, {"id": 49, "transactionId": "***************************", "username": "<PERSON><PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 3546, "currency": "EUR", "date": "06-44-2022", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:500", "balance": 7873, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "26-24-2022"}, {"id": 50, "transactionId": "************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 3742, "currency": "JOD", "date": "31-55-2022", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:800", "balance": 3814, "group": "ECN-EUR", "platform": "MT4", "createdAt": "21-39-2022"}, {"id": 51, "transactionId": "******************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 8252, "currency": "EUR", "date": "17-37-2022", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:200", "balance": 5601, "group": "Professional-USD", "platform": "MT5", "createdAt": "12-19-2023"}, {"id": 52, "transactionId": "******************************", "username": "<PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 2005, "currency": "EUR", "date": "20-35-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:800", "balance": 7625, "group": "ECN-EUR", "platform": "MT5", "createdAt": "16-15-2022"}, {"id": 53, "transactionId": "********************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 2863, "currency": "JOD", "date": "08-11-2021", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:100", "balance": 3735, "group": "ECN-EUR", "platform": "MT4", "createdAt": "15-07-2023"}, {"id": 54, "transactionId": "********************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 464, "currency": "USD", "date": "16-20-2022", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:200", "balance": 6592, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "10-00-2022"}, {"id": 55, "transactionId": "**************************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 7897, "currency": "JOD", "date": "14-01-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:100", "balance": 7579, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "08-41-2023"}, {"id": 56, "transactionId": "****************", "username": "<PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 1030, "currency": "JOD", "date": "11-53-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:800", "balance": 2945, "group": "Professional-USD", "platform": "MT5", "createdAt": "24-13-2022"}, {"id": 57, "transactionId": "************************", "username": "Kiona Green", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 1665, "currency": "EUR", "date": "07-05-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:800", "balance": 8651, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "20-34-2023"}, {"id": 58, "transactionId": "****************************", "username": "<PERSON><PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 1211, "currency": "JOD", "date": "21-55-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:800", "balance": 8383, "group": "Professional-USD", "platform": "MT4", "createdAt": "08-56-2023"}, {"id": 59, "transactionId": "*********************", "username": "<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 1117, "currency": "USD", "date": "20-08-2023", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:200", "balance": 8139, "group": "Professional-USD", "platform": "MT4", "createdAt": "04-51-2022"}, {"id": 60, "transactionId": "*********************", "username": "<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 7752, "currency": "EUR", "date": "11-54-2022", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:100", "balance": 5672, "group": "ECN-EUR", "platform": "MT4", "createdAt": "11-36-2022"}, {"id": 61, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 6274, "currency": "USD", "date": "24-06-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:100", "balance": 3160, "group": "Professional-USD", "platform": "MT4", "createdAt": "15-17-2023"}, {"id": 62, "transactionId": "***************************", "username": "<PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 4186, "currency": "USD", "date": "02-16-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:100", "balance": 7157, "group": "Professional-USD", "platform": "MT5", "createdAt": "06-50-2022"}, {"id": 63, "transactionId": "***************************", "username": "Hay<PERSON> Rai", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 9637, "currency": "JOD", "date": "28-05-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:800", "balance": 7141, "group": "Professional-USD", "platform": "MT5", "createdAt": "16-23-2022"}, {"id": 64, "transactionId": "**************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 3362, "currency": "USD", "date": "06-00-2023", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:100", "balance": 2397, "group": "Professional-USD", "platform": "MT5", "createdAt": "06-43-2022"}, {"id": 65, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 5743, "currency": "USD", "date": "27-53-2021", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:800", "balance": 3920, "group": "ECN-EUR", "platform": "MT5", "createdAt": "20-33-2022"}, {"id": 66, "transactionId": "******************", "username": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 3674, "currency": "EUR", "date": "11-51-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:400", "balance": 9104, "group": "ECN-EUR", "platform": "MT4", "createdAt": "14-05-2023"}, {"id": 67, "transactionId": "****************************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 2385, "currency": "JOD", "date": "28-44-2023", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:100", "balance": 6592, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "23-47-2023"}, {"id": 68, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 7457, "currency": "USD", "date": "21-36-2023", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:800", "balance": 2222, "group": "ECN-EUR", "platform": "MT4", "createdAt": "15-50-2022"}, {"id": 69, "transactionId": "****************************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 4295, "currency": "JOD", "date": "04-54-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:400", "balance": 4461, "group": "Professional-USD", "platform": "MT5", "createdAt": "04-03-2023"}, {"id": 70, "transactionId": "**********************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 4952, "currency": "USD", "date": "18-00-2023", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:800", "balance": 8319, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "15-38-2022"}, {"id": 71, "transactionId": "*********************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 9611, "currency": "USD", "date": "03-31-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:400", "balance": 3993, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "05-39-2022"}, {"id": 72, "transactionId": "******************", "username": "Nero Park", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 221, "currency": "EUR", "date": "20-50-2022", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:200", "balance": 1570, "group": "Professional-USD", "platform": "MT4", "createdAt": "12-00-2023"}, {"id": 73, "transactionId": "******************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 8466, "currency": "EUR", "date": "27-12-2022", "transactionStatus": "rejected", "transactionType": "out", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:100", "balance": 8418, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "13-34-2023"}, {"id": 74, "transactionId": "**************************", "username": "<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 8722, "currency": "USD", "date": "26-16-2022", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:200", "balance": 1573, "group": "Professional-USD", "platform": "MT4", "createdAt": "22-39-2021"}, {"id": 75, "transactionId": "*******************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 7452, "currency": "EUR", "date": "11-35-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:800", "balance": 3928, "group": "ECN-EUR", "platform": "MT4", "createdAt": "31-17-2023"}, {"id": 76, "transactionId": "****************************", "username": "<PERSON><PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 1033, "currency": "JOD", "date": "09-26-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:200", "balance": 7307, "group": "Profissional-EUR", "platform": "MT4", "createdAt": "25-22-2022"}, {"id": 77, "transactionId": "***************************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 6108, "currency": "USD", "date": "17-30-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:500", "balance": 6369, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "08-19-2021"}, {"id": 78, "transactionId": "****************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 9943, "currency": "USD", "date": "05-45-2022", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:500", "balance": 9022, "group": "ECN-EUR", "platform": "MT4", "createdAt": "23-41-2021"}, {"id": 79, "transactionId": "****************", "username": "<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 3906, "currency": "EUR", "date": "01-01-2021", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:400", "balance": 4005, "group": "Professional-USD", "platform": "MT4", "createdAt": "10-54-2022"}, {"id": 80, "transactionId": "************************", "username": "Imo<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 376, "currency": "EUR", "date": "01-03-2023", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:400", "balance": 2729, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "15-35-2022"}, {"id": 81, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 2708, "currency": "USD", "date": "10-21-2023", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:100", "balance": 5571, "group": "Professional-USD", "platform": "MT5", "createdAt": "30-56-2023"}, {"id": 82, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 7884, "currency": "JOD", "date": "22-22-2021", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:800", "balance": 6847, "group": "ECN-EUR", "platform": "MT4", "createdAt": "13-23-2023"}, {"id": 83, "transactionId": "*******************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 1905, "currency": "USD", "date": "18-31-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:400", "balance": 4142, "group": "ECN-EUR", "platform": "MT5", "createdAt": "30-58-2022"}, {"id": 84, "transactionId": "***********************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 2332, "currency": "EUR", "date": "20-09-2021", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:500", "balance": 5120, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "26-53-2023"}, {"id": 85, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 4199, "currency": "EUR", "date": "28-11-2021", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:200", "balance": 7757, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "13-58-2022"}, {"id": 86, "transactionId": "**********************", "username": "<PERSON><PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 5288, "currency": "USD", "date": "21-30-2021", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:400", "balance": 9624, "group": "ECN-EUR", "platform": "MT5", "createdAt": "01-16-2021"}, {"id": 87, "transactionId": "**************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 9867, "currency": "USD", "date": "24-57-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:200", "balance": 8871, "group": "Professional-USD", "platform": "MT4", "createdAt": "22-02-2023"}, {"id": 88, "transactionId": "***************************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 7794, "currency": "USD", "date": "17-11-2023", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 0.5, "label": "N/A", "leverage": "1:200", "balance": 6777, "group": "ECN-EUR", "platform": "MT4", "createdAt": "03-39-2023"}, {"id": 89, "transactionId": "************************", "username": "<PERSON><PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 6547, "currency": "JOD", "date": "11-30-2023", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:500", "balance": 4387, "group": "ECN-EUR", "platform": "MT5", "createdAt": "23-21-2022"}, {"id": 90, "transactionId": "********************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 3553, "currency": "JOD", "date": "25-11-2022", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 3.2, "label": "N/A", "leverage": "1:400", "balance": 9355, "group": "ECN-EUR", "platform": "MT5", "createdAt": "23-08-2021"}, {"id": 91, "transactionId": "****************************", "username": "Aquila Anand", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 9323, "currency": "JOD", "date": "14-43-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:400", "balance": 4826, "group": "Professional-USD", "platform": "MT4", "createdAt": "29-22-2022"}, {"id": 92, "transactionId": "******************************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 5454, "currency": "USD", "date": "18-45-2023", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 9.8, "label": "N/A", "leverage": "1:400", "balance": 1668, "group": "ECN-EUR", "platform": "MT5", "createdAt": "24-31-2022"}, {"id": 93, "transactionId": "************************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 3046, "currency": "EUR", "date": "31-46-2022", "transactionStatus": "approved", "transactionType": "in", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:400", "balance": 5039, "group": "Professional-USD", "platform": "MT5", "createdAt": "06-41-2022"}, {"id": 94, "transactionId": "************************", "username": "<PERSON><PERSON><PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 5467, "currency": "USD", "date": "11-06-2022", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 100.3, "label": "N/A", "leverage": "1:800", "balance": 6922, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "12-27-2021"}, {"id": 95, "transactionId": "****************************", "username": "<PERSON>", "paymentMethod": "Bank Transfer", "accountNumber": **********, "amount": 8738, "currency": "EUR", "date": "18-57-2023", "transactionStatus": "pending", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:800", "balance": 8917, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "03-44-2023"}, {"id": 96, "transactionId": "********************", "username": "<PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 1234, "currency": "USD", "date": "24-52-2022", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 22.78, "label": "N/A", "leverage": "1:200", "balance": 5333, "group": "Professional-USD", "platform": "MT4", "createdAt": "26-11-2022"}, {"id": 97, "transactionId": "*********************", "username": "<PERSON>", "paymentMethod": "Ethereum", "accountNumber": **********, "amount": 6632, "currency": "EUR", "date": "18-37-2021", "transactionStatus": "pending", "transactionType": "out", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:400", "balance": 1515, "group": "Professional-USD", "platform": "MT5", "createdAt": "21-50-2022"}, {"id": 98, "transactionId": "****************************", "username": "<PERSON><PERSON>", "paymentMethod": "Visa", "accountNumber": **********, "amount": 9710, "currency": "JOD", "date": "23-31-2023", "transactionStatus": "rejected", "transactionType": "in", "exchangeRate": 85.95, "label": "N/A", "leverage": "1:200", "balance": 1096, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "28-19-2022"}, {"id": 99, "transactionId": "******************", "username": "<PERSON>", "paymentMethod": "<PERSON><PERSON>", "accountNumber": **********, "amount": 9482, "currency": "EUR", "date": "23-20-2023", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:100", "balance": 2741, "group": "Profissional-EUR", "platform": "MT5", "createdAt": "25-04-2023"}, {"id": 100, "transactionId": "****************************", "username": "<PERSON>", "paymentMethod": "Bitcoin", "accountNumber": **********, "amount": 972, "currency": "USD", "date": "19-23-2021", "transactionStatus": "approved", "transactionType": "out", "exchangeRate": 1.2, "label": "N/A", "leverage": "1:200", "balance": 1243, "group": "ECN-EUR", "platform": "MT4", "createdAt": "06-42-2023"}]