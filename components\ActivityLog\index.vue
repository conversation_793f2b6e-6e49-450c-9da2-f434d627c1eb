<template>
	<div>
		<slot v-bind="{ open, close }">
			<v-btn icon @click="open">
				<v-icon>mdi-history</v-icon>
			</v-btn>
		</slot>
		<v-navigation-drawer v-model="drawerModel" app :right="!isRTL" temporary stateless :width="800">
			<v-toolbar flat>
				<v-toolbar-title> Activity Log </v-toolbar-title>
				<v-spacer />
				<v-btn icon @click="close">
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</v-toolbar>
			<v-timeline dense clipped>
				<v-skeleton-loader :loading="$fetchState.pending" type="list-item@4">
					<div>
						<activity-log-item v-for="(item, i) in log" :key="`item-${i}`" :item="item" />
					</div>
				</v-skeleton-loader>
			</v-timeline>
		</v-navigation-drawer>
	</div>
</template>

<script>
export default {
	props: {
		model: {
			type: String,
			required: true,
		},
		id: {
			type: [String, Number],
			// required: true,
			default: "",
		},
	},
	data() {
		return {
			drawerModel: false,
			log: [],
		}
	},
	async fetch() {
		if (!this.drawerModel) { return }

		await this.$axios.$get(`/v1/admin/activity-log/model/${this.model}/${this.id}`).then((resp) => {
			this.log = resp
		})
	},
	methods: {
		open() {
			this.drawerModel = true
			this.$fetch()
		},
		close() {
			this.drawerModel = false
			this.log = []
		},
	},
}
</script>
