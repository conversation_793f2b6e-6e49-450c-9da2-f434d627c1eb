<template>
	<v-timeline-item fill-dot class="white--text mb-12" large>
		<template #icon>
			<template v-if="item.causer">
				<v-avatar v-if="item.causer.avatar" size="36">
					<v-img :src="item.causer.avatar" />
				</v-avatar>
				<span v-else>{{ item.causer.fullName | initials }}</span>
			</template>
		</template>
		<v-card outlined>
			<v-card-title>
				<activity-log-action :value="item.event" /> <v-spacer />
				<span class="text-subtitle-2">{{ item.createdAt | smartDateTime }}</span>
			</v-card-title>
			<template v-if="item.properties && item.properties.old">
				<v-list-group>
					<template #activator>
						<v-list-item-content>
							<v-list-item-title>
								<activity-log-item-body :item="item" />
							</v-list-item-title>
						</v-list-item-content>
					</template>

					<v-list-item v-for="(val, property, p) in item.properties.old" :key="p">
						<div class="me-4 text-subtitle-1 text-capitalize" style="width: 30%">
							<b>{{ property }}</b>
							:
						</div>
						<v-list-item-content>
							<v-list-item-title class="d-flex align-center">
								<strike class="line-clamp-1">
{{ val }}
</strike>
								<v-spacer />
								<v-icon>mdi-arrow-right</v-icon>
								<v-spacer />
								<span class="line-clamp-1">{{ item.properties?.attributes?.[property] }}</span>
							</v-list-item-title>
						</v-list-item-content>
					</v-list-item>
				</v-list-group>
			</template>
			<v-card-text v-else>
				<activity-log-item-body :item="item" />
			</v-card-text>
		</v-card>
	</v-timeline-item>
</template>

<script>
export default {
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
}
</script>
