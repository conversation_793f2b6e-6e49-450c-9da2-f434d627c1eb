<template>
	<v-card class="product-card d-flex flex-column mb-2 fill-height" :class="{ 'has-discount': hasDiscount }" flat hover
		:max-width="maxWidth" :elevation="isHovered ? 5 : 0" v-on="$listeners">
		<div>
			<div>
				<v-sheet color="accent13" class="image-wrapper">
					<v-carousel v-model="carouselModel" :height="150" :show-arrows="isHovered" :cycle="isHovered"
						:hide-delimiters="covers.length <= 1 || !isHovered" hide-delimiter-background>
						<template #next="{ on }">
							<v-btn v-show="covers.length > 1" icon v-on="on" @click.prevent>
								<v-icon>mdi-chevron-right</v-icon>
							</v-btn>
						</template>
						<template #prev="{ on }">
							<v-btn v-show="covers.length > 1" icon v-on="on" @click.prevent>
								<v-icon>mdi-chevron-left</v-icon>
							</v-btn>
						</template>
						<v-carousel-item v-for="(image, i) in covers" :key="i" :eager="isHovered">
							<img :src="image.preview" height="150">
						</v-carousel-item>
					</v-carousel>

					<div class="overlay">
						<div class="c-row">
							<div>
								<v-sheet v-if="hasDiscount" shaped dark class="discount-label">
									<div class="me-1">
										<span class="number enlarge px"> {{ discount }} % </span>
										<small>
											<count-down :date="discountExpiresIn" />
										</small>
										<!-- {{ $t('off') }} -->
									</div>
								</v-sheet>
								<!--<p-new v-if="data.isNew" class="mt-1" />-->
							</div>

							<div class="d-flex flex-column">
								<v-spacer />
							</div>
						</div>
						<v-spacer />
						<div class="c-row justify-end align-end" />
					</div>
					<v-progress-linear :value="100" height="2" />
				</v-sheet>
			</div>

			<v-card-text>
				<div class="d-flex align-center pt-1">
					<!-- <div v-if="item.category" class="text--secondary text-subtitle-2">
							{{ item.category.name }}
						</div> -->
					<v-spacer />
					<v-rating :value="item.avgRate || 3" background-color="default" dense readonly small color="warning"
						length="5" />
				</div>
				<div class="text-subtitle-1 pb-1">
					{{ item.name }} {{ item.variance?.name }}
					<!-- <small>{{ item.type }}</small> -->
				</div>
				<div v-if="hasStock" class="price-wrapper">
					<span class="price--text">
						<small>{{ priceObject.symbol }}</small>
						<span class="font-weight-bold text-body-1 value">{{ priceObject.value }}</span></span>
					<span v-if="hasDiscount" class="text-body-2 text--secondary">
						<span class="text-decoration-line-through me-1">{{ priceBeforeOffer }} </span>
						<span class=""> Save {{ discountAmount | money }}</span>
					</span>
				</div>
				<div v-else class="warning--text">
					{{ $t("product.out-of-stock") }}
				</div>
			</v-card-text>
		</div>
	</v-card>
</template>

<script>
import { mapGetters } from "vuex"

export default {
	props: {
		item: {
			type: [Object, Number],
			default: () => ({}),
		},
		loading: {
			type: Boolean,
			default: false,
		},
		categoryId: {
			type: Number,
			default: 0,
		},
		width: {
			type: [Number, String],
			default: "100%",
		},
		maxWidth: {
			type: [Number, String],
			default: "100%",
		},
	},

	data() {
		return {
			carouselModel: 0,
			isHovered: false,
			isWishlisting: false,
			isComparing: false,
		}
	},

	computed: {
		...mapGetters({
			isAllowedToAddToComparison: "comparison/isAllowedToAdd",
		}),
		varianceSlug() {
			return this.item?.slug
		},
		stock() {
			return this.item?.stock
		},
		hasStock() {
			return !!this.stock && this.stock.quantity > 0
		},

		hasDiscount() {
			return this.isOffer
		},

		discount() {
			if (this.hasDiscount) {
				return 100 - Math.round((this.stock.price.value / this.stock.priceBeforeOffer) * 100)
			}
			return 0
		},
		discountAmount() {
			if (this.hasDiscount) {
				return {
					value: this.stock.priceBeforeOffer?.value - this.stock.price.value,
					currency: this.stock.price.currency,
				}
			}
			return 0
		},
		discountExpiresIn() {
			if (this.hasDiscount) {
				return this.stock.unPublishedAt
			}
			return 0
		},

		price() {
			if (this.hasStock) {
				return this.stock.price
			}
			return null
		},
		priceObject() {
			if (this.hasStock) {
				const formatted = this.$options.filters.money(this.stock.price)
				const [symbol, value] = formatted.split(" ") // not normal space
				return { symbol, value }
			}

			return null
		},

		priceBeforeOffer() {
			return this.stock.priceBeforeOffer
		},

		isTypeSimple() {
			return this.item.type === "simple"
		},
		isTypeBundle() {
			return this.item.type === "bundle"
		},
		isTypeAlternative() {
			return this.item.type === "alternative"
		},

		covers() {
			return [...(this.item.media?.cover || []), ...(this.item.product?.media?.cover || [])]
		},
	},
	methods: {},
}
</script>

<style lang="scss">
@import "~/assets/variables.scss";

.product-card {
	.image-wrapper {
		position: relative;
		overflow: hidden;

		img {
			//	filter: $product-drop-shadow;
			padding: 16px 0;
			margin-inline: auto;
			display: block;
		}

		.overlay {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			flex-direction: column;

			.c-row {
				padding: 8px;
				display: flex;
				justify-content: space-between;
				flex: content;
			}
		}
	}

	.price-wrapper {
		.value {
			font-size: 20px !important;
		}
	}

	&.has-discount {
		.image-wrapper {
			img {
				padding-top: 36px;
			}
		}
	}

	.v-carousel__controls__item.v-btn {
		color: #000 !important;

		.v-icon {
			width: 18px;
			height: 18px;
			margin: 0 4px !important;
			//opacity: 0.9;
		}
	}
}
</style>
