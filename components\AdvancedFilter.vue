<template>
	<v-navigation-drawer v-model="localModel" fixed temporary v-bind="$attrs" :width="$attrs.width || 300" :right="isRTL">
		<v-form class="fill-height" @submit.prevent="filter">
			<v-card class="fill-height d-flex flex-column" height="100vh">
				<v-card-title>
					<span class="headline">Advanced Filter</span>
				</v-card-title>
				<v-card-text>
					<slot />
				</v-card-text>
				<v-spacer />
				<v-card-actions>
					<v-spacer />
					<v-btn text @click="resetFilter">
Reset
</v-btn>
					<v-btn type="submit" color="primary" text>
Filter
</v-btn>
				</v-card-actions>
			</v-card>
		</v-form>
	</v-navigation-drawer>
</template>

<script>
// import { serialize } from "~/modules/QuerySerialization";
export default {
	props: {
		value: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	computed: {
		localModel: {
			get() {
				return this.value
			},
			set(v) {
				this.$emit("input", v)
			},
		},
	},
	created() {},
	methods: {
		refresh() {
			this.$fetch()
		},
		resetFilter() {
			this.$emit("reset")
		},
		filter() {
			this.localModel = false

			// .then(() => {
			this.$emit("filter")
			// });
		},
	},
}
</script>

<style lang="scss" scoped></style>
