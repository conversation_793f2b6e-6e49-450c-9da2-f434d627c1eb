<template>
	<v-menu>
		<template #activator="{ on, attrs }">
			<v-btn :loading="isLoading" color="secondary" v-bind="{ ...attrs, ...$attrs }" v-on="on">
				<v-icon left>
mdi-robot
</v-icon> Generate description
</v-btn
			>
		</template>
		<v-card>
			<v-list-item dense @click="generate(['ar'])">
Generate in Arabic
</v-list-item>
			<v-list-item dense @click="generate(['en'])">
Generate in English
</v-list-item>
			<v-list-item dense @click="generate(['ar','en'])">
Generate in Arabic & English
</v-list-item>
		</v-card>
	</v-menu>
</template>

<script>
export default {
	props: {
		prompt: {
			type: String,
			required: true,
		},
		body: {
			type: Object,
			default: () => ({}),
		},
		value: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			isLoading: false,
		}
	},
	methods: {
		generateByLanguage(locale) {
			this.isLoading = true
			const localeName = locale === "ar" ? "Arabic" : "English"
            // if any item in the body is object and has the ar or en key, take only the current locale

            const body = Object.fromEntries(
                Object.entries(this.body).map(([key, value]) => {
                    if (typeof value === "object" && value[locale]) {
                        return [key, value[locale]]
                    }
                    return [key, value]
                }),
            )

			return this.$axios
				.$post(`/v1/admin/ai/get/${this.prompt}`, {
					// append: ". The result should be in " + localeName,
					...body,
					locale: localeName,
				})
				.then((res) => {
					this.$emit("input", {
						...this.value,
						[locale]: res.choices[0].text,
					})
				})
				.catch((err) => {
					console.log(err)
				})
		},
		generate(locales) {
			const promises = locales.map(locale => this.generateByLanguage(locale))
			Promise.all(promises).then(() => {
				this.isLoading = false
			})
		},
	},
}
</script>
