<template>
	<v-card :loading="isFetching" min-height="100" flat>
		<!-- show overlay when loading -->
		<vc-label for="">
Attributes
</vc-label>
		<v-alert v-if="!categories.length" type="warning" text>
Please select a category first
</v-alert>
		<template v-else>
			<div v-for="(item, i) in items" :key="item.id">
				<component
					:is="mapComponent(item.insertType)"
					:disabled="isFetching"
					:label="item.name"
					:items="item.options"
					item-text="name"
					item-value="attributeOptionId"
					:value="item.value"
					@input="inputHandler($event, i)"
				/>
			</div>
		</template>
	</v-card>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => [],
		},
		categories: {
			type: [Array],
			default: () => [],
		},
	},
	data() {
		return {
			items: [],
		}
	},
	async fetch() {
		if (this.categories.length) {
			this.items = []
			return await this.$axios
				.$get("/v1/admin/products/attributes", {
					params: {
						categories: this.categories,
					},
				})
				.then((res) => {
					this.items = res
					// set value to items
					this.items.forEach((item) => {
						const value = this.value.find(v => v.attributeId === item.attributeId)
						if (value) {
							item.value = value.attributeOptionId
						}
					})
				})
				.catch((error) => {
					console.log(error)
				})
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
	watch: {
		categories: {
			handler(val) {
				this.$fetch()
			},
			deep: true,
		},
	},
	methods: {
		inputHandler(value, i) {
			this.$set(this.items[i], "value", value)

			// filter out which doesn't have value, then map array to attributeId and attributeOptionId
			const items = this.items
				.filter(item => item.value)
				.map(item => ({
					attributeId: item.attributeId,
					attributeOptionId: item.value,
				}))
			this.localValue = items
		},
		mapComponent(type) {
			switch (type) {
				case "select":
					return "vc-select"
				case "chips":
					return "vc-chips"
				case "radio":
					return "vc-radio"
				case "checkbox":
					return "vc-checkbox"
				case "text":
					return "vc-text-field"
				case "textarea":
					return "vc-textarea"
				case "date":
					return "vc-date-picker"
				case "time":
					return "vc-time-picker"
				case "datetime":
					return "vc-datetime-picker"
				default:
					return "vc-text-field"
			}
		},
	},
}
</script>
