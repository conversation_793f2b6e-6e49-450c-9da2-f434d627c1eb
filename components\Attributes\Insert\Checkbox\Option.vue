<template>
	<translatable v-slot="{ value, update, on, dir }" v-model="localValue.name" v-on="$listeners">
		<vc-text-field hide-details="true" :label="$t('common.option')" :value="value" :dir="dir" :clearable="clearable"
			@input="update" v-on="on" />
	</translatable>
</template>

<script>
export default {
	props: {
		value: {
			type: Object,
			default: () => { },
		},
		clearable: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
}
</script>
