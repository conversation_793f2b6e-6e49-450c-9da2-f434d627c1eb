<template>
	<div>
		<translatable v-slot="{ value, update, on, dir }" v-model="localValue.name" class="mt-4">
			<vc-text-field :value="value" :dir="dir" @input="update">
				<template #append>
					<v-card :color="localValue.hexCode" v-bind="attrs" width="24" height="24" rounded v-on="on" @click="dialogModel = true" />
				</template>
			</vc-text-field>
		</translatable>
		<v-dialog v-model="dialogModel" max-width="295">
			<v-card>
				<!-- <v-card-text> -->
				<v-color-picker
					v-model="localValue.hexCode"
					show-swatches
					:rules="[$rules.required($t('common.hex-code'))]"
					:label="$t('common.hex-code')"
				/>
				<!-- </v-card-text> -->
				<v-card-actions>
					<v-spacer />
					<v-btn color="primary" text @click="dialogModel = false">
						{{ $t("common.close") }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>

<script>
export default {
	props: {
		value: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			dialogModel: false,
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
}
</script>
