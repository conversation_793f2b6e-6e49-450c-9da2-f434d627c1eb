<template>
	<div>
		<vc-autocomplete
			v-model="localValue"
			:label="name"
			:prefix="prefix"
			:suffix="suffix"
			:items="options"
			:multiple="multiple"
			:clearable="clearable"
			item-text="name"
			item-value="attributeOptionId"
			class="flex-grow-1"
			v-on="$listeners"
		>
			<template #selection="{ item }">
				<div class="d-flex align-center">
					<v-sheet :color="item.hexCode" elevation="2" width="20" height="20" class="me-4" />

					<div>{{ item.name }}</div>

					<v-btn v-if="!extra.accentColor" x-small text class="mx-2" @click.stop="accentDialogModel = true"
						>
Add Accent Color
</v-btn
					>
					<template v-else>
						<v-icon left>
mdi-arrow-right
</v-icon>
						<v-sheet :color="extra.accentColor.hexCode" elevation="2" width="20" height="20" class="me-4" />
						<div>{{ extra.accentColor.name[$i18n.locale] }}</div>
						<v-btn small icon @click.stop="accentDialogModel = true">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</template>
				</div>
			</template>
			<template #item="{ item, on, attrs }">
				<v-list-item v-bind="attrs" v-on="on">
					<v-list-item-icon>
						<v-sheet :color="item.hexCode" elevation="2" width="24" height="24" />
					</v-list-item-icon>
					<v-list-item-content>
						<v-list-item-title>{{ item.name }}</v-list-item-title>
					</v-list-item-content>
				</v-list-item>
			</template>
		</vc-autocomplete>
		<v-dialog v-model="accentDialogModel" max-width="300">
			<v-form ref="form">
				<v-card>
					<v-card-title>
						<div>Accent Color</div>
						<v-spacer />
						<v-btn icon @click="accentDialogModel = false">
							<v-icon>mdi-close</v-icon>
						</v-btn>
					</v-card-title>
					<v-card-text>
						<v-color-picker v-model="accentColor.hexCode" />
						<translatable v-slot="{ on, dir, value, update }" v-model="accentColor.name" :rules="[$rules.required('Name')]">
							<vc-text-field label="Name" :dir="dir" :value="value" v-on="on" @input="update" />
</translatable>
					</v-card-text>
					<v-card-actions>
						<v-btn text @click="remove">
Remove Accent
</v-btn>
						<v-spacer />
						<v-btn color="primary" text @click="save">
Save
</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</v-dialog>
	</div>
</template>

<script>
import attributesInsertMixins from "~/mixins/attributesInsert"
export default {
	mixins: [attributesInsertMixins],
	data() {
		return {
			accentColor: {
				hexCode: "",
				name: "",
			},
			accentDialogModel: false,
		}
	},
	methods: {
		save() {
			if (!this.$refs.form.validate()) { return }

			this.$emit("extra", { ...this.extra, accentColor: this.accentColor })
			this.accentDialogModel = false
		},
		remove() {
			this.$emit("extra", { ...this.extra, accentColor: null })
			this.accentDialogModel = false
		},
	},
}
</script>
