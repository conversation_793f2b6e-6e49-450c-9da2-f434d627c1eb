<template>
<vc-text-field v-model.number="localValue.number" type="number" hide-details="true" :label="$t('common.option')" />
</template>

<script>
export default {
    props: {
        value: {
            type: Object,
            default: () => {},
        },
    },
    computed: {
        localValue: {
            get() {
                return this.value
            },
            set(value) {
                this.$emit("input", value)
            },
        },
    },
}
</script>
