<template>
	<vc-autocomplete
		v-model="localValue"
		:label="name"
		:prefix="prefix"
		:suffix="suffix"
		:items="options"
		:multiple="multiple"
		:clearable="clearable"
		item-text="name"
		item-value="attributeOptionId"
		class="flex-grow-1"
		v-on="$listeners"
	>
		<template #selection="{ item }">
			<div>{{ item.number }}</div>
		</template>
		<template #item="{ item, on, attrs }">
			<v-list-item v-bind="attrs" v-on="on">
				<v-list-item-content>
					<v-list-item-title>{{ item.number }}</v-list-item-title>
				</v-list-item-content>
			</v-list-item>
		</template>
	</vc-autocomplete>
</template>

<script>
import attributesInsertMixins from "~/mixins/attributesInsert"
export default {
	mixins: [attributesInsertMixins],

}
</script>
