<template>
	<translatable v-slot="{ value, update, on, dir }" v-model="localValue.name">
		<vc-text-field :label="$t('common.option')" :value="value" :dir="dir" @input="update" v-on="on" />
	</translatable>
</template>

<script>
export default {
	props: {
		value: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
}
</script>
