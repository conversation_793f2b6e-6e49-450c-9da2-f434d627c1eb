<template>
	<v-btn fab class="me-2" color="white" :loading="isLoading" @click="login">
		<v-img max-width="28" src="/images/social-login/facebook.png" />
	</v-btn>
</template>

<script>
/* eslint-disable camelcase */

export default {
	data() {
		return {
			isLoading: false,
		}
	},

	head() {
		return {
			script: [
				{
					hid: "FB-SDK",
					src: "https://connect.facebook.net/en_US/sdk.js",
					crossorigin: "anonymous",
					defer: true,
					async: true,
					callback: () => {
						const self = this

						window.fbAsyncInit = function () {
							window.FB.init({
								appId: self.$config.facebook.appId,
								cookie: true,
								xfbml: true,
								version: "v9.0",
							})
						}
					},
				},
			],
		}
	},
	methods: {
		login() {
			const self = this
			try {
				window.FB.AppEvents.logPageView()
				window.FB.login(
					function (response) {
						console.log(response)
						if (response.status === "connected") {
							this.isLoading = true
							self.$store
								.dispatch(`authExtend/loginWithSocialMedia`, {
									token: response.authResponse.accessToken,
									provider: "facebook",
								})
								.catch((e) => {
									this.genericErrorHandler(e)
								})
								.finally(() => {
									this.isLoading = false
								})
						} else {
							this.isLoading = false
						}
					},
					{ scope: "public_profile,email" },
				)
			} catch (e) {
				self.isLoading = false
				// eslint-disable-next-line
				console.error("FACEBOOK ERROR", e);
			}
		},

		// this.$store.dispatch(`authExtend/loginWithSocialMedia`, { token: response.credential, provider: "facebook" });
	},
}
</script>
