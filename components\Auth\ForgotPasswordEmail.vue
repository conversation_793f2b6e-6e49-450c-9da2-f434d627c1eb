<template>
	<v-form ref="email" @submit.prevent="submit">
		<v-card-title class="text-h5 relative justify-center mb-6 mt-4">
			<v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'send-reset-request')">
				<v-icon>mdi-chevron-left</v-icon>
			</v-btn>
			{{ $t("auth.forgot-password-email") }}
		</v-card-title>

		<v-card-text>
			<div class="mb-2" plain small>
				{{ $t("auth.forgot-email-password-description") }}
			</div>
			<v-text-field
				v-model="form.email"
				:error="!apiError"
				:error-messages="apiError"
				:rules="[$rules.required($t('auth.email')), $rules.email($t('auth.email'))]"
				outlined
				label="Email"
				prepend-inner-icon="mdi-email"
				@change="apiError = null"
			/>
			<v-btn plain small @click="$nuxt.$emit('auth.show', 'login')">
{{ $t("auth.remembered-your-password") }}
</v-btn>
		</v-card-text>

		<v-card-actions class="flex-column px-2 pb-2">
			<v-btn block large color="primary" type="submit" :loading="isLoading">
				{{ $t("auth.reset-my-password") }}
			</v-btn>
		</v-card-actions>
	</v-form>
</template>

<script>
export default {
	data() {
		return {
			form: {
				email: null,
			},
			isLoading: false,
			apiError: [],
		}
	},
	methods: {
		submit() {
			if (!this.$refs.email.validate()) {
				this.scrollToError()
				return
			}
			this.isLoading = true
			this.$store.email = this.form
			this.$store
				.dispatch("authExtend/sendResetPasswordEmail", this.form)
				.then((resp) => {
					this.$nuxt.$emit("auth.show", "verify-token")
				})
				.catch((e) => {
					console.log(e)
					if (e.response.status === 422) {
						this.$toast.error(e.response.data.message, { timeout: 3000 })
						this.apiError = e.response.data.errors.email[0]
					} else {
						this.genericErrorHandler(e, this.$refs.email)
					}
				})
				.finally(() => {
					this.isLoading = false
				})
		},
	},
}
</script>
