<template>
	<v-form ref="phone" @submit.prevent="submit">
		<!-- <v-card-title class="text-h5 my-4 relative justify-center"> </v-card-title> -->

		<v-card-title class="text-h5 relative justify-center mb-6 mt-4">
			<v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'send-reset-request')">
				<v-icon>mdi-chevron-left</v-icon>
			</v-btn>
			{{ $t("auth.forgot-password-phone") }}
		</v-card-title>

		<v-card-text>
			<div class="mb-2" plain small>
				{{ $t("auth.forgot-phone-password-description") }}
			</div>
			<vc-phone
				v-model="form.phone"
				:error="!apiError"
				:error-messages="apiError"
				outlined
				name="phone"
				:rules="[$rules.required($t('auth.phone'))]"
				@change="apiError = null"
			/>
			<v-btn plain small @click="$nuxt.$emit('auth.show', 'login')">
{{ $t("auth.remembered-your-password") }}
</v-btn>
		</v-card-text>

		<v-card-actions class="flex-column px-2 pb-2">
			<v-btn block large color="primary" :loading="isLoading" type="submit">
				{{ $t("auth.reset-my-password") }}
			</v-btn>
		</v-card-actions>
	</v-form>
</template>

<script>
export default {
	data() {
		return {
			form: {
				phone: {
					code: "962",
					number: null,
					iso: "JO",
				},
			},
			apiError: [],
			isLoading: false,
		}
	},
	methods: {
		submit() {
			if (!this.$refs.phone.validate()) {
				this.scrollToError()
				return
			}
			this.isLoading = true
			this.$store
				.dispatch("authExtend/sendResetPasswordPhone", this.form)
				.then(() => {
					this.$toast.success(this.$t("auth.reset-password-phone-sent"))
					this.$nuxt.$emit("auth.show", "verify-code")
				})
				.catch((e) => {
					if (e.response.status === 422) {
						this.$toast.error(e.response.data.message, { timeout: 3000 })
						this.apiError = e.response.data.errors.phone.number[0]
					} else {
						this.genericErrorHandler(e, this.$refs.phone)
					}
				})
				.finally(() => {
					this.isLoading = false
				})
		},
	},
}
</script>
