<template>
	<div>
		<v-btn fab class="me-2" color="white" :loading="isLoading" @click="handleGoogleLogin">
			<v-img max-width="28" src="/images/social-login/google.png" />
		</v-btn>
		<div class="google-wrapper">
			<div ref="btn" />
		</div>
	</div>
</template>

<script>
/* eslint-disable camelcase */
export default {
	data() {
		return {
			isLoading: false,
			options: {
				client_id: this.$config.google.clientId,
				auto_select: false, // optional
				cancel_on_tap_outside: false, // optional
				context: "signup", // optional
				ux_mode: "popup", // optional
			},
			googleButtonWrapper: null,
		}
	},
	// head() {
	// 	return {
	// 		script: [
	// 			{
	// 				hid: "google-sdk",
	// 				src: "https://accounts.google.com/gsi/client",
	// 				crossorigin: "anonymous",
	// 				defer: true,
	// 				async: true,
	// 			},
	// 		],
	// 	};
	// },

	mounted() {
		this.addScript()
		console.log("this.$refs.btn", this.$refs.btn)
	},
	beforeDestroy() {
		window?.google?.accounts?.id?.cancel()
	},
	methods: {
		login() {
			this.$refs.btn.click()
		},
		callbackHandler(response) {
			this.isLoading = true
			this.$store
				.dispatch(`authExtend/loginWithSocialMedia`, { token: response.credential, provider: "google" })
				.catch((e) => {
					this.genericErrorHandler(e)
				})
				.finally(() => {
					this.isLoading = false
				})
		},
		createFakeGoogleWrapper() {
			const googleLoginWrapper = document.createElement("div")
			// Or you can simple hide it in CSS rule for custom-google-button
			googleLoginWrapper.style.display = "none"
			googleLoginWrapper.classList.add("custom-google-button")

			// Add the wrapper to body
			document.body.appendChild(googleLoginWrapper)

			// Use GSI javascript api to render the button inside our wrapper
			// You can ignore the properties because this button will not appear
			window.google.accounts.id.renderButton(googleLoginWrapper, {
				type: "icon",
				width: "200",
			})

			const googleLoginWrapperButton = googleLoginWrapper.querySelector("div[role=button]")

			return {
				click: () => {
					googleLoginWrapperButton.click()
				},
			}
		},
		handleGoogleLogin() {
			// Use wrapper click to prevent Illegal invocation exception
			this.googleButtonWrapper.click()
			// This will open GSI login and after success you will have
			// a response on googleLoginCallback method previously created
		},
		addScript() {
			const googleScript = document.createElement("script")
			googleScript.src = "https://accounts.google.com/gsi/client"
			googleScript.async = true
			googleScript.defer = true
			googleScript.onload = () => {
				window.google.accounts.id.initialize({
					callback: this.callbackHandler,
					...this.options,
				})
				window.google.accounts.id.prompt((notification) => {
					if (notification.isNotDisplayed() || !notification.isDisplayed()) {
						console.log(notification)
					}
				})

				this.googleButtonWrapper = this.createFakeGoogleWrapper()

				// console.log(this.$refs.$el);
				// window.google.accounts.id.renderButton(this.$refs.btn, {
				// 	type: "icon", // standard , icon
				// 	text: "signin_with", // or 'signup_with' | 'continue_with' | 'signin'
				// 	size: "large", // or 'small' | 'medium'
				// 	width: "560", // max width 400
				// 	shape: "circle", // or 'circle'
				// 	// theme: "outline", // or 'filled_black' |  'filled_blue'
				// 	logo_alignment: "left", // or 'center'
				// 	// click_listener: this.onClickHandler,
				// 	callback: (response) => {
				// 		alert("callback");
				// 		this.isLoading = false;
				// 		// Send response to server
				// 		console.log(response);
				// 		this.$store.dispatch(`authExtend/loginWithSocialMedia`, { token: response.credential, provider: "google" });
				// 	},
				// });
			}
			document.head.appendChild(googleScript)
		},
	},
}
</script>
