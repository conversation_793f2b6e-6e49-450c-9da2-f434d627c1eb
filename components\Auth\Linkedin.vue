<template>
	<v-btn fab class="me-2" color="white" :loading="isLoading" @click="login">
		<v-img max-width="28" src="/images/social-login/linkedin.png" />
	</v-btn>
</template>

<script>
export default {
	data() {
		return {
			isLoading: false,
			authWindow: {},
			code: null,
		}
	},

	// created() {
	// 	/* eslint-disable-next-line */
	// 	window.addEventListener("message", (event) => {
	// 		console.log(event);
	// 	});
	// },
	mounted() {
		// check if code is present in url, if so then exchange it for access token
		this.code = this.$route.query.code
		if (this.code) {
			this.exchangeCodeForAccessToken()
			// remove code parameter from url
			this.$router.replace({ query: { ...this.$route.query, code: undefined } })
		}
	},
	methods: {
		login() {
			const clientId = this.$config.linkedin.clientId
			const redirectUri = window.location.origin
			const responseType = "code"
			const scope = "r_liteprofile"
			const url = `https://www.linkedin.com/oauth/v2/authorization?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=${responseType}`
			window.location = url
		},

		createOauthWindow() {
			const clientId = this.$config.linkedin.clientId
			const redirectUri = window.location.origin
			const responseType = "code"
			const scope = "r_liteprofile"
			const width = 500
			const height = 600
			const url = `https://www.linkedin.com/oauth/v2/authorization?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=${responseType}`

			const left = screen.width / 2 - width / 2
			const top = screen.height / 2 - height / 2
			const options = `width=${width},height=${height},left=${left},top=${top}, directories=no, titlebar=no, toolbar=no, location=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no`

			this.authWindow = window.open(url, "_blank", options)
		},
		onMessageReceived(event) {
			console.log(event.data)
			if (event.origin === window.location.origin && !event.data?.source.startsWith("vue-devtools")) {
				// Handle event here (e.g. extract code from URL and exchange for access token)
				console.log(event.data)
				this.authWindow.close()
			} else {
				console.error("Received message from invalid origin:", event)
			}
		},
		exchangeCodeForAccessToken() {
			this.isLoading = true
			this.$store
				.dispatch(`authExtend/loginWithSocialMedia`, { token: this.code, provider: "linkedin" })
				.finally(() => {
					this.isLoading = false
				})
				.catch((e) => {
					this.genericErrorHandler(e)
				})
		},
	},
}
</script>
