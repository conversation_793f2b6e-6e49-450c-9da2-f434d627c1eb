<template>
	<v-form ref="login" @submit.prevent="submit">
		<v-card-title class="text-h5 relative justify-center mb-8 mt-6">
			<div>{{ $t("auth.login") }}</div>
		</v-card-title>
		<v-card-text>
			<v-window v-model="loginTypeModel" class="overflow-visible">
				<v-window-item v-click-outside="{ handler: historyItemOutsideClickHandler }" value="history">
					<v-list-item v-for="(login, l) in loginHistory" :key="'login-' + l"
						v-ripple="!$isEqual(userSelected, login.phone)" :three-line="$isEqual(userSelected, login.phone)" link
						class="all-transition" color="primary" :input-value="$isEqual(userSelected, login.phone)"
						@click.stop="userSelected = login.phone">
						<v-list-item-avatar>
							<v-img v-if="login.avatar" :src="login.avatar" />
							<v-img v-else src="/images/default-avatar.jpg" />
						</v-list-item-avatar>
						<v-list-item-content>
							<v-list-item-title>{{ login.firstName }}</v-list-item-title>
							<v-list-item-subtitle>{{ login.phone | phone }}</v-list-item-subtitle>
							<div v-if="$isEqual(userSelected, login.phone)" class="mt-2">
								<vc-password v-model="form.password" name="password" no-strength
									:rules="[$rules.required($t('auth.password')), $rules.minLength($t('auth.password'), 8)]"
									:label="$t('auth.password')" />
								<!-- <v-checkbox dense class="mt-0" hide-details label="Keep me logged in on this device"></v-checkbox> -->
							</div>
						</v-list-item-content>
						<v-list-item-action class="align-center">
							<v-menu offset-y>
								<template #activator="{ on }">
									<v-btn icon v-on="on" @click.stop="">
										<v-icon>mdi-dots-vertical</v-icon>
									</v-btn>
								</template>
								<v-card>
									<v-list-item link @click="deleteHistoryItem(l)">
										<v-list-item-icon><v-icon>mdi-delete</v-icon></v-list-item-icon>
										<v-list-item-title>{{ $t("auth.forget-this") }}</v-list-item-title>
									</v-list-item>
								</v-card>
							</v-menu>
							<v-btn v-if="$isEqual(userSelected, login.phone)" class="mt-2" icon type="submit"
								:loading="isLoggingIn">
								<v-icon>mdi-chevron-right</v-icon>
							</v-btn>
						</v-list-item-action>
					</v-list-item>
				</v-window-item>
				<v-window-item value="new">
					<vc-phone v-model="form.phone" outlined name="phone" :label="$t('auth.phone')"
						:rules="[$rules.required($t('auth.phone'))]" style="direction:ltr" />

					<vc-password v-model="form.password" name="password" no-strength
						:rules="[$rules.required($t('auth.password')), $rules.minLength($t('auth.password'), 8)]"
						:label="$t('auth.password')">
						<template #counter>
							<v-btn class="mt-2" plain small @click="$nuxt.$emit('auth.show', 'send-reset-request')">
								{{
									$t("auth.forgot-your-password")
								}}
							</v-btn>
						</template>
					</vc-password>

					<!-- <v-checkbox dense class="mt-0" hide-details label="Keep me logged in on this device"></v-checkbox> -->
				</v-window-item>
			</v-window>
		</v-card-text>

		<v-card-actions class="flex-column px-4 pb-4">
			<v-btn v-show="loginTypeModel === 'new'" :loading="isLoggingIn" type="submit" large block color="primary">
				{{
					$t("auth.login")
				}}
			</v-btn>
			<!-- <v-btn
				v-show="loginTypeModel === 'forgot-password'"
				block
				large
				color="primary"
				:loading="isSendingResetPassword"
				@click="sendResetPasswordEmail"
			>
				{{ $t("login.reset-my-password") }}
			</v-btn> -->

			<v-btn v-show="loginTypeModel === 'history'" text large color="primary" @click="loginTypeModel = 'new'">
				<v-icon left>
					mdi-plus
				</v-icon> {{ $t("login.login-with-different-account") }}
			</v-btn>
			<!-- <v-subheader class="my-2">{{ $t("auth.or-login-using") }}</v-subheader> -->

			<!-- <div class="d-flex">
				<auth-google />
				<auth-facebook />
				<auth-microsoft />
				<auth-linkedin />
			</div> -->
			<v-subheader class="mt-4">
				{{ $t("auth.you-don-t-have-an-account") }}
			</v-subheader>
			<v-btn color="primary" text @click="$nuxt.$emit('auth.show', 'register')">
				{{ $t("auth.create-account") }}
			</v-btn>

			<auth-footer @show-disclaimer="$nuxt.$emit('auth.show', 'disclaimer')" />
		</v-card-actions>
	</v-form>
</template>

<script>
export default {
	data() {
		return {
			isLoggingIn: false,
			loginTypeModel: "new",
			userSelected: false,
			form: {
				phone: {
					iso: "JO",
					number: "",
					code: "962",
				},
				password: null,
			},

			socialMediaAuth: [
				{ name: "google", src: "/images/social-login/google.png", isLoading: false },
				{ name: "facebook", src: "/images/social-login/facebook.png", isLoading: false },
			],
		}
	},
	computed: {
		loginHistory() {
			return this.storage?.loginHistory
		},
	},
	watch: {
		"storage.loginHistory.length": {
			handler(val) {
				if (this.loginTypeModel === "history" && !val) {
					this.loginTypeModel = "new"
				}
			},
		},

		loginTypeModel(currentValue, oldValue) {
			if (oldValue === "reset-password") {
				this.$router.replace(this.localePath({ name: "login" }))
			}
		},
	},
	created() {
		if (this.storage?.loginHistory?.length) {
			this.loginTypeModel = "history"
		}
	},
	methods: {
		loginWithMedia(media) {
			media.isLoading = true
			// this.$auth.loginWith("SocialMedia", { provider: media.name }).finally(() => {
			// 	media.isLoading = false;
			// });
			return this.$store.dispatch(`authExtend/loginWithSocialMedia`, media.name).finally(() => {
				media.isLoading = false
				media.isLoggingIn = true
			})
		},
		submit() {
			const savePhone = this.form.phone.number
			console.log("this.form.phone", this.form.phone)
			if (this.loginTypeModel === "history") {
				this.form.phone = this.userSelected
			}

			if (!this.$refs.login.validate()) {
				this.scrollToError()
				return
			}

			this.isLoggingIn = true
			//! TODO: remove this line after testing
			// if (this.form.phone.code[0] !== "+") {
			// 	this.form.phone.code = +this.form.phone.code;
			// }
			this.$store
				.dispatch("authExtend/login", this.form)
				.then(() => {
					this.$nuxt.$emit("auth.success")
					this.$router.push(this.localePath({ name: "index" }))
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.login)
				})
				.finally(() => {
					this.isLoggingIn = false
				})
			this.form.phone = {
				iso: "JO",
				number: savePhone,
				code: "962",
			}
		},
		historyItemOutsideClickHandler(e) {
			this.userSelected = false
			return true
		},
		deleteHistoryItem(item) {
			this.storage.loginHistory.splice(this.storage.loginHistory.indexOf(item), 1)
		},
		sendNewPassword() { },
	},
}
</script>
