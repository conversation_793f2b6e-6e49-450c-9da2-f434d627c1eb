<template>
	<v-form ref="register" @submit.prevent="submit">
		<v-card-title class="text-h5 relative justify-center">
			<v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'login')">
<v-icon>mdi-chevron-left</v-icon>
</v-btn>
			<div>{{ $t("auth.register-account") }}</div>
		</v-card-title>

		<v-card-text>
			<div class="d-flex">
				<vc-text-field
					v-model="form.firstName"
					name="firstName"
					:label="$t('auth.first-name')"
					:rules="[$rules.required($t('auth.first-name'))]"
				/>

				<v-spacer class="pl-5" />

				<vc-text-field
					v-model="form.lastName"
					name="lastName"
					:label="$t('auth.last-name')"
					:rules="[$rules.required($t('auth.last-name'))]"
				/>
			</div>

			<!-- hidden for now, resgitering must be using phone only -->
			<!-- <vc-text-field
				v-model="form.email"
				name="email"
				:label="$t('auth.email')"
				:rules="[$rules.required($t('auth.email')), $rules.email($t('auth.email'))]"
			></vc-text-field> -->

			<vc-phone
				v-model="form.phone"
				name="phone"
				:label="$t('auth.mobile-number')"
				:rules="[$rules.required($t('auth.mobile-number'))]"
			/>

			<vc-password
				v-model="form.password"
				name="password"
				:rules="[$rules.required($t('auth.password')), $rules.minLength($t('auth.password'), 8)]"
				:label="$t('auth.password')"
			/>

			<v-checkbox
				v-model="form.confirmation"
				class="text-justify"
				:rules="[$rules.required($t('auth.agreement'))]"
				name="confirm_one"
			>
				<template #label>
					<i18n path="auth.reg-confirmations" tag="div" class="text-caption">
						<a place="agreement" target="_blank" :href="agreementLink" @click.stop>
							{{ $t("auth.agreement") }}
						</a>
					</i18n>
				</template>
			</v-checkbox>
		</v-card-text>

		<v-card-actions class="flex-column px-4 pb-4">
			<v-btn :loading="isLoading" type="submit" large block color="primary">
{{ $t("auth.create-account") }}
</v-btn>

			<div class="d-flex align-baseline mt-2">
				<v-subheader>{{ $t("auth.remembered-your-account") }}</v-subheader>
				<v-btn color="primary" text @click="$nuxt.$emit('auth.show', 'login')">
{{ $t("auth.login") }}
</v-btn>
			</div>
		</v-card-actions>
	</v-form>
</template>

<script>
export default {
	data() {
		return {
			countries: [],
			form: {
				password: null,
				phone: { code: "962", number: null, iso: "jo" },
				firstName: null,
				lastName: null,
				email: null,
				confirmation: null,
			},
			isLoading: false,
		}
	},
	computed: {
		agreementLink() {
			return this.$store.state.agreementLink
		},
	},
	methods: {
		submit() {
			if (!this.$refs.register.validate()) {
				this.scrollToError(this.$refs.login)
				return
			}
			this.isLoading = true
			this.$axios
				.$post(`/v1/auth/register`, this.form)
				.then((resp) => {
					console.log("login then")
					console.log("RESP", resp)
					try {
						return this.$store
							.dispatch("authExtend/login", this.form)
							.then(() => {
								this.$nuxt.$emit("auth.success")
							})
							.catch((e) => {
								this.genericErrorHandler(e, this.$refs.login)
							})
					} catch (error) {
						console.log("Catch ERROR 1", error)
					}
				})
				.catch((e) => {
					console.log("login catch")
					console.log("CATCH ERROR 2", e)
					this.genericErrorHandler(e, this.$refs.register)
				})
				.finally(() => {
					this.isLoading = false
				})
		},
	},
}
</script>
