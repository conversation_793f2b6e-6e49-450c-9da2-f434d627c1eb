<template>
	<v-form>
		<v-card-title class="text-h5 my-4 relative justify-center">
			<v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'login')">
<v-icon>mdi-chevron-left</v-icon>
</v-btn>
			<div>{{ $t("auth.forgot-password") }}</div>
		</v-card-title>
		<v-card-text>
			<div class="mt-2" plain small>
				{{ $t("auth.forgot-password-description") }}
				Trouble logging in? No worries! Enter your email or phone number, and we'll guide you through resetting your password.
			</div>
			<v-list>
				<v-list-item-group color="primary">
					<!-- Hiding reset password by email for now til later stages -->
					<v-list-item @click="$nuxt.$emit('auth.show', 'forgot-password-email')">
						<v-list-item-icon>
							<v-icon>mdi-email</v-icon>
						</v-list-item-icon>

						<v-list-item-content>
							<v-list-item-title>{{ $t("auth.forgot-email-description") }}</v-list-item-title>
						</v-list-item-content>
					</v-list-item>

					<v-list-item @click="$nuxt.$emit('auth.show', 'forgot-password-phone')">
						<v-list-item-icon>
							<v-icon>mdi-phone</v-icon>
						</v-list-item-icon>

						<v-list-item-content>
							<v-list-item-title>{{ $t("auth.forgot-phone-description") }}</v-list-item-title>
						</v-list-item-content>
					</v-list-item>
				</v-list-item-group>
			</v-list>
		</v-card-text>
	</v-form>
</template>
