<template>
	<v-form ref="otp" @submit.prevent="submit">
		<v-card-title class="text-h5 my-4 relative justify-center">
			<v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'forgot-password-phone')"
				>
<v-icon>mdi-chevron-left</v-icon>
</v-btn
			>
		</v-card-title>

		<v-card-title class="text-h5 relative justify-center mb-8 mt-6">
			{{ $t("auth.otp-verification-code") }}
		</v-card-title>

		<v-card-text>
			<v-text-field
				v-model="form.code"
				:rules="[$rules.required($t('common.verification-code'))]"
				outlined
				label="Code"
				prepend-inner-icon="mdi-code"
			/>
		</v-card-text>

		<v-card-actions class="flex-column px-4 pb-4">
			<v-btn type="submit" block large color="primary" :loading="loading">
				{{ $t("auth.send") }}
			</v-btn>
		</v-card-actions>
	</v-form>
</template>

<script>
export default {
	data() {
		return {
			form: {
				code: null,
				userId: null,
			},
			loading: false,
		}
	},
	methods: {
		submit() {
			this.form.userId = this.$store.state.authExtend.userId
			if (!this.$refs.otp.validate()) {
				this.scrollToError()
				return
			}
			this.loading = true
			this.$store
				.dispatch("authExtend/verifyCode", this.form)
				.then(() => {
					this.$nuxt.$emit("auth.show", "verify-reset-password-code")
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.otp)
				})
				.finally(() => {
					this.loading = false
				})
		},
	},
}
</script>
