<template>
	<v-form ref="password" @submit.prevent="submit">
		<v-card-title class="text-h5 my-4 relative justify-center">
			<v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'send-reset-request')">
				<v-icon>mdi-chevron-left</v-icon>
			</v-btn>
		</v-card-title>
		<v-card-text>
			<vc-password
				v-model="form.password"
				outlined
				:rules="[$rules.required($t('auth.password')), $rules.minLength($t('auth.password'), 8)]"
				:label="$t('auth.password')"
			/>
			<vc-password
				v-model="form.passwordConfirmation"
				outlined
				:label="$t('auth.confirm-password')"
				no-strength
				:rules="[$rules.match('Confirm Password', 'password', form.password)]"
			/>
			<v-btn plain small @click="$nuxt.$emit('auth.show', 'login')">
{{ $t("auth.remembered-your-password") }}
</v-btn>
		</v-card-text>

		<v-card-actions>
			<v-btn block large color="primary" type="submit" :loading="isSendingNewPassword">
				{{ $t("auth.reset-my-password") }}
			</v-btn>
		</v-card-actions>
	</v-form>
</template>

<script>
import { mapGetters } from "vuex"

export default {
	data() {
		return {
			form: {
				password: "",
				passwordConfirmation: "",
				code: "",
				userId: "",
			},
			isSendingNewPassword: false,
		}
	},

	computed: {
		...mapGetters({
			code: "authExtend/code",
			userId: "authExtend/userId",
		}),
	},

	methods: {
		submit() {
			if (!this.$refs.password.validate()) {
				this.scrollToError()
				return
			}
			this.isSendingNewPassword = true
			this.form.code = this.code
			this.form.userId = this.userId
			this.$store
				.dispatch("authExtend/sendNewPasswordOfCode", { form: this.form })
				.then(() => {
					this.$toast.success(this.$t("auth.password-has-been-updated-successfully"))
					this.$nuxt.$emit("auth.show", "login")
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.password)
				})
				.finally(() => {
					this.isSendingNewPassword = false
				})
		},
	},
}
</script>
