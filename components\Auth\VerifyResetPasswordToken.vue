<template>
<v-form ref="password" @submit.prevent="submit">
    <v-card-title class="text-h5 my-4 relative justify-center">
        <v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'send-reset-request')">
            <v-icon>mdi-chevron-left</v-icon>
        </v-btn>
    </v-card-title>
    <v-card-text>
        <vc-password v-model="form.password" outlined label="Password" :rules="[$rules.required($t('auth.password')), $rules.minLength($t('auth.password'), 8)]" />
        <vc-password v-model="form.passwordConfirmation" outlined label="Confirm Password" no-strength :rules="[$rules.match('Confirm Password', 'password', form.password)]" />

        <v-btn plain small @click="$nuxt.$emit('auth.show', 'login')">
{{ $t("auth.remembered-your-password") }}
</v-btn>
    </v-card-text>
    <v-card-actions>
        <v-btn block large color="primary" type="submit" :loading="isSendingNewPassword">
            {{ $t("auth.reset-my-password") }}
        </v-btn>
    </v-card-actions>
</v-form>
</template>

<script>
import {
    mapGetters,
} from "vuex"
export default {
    data() {
        return {
            form: {
                password: "",
                passwordConfirmation: "",
            },
            isSendingNewPassword: false,
        }
    },

    computed: {
        ...mapGetters({
            token: "authExtend/token",
        }),
    },

    methods: {
        submit() {
            if (!this.$refs.password.validate()) {
                this.scrollToError()
                return
            }

            this.isSendingNewPassword = true
            this.$store
                .dispatch("authExtend/sendNewPasswordOfToken", {
                    form: this.form,
                    token: this.token,
                })
                .then(() => {
                    this.$toast.success(this.$t("auth.password-has-been-updated-successfully"))
                    this.$nuxt.$emit("auth.show", "login")
                })
                .catch((e) => {
                    this.genericErrorHandler(e, this.$refs.password)
                })
                .finally(() => {
                    this.isSendingNewPassword = false
                })
        },
    },
}
</script>
