<template>
	<v-form ref="token" class="py-8 px-6 text-center mx-auto ma-4 text-h5 my-4 relative justify-center" elevation="12"
		max-width="400" width="100%" @submit.prevent="submit">
		<!-- //	otp vuetify 1 -->
		<!-- <v-card class="py-8 px-6 text-center mx-auto ma-4 text-h5 my-4 relative justify-center" elevation="12" max-width="400" width="100%"> -->
		<v-btn icon class="absolute left ms-2" @click="$nuxt.$emit('auth.show', 'forgot-password-email')">
			<v-icon>mdi-chevron-left</v-icon>
		</v-btn>

		<h3 class="text-h6 mb-4">
			{{ $t("auth.enter-verification-code") }}
		</h3>

		<div class="text-body-2">
			{{ $t("auth.otp-verification-code") }}
		</div>

		<v-sheet color="surface">
			<v-otp-input v-model="otp" color="primary" focus-all focused variant="solo" :length="4" />
		</v-sheet>

		<v-btn type="submit" class="my-4" color="primary" height="40" width="70%">
			{{ $t("auth.change-your-password") }}
		</v-btn>

		<div class="text-caption">
			Didn't receive the code?
			<a href="#" :class="{ disabled: isDisabled }"
				:style="{ pointerEvents: isDisabled ? 'none' : 'auto', color: isDisabled ? 'gray' : 'blue' }"
				@click.prevent="resend">
				{{ $t("auth.resend") }} <span v-if="isDisabled">({{ countdown }}s)</span>
			</a>
		</div>
		<!-- </v-card> -->

		<!-- <v-card-actions class="flex-column px-4 pb-4">
			<v-btn type="submit" block large color="primary" :loading="loading" :disabled="!otp.length || loading">
				{{ $t("auth.change-your-password") }}
			</v-btn>
		</v-card-actions> -->
	</v-form>
</template>

<script>
import { mapGetters } from "vuex"

export default {
	data() {
		return {
			loading: false,
			otp: "",
			isDisabled: false,
			countdown: 30,
		}
	},

	computed: {
		...mapGetters({
			token: "authExtend/token",
		}),
	},
	mounted() { },

	methods: {
		submit() {
			if (!this.$refs.token.validate()) {
				this.scrollToError()
				return
			}
			this.loading = true
			this.visible = false
			// this.$store.dispatch("updateCode", this.otp);
			this.$store
				.dispatch("authExtend/verifyCode", { code: this.otp })
				.then(() => {
					this.$nuxt.$emit("auth.show", "verify-reset-password-code")
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.token)
				})
				.finally(() => {
					this.loading = false
				})
		},
		async resend() {
			if (this.isDisabled) {
				return ""
			}

			// Disable the button and start the timer
			this.isDisabled = true
			this.countdown = 30
			const timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					clearInterval(timer)
					this.isDisabled = false
				}
			}, 1000)

			// Dispatch the Vuex action to send the reset password email
			try {
				await this.$store.dispatch("authExtend/sendResetPasswordEmail", this.$store.state.authExtend.email)
				this.$toast.success(this.$t("auth.emailSentSuccess"))
			} catch (error) {
				console.error("Failed to send reset email:", error)
				this.$toast.error(this.$t("auth.email-sent-srror"), { timeout: 5000, showClose: true })
			}
		},
	},
}
</script>
