<template>
	<v-window v-model="localWindow">
		<v-window-item value="login">
			<auth-login />
		</v-window-item>
		<v-window-item value="register">
			<auth-register />
		</v-window-item>
		<v-window-item value="loading">
			<logging-in />
		</v-window-item>
		<v-window-item value="send-reset-request">
			<auth-send-reset-request />
		</v-window-item>
		<v-window-item value="verify-code">
			<auth-verify-code />
		</v-window-item>
		<v-window-item value="verify-token">
			<auth-verify-token />
		</v-window-item>
		<v-window-item value="forgot-password-email">
			<auth-forgot-password-email />
		</v-window-item>
		<v-window-item value="forgot-password-phone">
			<auth-forgot-password-phone />
		</v-window-item>
		<v-window-item value="verify-reset-password-code">
			<auth-verify-reset-password-code />
		</v-window-item>
		<v-window-item value="verify-reset-password-token">
			<auth-verify-reset-password-token />
		</v-window-item>
		<v-window-item value="privacy-policy">
			<v-card-text class="text-body-2">
				<v-sheet max-height="calc(100vh - 220px)" class="overflow-y-auto pe-2">
					<!-- <div v-html="$store.state.currentEntity.translatable_configs.site_disclosure"></div> -->
					privacy
				</v-sheet>
			</v-card-text>

			<v-card-actions>
				<v-spacer />
				<v-btn color="primary" text @click="setWindow('login')">
{{ $t("common.back") }}
</v-btn>
			</v-card-actions>
		</v-window-item>
		<v-window-item value="terms-of-use">
			<v-card-text class="text-body-2">
				<v-sheet max-height="calc(100vh - 220px)" class="overflow-y-auto pe-2">
					<!-- <div v-html="$store.state.currentEntity.translatable_configs.site_disclosure"></div> -->
					terms
				</v-sheet>
			</v-card-text>

			<v-card-actions>
				<v-spacer />
				<v-btn color="primary" text @click="setWindow('login')">
{{ $t("common.back") }}
</v-btn>
			</v-card-actions>
		</v-window-item>
	</v-window>
</template>

<script>
export default {
	props: {
		window: {
			type: String,
			default: "main",
		},
	},
	data() {
		return {
			/// windowModel: "main",
		}
	},
	computed: {
		localWindow: {
			get() {
				return this.window
			},
			set(v) {
				this.$emit("update:window", v)
			},
		},
	},
	methods: {
		setWindow(v) {
			this.localWindow = v
		},
	},
}
</script>
