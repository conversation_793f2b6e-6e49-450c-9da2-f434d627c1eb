<template>
	<v-avatar v-bind="$attrs" left>
		<v-menu offset-y v-bind="menuProps">
			<template #activator="{ on, attrs }">
				<slot v-bind="{ on, attrs, item }">
					<v-img v-if="src" :src="imgURL" cover v-bind="attrs" :class="{ pointer: !!$scopedSlots.menu }" v-on="on" />
					<v-img
						v-else
						src="/images/default-avatar.jpg"
						v-bind="attrs"
						:class="{ pointer: !!$scopedSlots.menu }"
						v-on="on"
					/>
				</slot>
			</template>
			<v-card><slot name="menu" v-bind="{ item }" /></v-card>
		</v-menu>
	</v-avatar>
</template>

<script>
export default {
	inheritAttrs: false,
	props: {
		src: {
			type: [String, Array],
			default: null,
		},
		item: {
			type: Object,
			default: () => {
				return {}
			},
		},
		menuProps: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		imgURL() {
			return this.item.profile_pic || this.src
		},
	},
}
</script>
