<template>
	<div class="avatars">
		<avatar
			v-for="(item, i) in avatars"
			:key="i"
			v-tooltip="item.first_name + ' ' + item.last_name"
			:src="item.profile_pic"
			:item="item"
		>
			<template #menu="props">
				<slot v-bind="props" name="menu" />
			</template>
		</avatar>

		<avatar v-if="shouldShowMore" v-tooltip="`${value.length - limitAsNumber} more`">
			<v-img src="/images/default-avatar.jpg">
				<v-sheet
					dark
					height="100%"
					width="100%"
					color="rgba(0,0,0,0.7)"
					class="d-flex align-center justify-center pointer"
					@click="$emit('click:read-more')"
				>
					<span class="text-subtitle-2">+{{ value.length - limitAsNumber }}</span>
				</v-sheet>
			</v-img>
		</avatar>
	</div>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => {
				return []
			},
		},
		limit: {
			type: [Number, String],
			default: 3,
		},
	},
	computed: {
		limitAsNumber() {
			return Number(this.limit)
		},
		avatars() {
			return this.value.slice(0, this.limitAsNumber)
		},
		shouldShowMore() {
			return this.value.length > this.limitAsNumber
		},
	},
}
</script>
<style lang="scss">
@import "~/assets/mixins";
.avatars {
	display: flex;
	align-items: center;
}
.avatars > div {
	margin-right: -12px;
	@include mode("border", "2px solid #fff", "2px solid #272727");
}
</style>
