<template>
<v-skeleton-loader type="card" :loading="loading" class="fill-height" :width="width">
    <v-card class="mx-auto" max-width="300">
        <v-img height="150">
            <iframe :src="item.map" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" />
        </v-img>

        <v-card-title>
            <div>
                <h4> {{ item.name }}</h4>
            </div>
</v-card-title>

        <v-card-text>
<div class="d-flex">
                <h3 class="mb-1 mr-2">
Phone
</h3>
                <span> {{ item.phone.number }}</span>
            </div>

            <div>
                <h3 class="my-2">
Working Hours
</h3>
                <span> {{ item.description }}</span>
            </div>
        </v-card-text>
    </v-card>
</v-skeleton-loader>
</template>

<script>
export default {
    props: {
        item: {
            type: [Object],
            default: () => ({}),
        },
        loading: {
            type: Boolean,
            default: false,
        },
        width: {
            type: Number,
            default: 300,
        },
    },

}
</script>
