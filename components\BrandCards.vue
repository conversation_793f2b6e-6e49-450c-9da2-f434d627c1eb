<template>
	<div class="d-flex align-center fill-width my-2 ">
		<v-skeleton-loader :loading="loading" type="image" height="70">
			<v-slide-group class="categories-group">
				<v-card v-for="(brand, index) in data" :key="index" flat :to="decodeURIComponent(
					localePath({
						name: 'category',
						params: {
							slugs: `${stringSlug}/${brand.slug}`,
						},
					})
				)" color="var(--v-accent13-base)" height="67" class="d-flex align-center px-3 me-3 fill-height">
					<nuxt-picture provider="s3" loading="lazy" sizes="xs:55px" contain width="60" class="img  pt-2 pb-2"
						quality="90" :src="brand.media?.logo?.[0]?.src || '/images/product-placeholder.webp'" />
					<div class="text text-subtitle-1 pl-2">
						{{ brand.name }}
					</div>
				</v-card>
			</v-slide-group>
		</v-skeleton-loader>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: () => { },
		},
		loading: {
			type: Boolean,
			default: false,
		},
		category: {
			type: Object,
			default: () => { },
		},
	},
	computed: {

		stringSlug() {
			const slugs = this.$route.params.slugs
			const categorySlug = this.category.slug
			const index = slugs.split('/').indexOf(categorySlug)
			const newSlugs = slugs.split('/').slice(0, index + 1).join('/')
			return newSlugs
		},
	},
}
</script>

<style lang="scss">
.categories-group {

	.v-slide-group__prev,
	.v-slide-group__next {
		position: absolute;
		top: 0;
		bottom: 0;
		background: #fff;
		z-index: 1;
	}

	.v-slide-group__next {
		right: 0;

		&.v-slide-group__next--disabled {
			display: none;
		}
	}

	.v-slide-group__prev {
		left: 0;

		&.v-slide-group__prev--disabled {
			display: none;
		}
	}

	.v-slide-group__wrapper {
		contain: unset;
		overflow: visible;
	}
}
</style>
