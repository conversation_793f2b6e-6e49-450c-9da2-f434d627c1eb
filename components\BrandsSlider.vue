<template>
	<div class="my-4">
		<div class="d-flex justify-space-between">
			<h2 class="font-weight-light">
				<template v-if="toRoute">
					<nuxt-link :to="toRoute" class="text-decoration-none inherit--text">
						{{ title }}
					</nuxt-link>
				</template>
				<template v-else>
					{{ title }}
				</template>
			</h2>
		</div>
		<v-slide-group class="hide-disabled-arrows brand-slider">
			<v-slide-item v-for="(item, i) in items" :key="i" class="me-3 pa-2 brand-slide">
				<div class="brand-slide-contentx">
					<v-card :to="localePath({ name: 'brands', params: { slug: item.meta.slug } })">
						<v-card-text class="d-flex justify-center">
							<v-img contain eager :aspect-ratio="4 / 4" :src="$img(item.meta.media.logo[0].preview, {
								format: 'webp',
								fit: 'contain',
								background: 'transparent',
								sizes: 'xs: 97px',
								quality: 95,
							}, { provider: 's3' })" class="brand-logo" />
						</v-card-text>
						<!-- <div class="brand-name">
							{{ item.text }}
						</div> -->
					</v-card>
				</div>
			</v-slide-item>
		</v-slide-group>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			required: true,
		},
		items: {
			type: Array,
			required: true,
		},
	},
	computed: {
		toRoute() {
			return this.to ? this.to : undefined
		},
	},
}
</script>

<style lang="scss">
.brand-slider {
	.v-slide-group__content {
		padding: 0 20px;
	}
}

.brand-slide {
	width: 145px;
	height: 170px;
	margin: 0 10px;
	transition: transform 0.3s ease-in-out;
	position: relative;
}

.brand-slide-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
	border: 1px solid #ddd;
	border-radius: 10px;
	//box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	background-color: #fff;
	overflow: hidden;
}

.brand-logo {
	width: 90%;
	height: 90%;
	object-fit: contain;
	margin-bottom: 10px;
}

.brand-name {
	font-size: 15px;
	color: #0f0f0f;
	text-align: center;
	display: block;
}

.brand-slide:hover {
	transform: scale(1.1);
	//box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

.brand-slide:hover .brand-overlay {
	display: flex;
}
</style>
<style>
@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}
</style>
