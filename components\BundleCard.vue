<template>
	<v-card outlined>
		<v-list-item two-line link>
			<v-list-item-icon><v-img width="100" :aspect-ratio="4 / 3" :src="computedSrc" /></v-list-item-icon>
			<v-list-item-content>
				<v-list-item-title>{{ item.name }}</v-list-item-title>
				<v-list-item-subtitle>{{ item.description }}</v-list-item-subtitle>
				<div class="d-flex align-center">
					<slot name="actions" />
				</div>
			</v-list-item-content>
		</v-list-item>
	</v-card>
</template>

<script>
export default {
	props: {
		item: {
			type: Object,
			default: () => { },
		},
	},
	computed: {
		computedSrc() {
			return this.item?.media?.cover?.[0]?.src || this.item?.media?.gallery?.[0]?.src || "/images/product-placeholder.webp"
		},
	},
}
</script>
