<script>
export default {
	name: "Component<PERSON>ache",
	serverCacheKey: (props) => {
		let name = props.name
		if (name === false) {
			return false
		}
		if (Array.isArray(props.name)) {
			name = props.name.join("-")
		}
		return `html-${name}__${props.timeout}`
	},
	props: {
		name: {
			type: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>],
			required: true,
		},
		timeout: {
			type: [String, Number],
			required: true,
		},
	},
	render() {
		return this.$slots.default
	},
}
</script>
