<template>
	<v-navigation-drawer v-model="drawerModel" :permanent="permanentDrawer" width="450" app hide-overlay temporary
		:left="isRTL" :right="!isRTL">
		<template #prepend>
			<v-subheader class="text-h5 d-flex align-center">
				<v-icon left>
					mdi-cart
				</v-icon>
				<span>{{ $t("common.my-cart") }}</span>
				<v-spacer />
				<v-btn icon @click="close">
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</v-subheader>
		</template>
		<v-lazy>
			<v-list>
				<v-list-item v-for="(item, itemIndex) in $store.state.cart.items" :key="`item-${itemIndex}`" three-line
					link>
					<v-list-item-icon>
						<thumbnail :width="80" :aspect-ratio="4 / 3" :src="item?.variance?.media?.gallery?.[0]?.src ||
							item?.variance?.media?.cover?.[0]?.src ||
							item?.media?.cover?.[0]?.src ||
							'/images/product-placeholder.webp'" />
					</v-list-item-icon>
					<v-list-item-content>
						<v-list-item-title>{{ item?.name | trans }}</v-list-item-title>
						<v-list-item-subtitle class="d-flex">
							SKU #{{ item?.variance?.SKU }}
							<v-spacer />
							<span
								v-if="(!item?.stock && item?.type == 'bundle') || (!item?.variance?.stock && ['simple', 'alternative'].includes(item?.type))"
								class="warning--text">
								{{ $t("product.out-of-stock") }}
							</span>
						</v-list-item-subtitle>
						<div class="d-flex align-center">
							<div class="text-caption me-4">
								{{ $t("common.quantity") }}
							</div>
							<v-btn elevation="3" width="24" height="24" fab @click.prevent="decrementQuantity(item)">
								<v-icon small>
									mdi-minus
								</v-icon>
							</v-btn>
							<div class="text-caption mx-4">
								{{ item?.quantity }}
							</div>
							<v-btn elevation="3" width="24" height="24" fab @click="incrementQuantity(item)">
								<v-icon small>
									mdi-plus
								</v-icon>
							</v-btn>
						</div>
					</v-list-item-content>

					<v-list-item-action>
						<v-btn icon @click.prevent="removeWithConfirm(item)">
							<v-icon>mdi-delete</v-icon>
						</v-btn>
					</v-list-item-action>
				</v-list-item>

				<v-sheet v-if="!$store.state.cart?.items?.length" class="px-4 mt-12">
					<v-row>
						<v-col cols="12" class="text-center muted-4">
							<v-icon size="64">
								mdi-cart-outline
							</v-icon>
							<div class="text-h6 py-4">
								{{ $t("common.your-cart-is-empty") }}
							</div>
						</v-col>
					</v-row>
				</v-sheet>
			</v-list>
		</v-lazy>
		<template #append>
			<v-sheet v-if="$store.getters['cart/total'].value" class="px-4">
				<v-row>
					<v-col cols="6" class="text-h6">
						{{ $t("common.total") }}
					</v-col>
					<v-col cols="6" class="text-h6 text-end">
						{{ $store.getters["cart/total"] | money }}
					</v-col>
				</v-row>
			</v-sheet>

			<v-card-actions>
				<v-btn color="primary" text @click="close">
					<span class="text-capitalize"> {{ $t("common.continue-shopping") }}</span>
				</v-btn>
				<v-spacer />
				<v-btn :loading="isCheckingOut" color="primary" :disabled="!$store.state.cart?.items?.length"
					@click="checkout">
					<v-icon left>
						mdi-cart
					</v-icon>
					<span class="text-capitalize"> {{ $t("common.checkout") }}</span>
				</v-btn>
			</v-card-actions>
		</template>
	</v-navigation-drawer>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex"
export default {
	data() {
		return {
			...mapState,
			drawerModel: false,
			isCheckingOut: false,
		}
	},
	computed: {
		...mapGetters,
	},
	watch: {
		"$auth.loggedIn": {
			handler(isLoggedIn) {
				this.get()
			},
			immediate: true,
		},
	},
	mounted() {
		this.$nuxt.$on("cart.show", this.open)
		this.$nuxt.$on("cart.hide", this.close)
		this.$nuxt.$on("cart.add", this.add)
		this.$nuxt.$on("cart.remove", this.remove)
	},
	methods: {
		...mapActions({
			add: "cart/add",
			remove: "cart/remove",
			get: "cart/get",
			setQuantity: "cart/setQuantity",
			moveToWishlist: "cart/moveToWishlist",
		}),
		open() {
			this.drawerModel = true
		},
		close() {
			this.drawerModel = false
		},
		removeWithConfirm(item) {
			this.$confirm(this.$t("product.are-you-sure-you-want-to-remove-this-item-from-your-cart"), {
				title: this.$t("product.remove-item"),
				prepend: [
					{
						text: "Just Remove",
						color: "error",
						flat: true,
						value: true,
					},
				],
				btns: [
					{
						text: "Cancel",
						flat: true,
						value: false,
					},
					{
						text: "Move to my wishlist",
						color: "primary",
						flat: true,
						value: "move",
					},
				],
			})
				.then((value) => {
					this.setPermanentDrawer()
					if (value === "move") {
						this.moveToWishlist(item)
					} else if (value) {
						this.remove(item)
					}
				})
				.catch(() => { })
		},
		incrementQuantity(item) {
			item = this.$cloneDeep(item)
			item.quantity++
			this.setQuantity(item)
		},
		decrementQuantity(item) {
			item = this.$cloneDeep(item)
			item.quantity--
			if (item.quantity < 1) {
				this.removeWithConfirm(item)
			} else {
				this.setQuantity(item)
			}
		},
		checkout() {
			this.isCheckingOut = true
			this.$axios
				.$post("/v1/orders/store")
				.then((resp) => {
					const productIds = resp.orderItems.map(item => item.productId)
					this.$fb.track('InitiateCheckout', {
						content_name: 'Checkout', // Name of the event
						content_ids: [productIds], // Array of product IDs
						content_type: 'product', // Type of content
						value: resp.total.value, // Total value of the cart
						currency: resp.total.currency, // Currency
					})
					this.isCheckingOut = false
					this.$router.push(
						this.localePath({
							name: "checkout",
							params: {
								orderId: resp.orderId,
								step: 1,
							},
						}),
					)
					this.close()
				}).catch(this.genericErrorHandler)
				.finally(() => {
					this.isCheckingOut = false
					this.get()
				})
		},
	},
}
</script>
