<template>
	<v-skeleton-loader :loading="$fetchState.pending" type="text@6">
		<v-treeview v-model="localValue" selection-type="independent" :selectable="multiple" :items="items"
			:open-all="false" item-children="meta.children" item-key="value" item-text="text" selected-color="primary"
			dense :active="multiple ? value : [value]" open-on-click :multiple-active="false" active-class="primary--text">
			<template v-if="!multiple" #append="{ item }">
				<v-btn :disabled="value === item.value" text small @click.stop="singleSelect(item)">
					Select
				</v-btn>
			</template>
		</v-treeview>
	</v-skeleton-loader>
</template>

<script>
export default {
	fetchOnServer: false,
	props: {
		value: {
			type: [Array, String, Number],
			default: () => [],
		},
		multiple: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			items: [],
		}
	},
	async fetch() {
		await this.$axios
			.$getOnce(this.api)
			.then((resp) => {
				this.items = resp
				this.$emit("load", resp)
			})
			.catch(this.genericErrorHandler)
	},
	computed: {
		localValue: {
			get() {
				if (this.value && this.value?.length === 0) { return [] }

				if (Array.isArray(this.value)) {
					return this.value
				}
				return [this.value]
			},
			set(value) {
				// console.log("🚀 ~ set ~ value:", value)
				const allValues = new Set(value)
				value.forEach((val) => {
					this.includeParentNodes(this.items, val, allValues)
				})
				this.$emit("input", [...allValues])
			},
		},
		api() {
			return "/v1/lookups/categories"
		},
	},
	methods: {
		singleSelect(item) {
			this.$emit("input", item.value)
		},
		includeParentNodes(items, value, allValues) {
			for (const item of items) {
				if (item.value === value) {
					return true
				}
				if (item.meta && item.meta.children && this.includeParentNodes(item.meta.children, value, allValues)) {
					allValues.add(item.value)
					return true
				}
			}
			return false
		},
	},
}
</script>
