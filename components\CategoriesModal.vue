<template>
	<div>
		<vc-text-field label="Category" :value="selectedItemObject.text">
			<template #append>
				<v-btn height="24" small text @click="dialogModel = true">
Select Category
</v-btn>
			</template>
		</vc-text-field>
		<v-dialog v-model="dialogModel" eager max-width="500">
			<v-card>
				<v-card-title>
					Choose Category
					<v-spacer />
					<v-btn icon @click="closeDialog">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<categories v-model="localValue" @load="loadHandler" @input="closeDialog" />
				</v-card-text>
				<v-card-actions>
					<v-btn text>
Cancel
</v-btn>
					<v-spacer />
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>

<script>
import Categories from "./Categories.vue"
export default {
	components: { Categories },
	props: {
		value: {
			type: [String, Number],
			default: "",
		},
	},
	data() {
		return {
			dialogModel: false,
			items: [],
		}
	},
	computed: {
		selectedItemObject() {
			const searchRecursive = (items, value) => {
				for (const item of items) {
					if (item.value === value) {
						return item
					}
					if (item.meta?.children && item.meta?.children.length) {
						const found = searchRecursive(item.meta.children, value)
						if (found) {
							return found
						}
					}
				}
				return null
			}
			return searchRecursive(this.items, this.value) || {}
		},
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
	methods: {
		closeDialog() {
			this.dialogModel = false
		},
		loadHandler(items) {
			this.$set(this, "items", items)
		},
	},
}
</script>
