<template>
	<v-card color="var(--v-accent13-base)" flat :to="decodeURIComponent(
		localePath({
			name: 'category',
			params: {
				slugs: `${item.slug}`,
			},
		})
	)" class="d-flex align-center px-3 me-3">
		<div v-if="item?.cover" class="me-2">
			<nuxt-picture provider="s3" loading="lazy" sizes="xs:60px" width="60" class="img"
				:src="item?.cover?.src || '/images/product-placeholder.webp'" />
		</div>

		<h2 class="text text-subtitle-1">
			{{ item.name }}
		</h2>
	</v-card>
</template>

<script>
export default {
	props: {
		item: {
			type: Object,
			default: () => { },
		},
	},
}
</script>
