<template>
	<div class="d-flex align-center fill-width my-2 ">
		<v-skeleton-loader :loading="loading" type="image" height="70">
			<v-slide-group class="categories-group">
				<v-card v-for="(category, index) in data.children" :key="index" flat :to="decodeURIComponent(
					localePath({
						name: 'category',
						params: {
							slugs: `${$route.params.slugs}/${category.slug}`,
						},
					})
				)" color="var(--v-accent13-base)" height="66" class="d-flex align-center px-3 me-3 fill-height">
					<div v-if="category.media.cover" class="me-2">
						<nuxt-picture provider="s3" loading="lazy" sizes="xs:60px" width="60" class="img"
							:src="category.media.cover.src" />
					</div>

					<div class="text text-subtitle-1">
						{{ category.name }}
					</div>
				</v-card>
			</v-slide-group>
		</v-skeleton-loader>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: () => { },
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
}
</script>

<style lang="scss">
.categories-group {

	.v-slide-group__prev,
	.v-slide-group__next {
		position: absolute;
		top: 0;
		bottom: 0;
		background: #fff;
		z-index: 1;
	}

	.v-slide-group__next {
		right: 0;

		&.v-slide-group__next--disabled {
			display: none;
		}
	}

	.v-slide-group__prev {
		left: 0;

		&.v-slide-group__prev--disabled {
			display: none;
		}
	}

	.v-slide-group__wrapper {
		contain: unset;
		overflow: visible;
	}
}
</style>
