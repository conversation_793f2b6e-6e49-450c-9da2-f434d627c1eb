<template>
	<div>
		<v-form v-model="isFormValid">
			<v-card v-if="!$auth.loggedIn" flat>
				<v-card-title>
					{{ $t("order.contact") }}
					<v-spacer />
					<v-icon left small>
						mdi-account
					</v-icon><span class="text-subtitle-2">{{ $t("auth.have-an-account") }}</span>
					<v-btn small plain @click="$nuxt.$emit('auth.show', 'login')">
						{{ $t("auth.login") }}
					</v-btn>
				</v-card-title>
			</v-card>
			<v-card flat>
				<v-card-title>
					{{ $t("order.delivery") }}

					<v-spacer />
					<v-btn small plain @click="newAddressModel = true">
						<v-icon left small>
							mdi-plus
						</v-icon>{{ $t("common.add-new-address") }}
					</v-btn>
				</v-card-title>

				<v-card-text>
					<v-skeleton-loader :loading="$fetchState.pending" type="paragraph@4">
						<v-input :rules="[$rules.required('')]" hide-details :value="form.addressId">
							<v-list-item-group v-model="form.addressId" class="flex-grow-1">
								<template v-for="(item, i) in addresses">
									<v-divider v-if="!item" :key="`divider-${i}`" />

									<v-list-item v-else :key="`item-${i}`" :value="item.addressId" active-class="primary--text"
										two-line>
										<template #default="{ active }">
											<v-list-item-action>
												<v-checkbox :input-value="active" color="primary" />
											</v-list-item-action>

											<v-list-item-content>
												<v-list-item-title>
													{{ item.recipientName }}
												</v-list-item-title>
												<v-list-item-subtitle>
													{{ item.city?.name }}, {{ item.district }},
												</v-list-item-subtitle>
												<v-list-item-subtitle>
													{{ item.street }}, {{ $t("order.building") }} {{ item.buildingNumber }} ,
													{{ $t("order.apartment") }}
													{{ item.apartmentNumber }}
												</v-list-item-subtitle>
											</v-list-item-content>
										</template>
									</v-list-item>
								</template>
								<v-alert v-if="!addresses.length" type="warning" outlined>
									{{ $t("common.you-dont-have-any-addresses-yet") }}
									<v-btn small plain @click="newAddressModel = true">
										<v-icon left small>
											mdi-plus
										</v-icon>{{ $t("common.add-new-address") }}
									</v-btn>
								</v-alert>
							</v-list-item-group>
						</v-input>
					</v-skeleton-loader>
				</v-card-text>
			</v-card>
		</v-form>
		<v-card flat>
			<v-spacer />
			<v-card-actions>
				<v-btn large text @click="$emit('cancel')">
					<span class="text-h6">{{ $t("common.cancel") }}</span>
				</v-btn>
				<v-spacer />
				<v-btn :disabled="!isFormValid" large color="primary" min-width="200" :loading="isSettingAddress"
					@click="setAddress">
					<span class="text-h6">{{ $t("common.next") }}</span>
				</v-btn>
			</v-card-actions>
		</v-card>
		<v-dialog v-model="newAddressModel" max-width="800">
			<v-form ref="newAddressForm" v-model="isNewAddressFormValid" @submit.prevent="createNewAddress">
				<v-card>
					<v-card-title>Contact</v-card-title>
					<v-card-text>
						<v-row>
							<v-col cols="12" sm="6" md="6" lg="6">
								<vc-text-field v-model="newAddressForm.recipientName" autocomplete="name"
									:disabled="isSameAccountHolder" :rules="[$rules.required($t('common.recipient-name'))]">
									<template #label>
										<span class="red--text"><strong>* </strong></span>{{ $t('common.recipient-name') }}
									</template>
								</vc-text-field>
							</v-col>
							<v-col cols="12" sm="6" md="6" lg="6">
								<vc-phone v-model="newAddressForm.phone" outlined :disabled="isSameAccountHolder" name="phone"
									autocomplete="phone" :label="`${$t('common.recipient-phone')}`"
									:rules="[$rules.required($t('common.phone'))]" />
							</v-col>
						</v-row>
						<v-row v-if="$auth.loggedIn" no-gutters>
							<v-col cols="12">
								<v-checkbox v-model="isSameAccountHolder" hide-details label="Same Account Holder" class="mt-0"
									@change="sameAccountHolderHandler" />
							</v-col>
						</v-row>
					</v-card-text>
					<v-card-title>
						{{ $t("common.address") }}
					</v-card-title>
					<v-card-text>
						<v-row>
							<v-col cols="12" sm="6" md="6" lg="6">
								<vc-select v-model="newAddressForm.cityId" :rules="[$rules.required($t('common.city-id'))]"
									api="/v1/lookups-website/cities">
									<template #label>
										<span class="red--text"><strong>* </strong></span>{{ $t('common.city') }}
									</template>
								</vc-select>
							</v-col>
							<v-col cols="12" sm="6" md="6" lg="6">
								<vc-text-field v-model="newAddressForm.district" autocomplete="on"
									:rules="[$rules.required($t('common.district'))]">
									<template #label>
										<span class="red--text"><strong>* </strong></span>{{ $t('common.district') }}
									</template>
								</vc-text-field>
							</v-col>
							<v-col cols="12" sm="4" md="4" lg="4">
								<vc-text-field v-model="newAddressForm.street" autocomplete="street-address"
									:label="$t('common.street')" />
							</v-col>
							<v-col cols="12" sm="4" md="4" lg="4">
								<vc-text-field v-model="newAddressForm.apartmentNumber" autocomplete="on"
									:label="$t('common.apartment-number')" />
							</v-col>
							<v-col cols="12" sm="4" md="4" lg="4">
								<vc-text-field v-model="newAddressForm.buildingNumber" autocomplete="on"
									:label="$t('common.building-number')" />
							</v-col>
						</v-row>
					</v-card-text>
					<v-card-actions class="d-flex justify-end">
						<v-spacer />
						<v-btn text @click="newAddressModel = false">
							{{ $t("common.cancel") }}
						</v-btn>
						<v-btn text color="primary" :loading="isLoading" type="submit">
							{{ $t("common.save-address") }}
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</v-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			addresses: [],
			newAddressModel: false,
			newAddressForm: {},
			isSameAccountHolder: false,
			isLoading: false,
			isFormValid: false,
			isNewAddressFormValid: false,
			isSettingAddress: false,
			form: {
				phone: {
					code: "962",
					number: "",
					iso: "JO",
				},
				addressId: null,
			},
		}
	},
	async fetch() {
		await this.$axios.$get("/v1/addresses").then((response) => {
			if (!response.length) { return }
			this.addresses = response
		})
	},
	computed: {
		// canGoNext() {
		// 	return this.isAddressFormValid && this.isContactFormValid;
		// },
	},
	watch: {
		"$auth.loggedIn": {
			handler() {
				this.$fetch()
			},
			// immediate: true,
		},
	},
	methods: {
		createNewAddress() {
			if (!this.$refs.newAddressForm.validate()) { return }

			this.isLoading = true
			this.$axios
				.$post("/v1/addresses", this.newAddressForm)
				.then((response) => {
					this.$refs.newAddressForm.reset()
					this.newAddressModel = false
					this.$fetch()
				})
				.finally(() => {
					this.isLoading = false
				})
		},
		sameAccountHolderHandler(isSameAccountHolder) {
			if (isSameAccountHolder) {
				this.newAddressForm.recipientName = this.$auth.user.fullName
				this.newAddressForm.phone = { ...this.$auth.user.phone }
			} else {
				this.newAddressForm.recipientName = ""
				this.newAddressForm.phone = ""
			}
		},
		setAddress() {
			this.isSettingAddress = true
			const { orderId } = this.$route.params
			this.$axios
				.$post(`/v1/orders/${orderId}/address`, {
					addressId: this.form.addressId,
				})
				.then(() => {
					this.$emit("next")
				})
				.finally(() => {
					this.isSettingAddress = false
					// this.$emit("next"); // TODO: remove this line
				})
		},
	},
}
</script>
