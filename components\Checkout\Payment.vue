<template>
	<div>
		<v-card flat>
			<v-card-title>{{ $t("order.payment") }}</v-card-title>
			<v-card-text v-if="!isOrderCompletedSuccessfully">
				<v-skeleton-loader type="paragraph@3" :loading="$fetchState.pending">
					<v-input :rules="[$rules.required('')]" hide-details :value="form.paymentMethodId">
						<v-list-item-group v-model="form.paymentMethodId" class="flex-grow-1" :mandatory="isMandatory"
							@change="paymentMethodChangeHandler">
							<template v-for="(item, i) in paymentMethods">
								<v-divider v-if="!item" :key="`divider-${i}`" />

								<v-card v-else :key="`divider-${i}`" class="my-3">
									<v-list-item :key="`item-${i}`" :disabled="item.status !== 'active'"
										:value="item.paymentMethodId" active-class="purple--text">
										<template #default="{ active }">
											<v-list-item-action>
												<v-checkbox :disabled="item.status !== 'active'" readonly :ripple="false"
													:input-value="active" color="primary" />
											</v-list-item-action>
											<v-list-item-action v-if="item.media?.logo">
												<div class="me-2">
													<v-img contain max-height="35" :width="64" class="flex-grow-0"
														:src="item.media?.logo" />
												</div>
											</v-list-item-action>
											<v-list-item-content>
												<v-list-item-title class="align-center text-start">
													{{ item.name }}
												</v-list-item-title>
												<!-- for payment Method description use -->
												<v-list-item-subtitle />
											</v-list-item-content>
										</template>
									</v-list-item>

									<client-only>
										<v-slide-y-transition leave-absolute>
											<v-card-text v-if="form.paymentMethodId === item.paymentMethodId">
												<v-skeleton-loader :loading="isGettingPaymentDetails" type="paragraph@2">
													<component :is="`payment-methods-${item.module}`" :item="paymentDetails"
														:order="order" @success="successHandler" @verify="verifyHandler" />
												</v-skeleton-loader>
											</v-card-text>
										</v-slide-y-transition>
									</client-only>
								</v-card>
								<!-- <v-card v-if="form.paymentMethodId === item.paymentMethodId" :key="`details-${i}`" flat> vdsvsdv </v-card> -->
							</template>
							<v-alert v-if="!paymentMethods.length" type="warning" outlined>
								{{ $t("order.there-is-no-any-payment-method-available-for-this-order") }}
							</v-alert>
						</v-list-item-group>
					</v-input>
				</v-skeleton-loader>
			</v-card-text>
			<v-card-text v-else class="d-flex flex-column justify-center align-center">
				<animated-success class="ma-8" />
				<div>
					<v-sheet v-if="redirectCounter > 0" class="success--text text-subtitle-1">
						<!-- {{ $t("order.you-will-be-redirected-to-your-orders-page-in") }} {{ redirectCounter }} {{
							$t("order.seconds") }} -->
						{{ $t("order.thank-you-purchase-message") }}
					</v-sheet>
					<v-sheet class="success--text text-subtitle-1 mt-4">
						<v-btn color="primary" :to="decodeURIComponent(
							localePath({
								name: 'index'
							})
						)">
							<span class="text">{{ $t("common.home") }}</span>
						</v-btn>
						<v-btn v-if="$auth.loggedIn" color="primary" :to="decodeURIComponent(
							localePath({
								name: 'my-orders'
							})
						)">
							<span class="text">{{ $t("order.my-orders") }}</span>
						</v-btn>
					</v-sheet>
				</div>
			</v-card-text>
		</v-card>
		<v-card flat>
			<v-card-actions>
				<v-btn large text @click="$emit('prev')">
					<span class="text-h6">{{ $t("common.back") }}</span>
				</v-btn>
				<v-btn large text @click="$emit('cancel')">
					<span class="text-h6">{{ $t("common.cancel") }}</span>
				</v-btn>
			</v-card-actions>
		</v-card>
	</div>
</template>

<script>
export default {
	props: {
		order: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			paymentMethods: [],
			form: {},
			isGettingPaymentDetails: false,
			isMandatory: false,
			paymentDetails: {},
			isOrderCompletedSuccessfully: false,
			redirectCounter: 4,
		}
	},
	async fetch() {
		if (this.isVerify) {
			const orderId = this.$route.params.orderId
			await this.$axios
				.$get(`/v1/orders/${orderId}/verify`, {
					query: this.$route.query,
				})
				.then((res) => {
					this.successHandler()
				})
				.catch(() => {
					//
				})
		} else {
			await this.$axios.$get(`/v1/orders/${this.$route.params.orderId}/payment-methods`).then((resp) => {
				this.paymentMethods = resp
			})
		}
		this.updateOrderCompletionStatus()
	},
	computed: {
		isVerify() {
			return this.getRouteBaseName() === "payment-verify"
		},
	},
	methods: {
		paymentMethodChangeHandler(paymentMethodId) {
			this.isGettingPaymentDetails = true
			this.isMandatory = true
			const { orderId } = this.$route.params
			this.$axios
				.$post(`/v1/orders/${orderId}/set-payment`, { paymentMethodId })
				.then((resp) => {
					this.isGettingPaymentDetails = false
					this.paymentDetails = resp
					this.$fb.track('PaymentInitiated', {
						value: this.order.total.value,
						currency: this.order.total.num_items,
						num_items: this.order.orderItems.length,
						contents: this.order.orderItems.map(item => ({
							id: item.productId,
							quantity: item.quantity,
							item_price: item.price.value,
						})),
						content_type: 'product',
						shipping_method: this.order.shippingCarrier.name,
						shipping_cost: this.order.shippingPrice.value,
						address: this.order.address.district,
					})
				})
				.finally(() => {
					this.isGettingPaymentDetails = false
				})
		},
		updateOrderCompletionStatus() {
			this.isOrderCompletedSuccessfully = this.order.status === 'received'
			console.log('this.isOrderCompletedSuccessfully', this.isOrderCompletedSuccessfully)
			if (this.isOrderCompletedSuccessfully) {
				this.redirectCounter = 4
				this.$emit("success")
			}
		},
		successHandler() {
			this.isOrderCompletedSuccessfully = true
			this.redirectCounter--
			// setInterval(() => {
			// 	if (this.redirectCounter === 0) {
			// 		// this.$router.push(this.localePath({ name: "my-orders" }))
			// 	}
			// }, 1000)
		},
	},
	watch: {
		'order.paymentMethodId'(newVal) {
			if (newVal !== null && newVal !== undefined) {
				this.form.paymentMethodId = newVal
				this.paymentMethodChangeHandler(newVal)
			}
		},
	},
}
</script>
