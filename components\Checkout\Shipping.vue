<template>
	<div>
		<v-card flat>
			<v-card-title> {{ $t("order.shipping") }} </v-card-title>
			<v-card-text>
				<!-- <div class="d-flex flex-column mb-4 ">
					<div class="text-h1 text-subtitle-1 primary--text mb-2">
						{{ $t("order.dear-customers") }}
					</div>
					<div class="text-h6 text-subtitle-2">
						{{ $t("order.message-shipping-body") }}
					</div>
					<div class="text-h6 text-subtitle-2">
						{{ $t("order.shipping-message-shipping-footer") }}
					</div>
				</div> -->
				<v-skeleton-loader :loading="$fetchState.pending" type="paragraph@3">
					<v-list-item-group v-model="form.shippingCarrierId" class="flex-grow-1">
						<v-list-item v-for="(carrier, m) in shippingCarriers" :key="'shipping-carrier' + m"
							v-slot="{ active }" :value="carrier.shippingCarrierId">
							<v-list-item-action>
								<v-checkbox :input-value="active" class="mt-0 me-2" hide-details />
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title class="d-flex">
									{{ carrier.label }}
									<div class="mx-2 price--text text-subtitle-2">
										{{ carrier.price | money }}
									</div>
								</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
					</v-list-item-group>
				</v-skeleton-loader>
			</v-card-text>
		</v-card>
		<v-card flat>
			<v-card-actions>
				<v-btn large text @click="$emit('prev')">
					<span class="text-h6">{{ $t("common.back") }}</span>
				</v-btn>
				<v-btn large text @click="$emit('cancel')">
					<span class="text-h6">{{ $t("common.cancel") }}</span>
				</v-btn>
				<v-spacer />
				<v-btn large color="primary" :min-width="!isMobile ? 200 : undefined" :loading="isLoading"
					:disabled="!form.shippingCarrierId" @click="setShippingCarrier">
					<span class="text-h6">{{ $t("common.next") }}</span>
				</v-btn>
			</v-card-actions>
		</v-card>
	</div>
</template>

<script>
export default {
	props: {
		order: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			form: {
				shippingCarrierId: null,
			},
			shippingCarriers: [],
			isLoading: false,
		}
	},
	async fetch() {
		const { orderId } = this.$route.params
		await this.$axios.$get(`/v1/orders/${orderId}/shipping-carriers`).then((response) => {
			this.shippingCarriers = response
		})
	},
	watch: {
		"order.shippingCarrierId": {
			handler(val) {
				this.form.shippingCarrierId = val
			},
			immediate: true,
		},
	},
	methods: {
		setShippingCarrier() {
			this.isLoading = true
			this.$axios
				/// orders/:orderId/shipping-carriers
				.$post(`/v1/orders/${this.$route.params.orderId}/shipping-carriers`, this.form)
				.then((response) => {
					this.$emit("next")
				})
				.finally(() => {
					this.isLoading = false
				})
		},
	},
}
</script>
