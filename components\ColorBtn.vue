<template>
	<v-btn v-bind="props" />
</template>

<script>
import VBtn from "vuetify/lib/components/VBtn/VBtn"
export default {
	name: "ColorBtn",
	extends: VBtn,
	props: {
		name: {
			type: String,
			default: null,
		},
		size: {
			type: [String, Number],
			default: 28,
		},
	},
	computed: {
		props() {
			return {
				...this.$props,
				color: this.color,
				height: this.computedSize,
				width: this.computedSize,
				class: ['rounded-circle', 'pa-0'],
				style: {
					minWidth: this.computedSize,
				},
			}
		},
		computedSize() {
			if (typeof this.size === "string") {
				return this.size
			} else {
				return `${this.size}px`
			}
		},
	},
}
</script>

<style></style>
