<template>
	<v-navigation-drawer v-model="localValue" v-bind="$attrs" :right="!isRTL" app temporary stateless :width="width"
		v-on="$listeners">
		<template #prepend>
			<v-card flat fluid class="d-flex flex-column">
				<v-card-title v-if="title">
					<div class="font-weight-regular">
						<slot name="title">
							{{ title }}
						</slot>
					</div>
					<v-spacer />
					<activity-log v-if="model" :id="id" :model="model" />
					<v-btn icon @click="close">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
			</v-card>
		</template>
		<v-form ref="form">
			<v-card flat>
				<v-card-text v-if="loading" key="loading"
					class="flex-grow-1 d-flex align-center justify-center flex-column">
					<v-sheet max-width="400" width="300">
						<v-progress-linear color="primary" indeterminate size="54" />
						<div class="my-2 font-weight-medium text-subtitle-1 text-center">
							Getting Data
						</div>
					</v-sheet>
				</v-card-text>
				<v-card-text v-else-if="error" class="flex-grow-1">
					<v-alert type="error">
						{{ error }}
					</v-alert>
				</v-card-text>
				<v-card-text v-else class="flex-grow-1">
					<v-lazy height="100%">
						<div>
							<slot />
						</div>
					</v-lazy>
				</v-card-text>
			</v-card>
		</v-form>
		<template #append>
			<v-card flat fluid>
				<v-card-actions>
					<slot name="actions">
						<v-btn text @click="close">
							Cancel
						</v-btn>
						<v-spacer />
						<v-btn :loading="isSaving" color="primary" text @click="$emit('save')">
							Save
						</v-btn>
					</slot>
				</v-card-actions>
			</v-card>
		</template>
	</v-navigation-drawer>
</template>

<script>
export default {
	name: "CrudDrawer",

	props: {
		value: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: "New",
		},
		loading: {
			type: Boolean,
			default: false,
		},
		width: {
			type: [Number, String],
			default: 800,
		},
		isSaving: {
			type: Boolean,
			default: false,
		},
		error: {
			type: Boolean,
			default: false,
		},
		model: {
			type: String,
			default: null,
		},
		id: {
			type: [String, Number],
			default: null,
		},
	},
	data() {
		return {}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(v) {
				this.$emit("input", v)
			},
		},
		$form() {
			return this.$refs.form
		},
	},
	methods: {
		close() {
			this.localValue = false
			this.reset()
			this.$emit("close")
		},
		handleStickyChange(isSticky) {
			console.log("isSticky", isSticky)
		},
		reset() {
			this.$form.reset()
		},
		validate() {
			return this.$form.validate()
		},
	},
}
</script>
<style lang="scss">
@import "~/assets/mixins";

.actions {
	left: 0 !important;

	.sticked {
		$dark-color: map-get($material-dark-elevation-colors, "4");
		@include mode("background", "rgb(251, 251, 251)", $dark-color);
		@include mode("background",
			"linear-gradient(180deg, rgba(255, 255, 255, 0.75) 0%, rgba(255, 255, 255, 0.75) 45%)",
			"linear-gradient(180deg, rgba(39, 39, 39, 0.75) 0%, rgba(39, 39, 39, 0.75) 45%)"
		);
		@include mode("border-top", "#e0e0e0 1px solid", "#414141 1px solid");

		backdrop-filter: blur(2px);
		left: 0 !important;
	}
}
</style>
