<template>
	<div class="fixed end" style="bottom: 36px">
		<!-- <fcm ref="fcm" /> -->
		<!-- <v-menu top offset-y :close-on-content-click="false">
			<template #activator="{ on }">
				<v-btn icon color="error" v-on="on"><v-icon>mdi-ladybug</v-icon></v-btn>
			</template>
			<v-card>
				<v-list-item two-lines>
					<v-list-item-content>
						<v-list-item-title>
							<v-select
								single-line
								:items="portalTypeItems"
								hide-details
								dense
								label="Routes File"
								:value="$store.state.debug.portalType"
								:loading="isChangingPortalType"
								@change="changePortalType"
							></v-select>
						</v-list-item-title>
						<v-list-item-subtitle>Change portal routes and type</v-list-item-subtitle>
					</v-list-item-content>
				</v-list-item>
				<v-list-item three-lines>
					<v-list-item-content>
						<v-list-item-title>
							<v-text-field v-model="debugEntityModel" single-line hide-details dense label="Entity Header"></v-text-field>
						</v-list-item-title>
						<v-list-item-subtitle>Set "Entity" Header</v-list-item-subtitle>
					</v-list-item-content>
				</v-list-item>
				<v-list-item-group v-model="debugPermissionsModel">
					<v-list-item :value="true">
						<template #default="{ active }">
							<v-list-item-content>
								<v-list-item-title>Debug Permissions</v-list-item-title>
								<v-list-item-subtitle>Draw border where the content is protected with permissions</v-list-item-subtitle>
							</v-list-item-content>
							<v-list-item-action>
								<v-checkbox :input-value="active"></v-checkbox>
							</v-list-item-action>
						</template>
					</v-list-item>
				</v-list-item-group>

			</v-card>
		</v-menu>
		<v-dialog v-model="permissionDialogModel" max-width="300">
			<v-card>
				<v-card-title>Permissions</v-card-title>
				<v-card-text>
					<v-list dense>
						<v-list-item
							v-for="permission in permissions"
							:key="permission"
							dense
							:color="$gates.hasPermission(permission) ? 'success' : 'error'"
							:input-value="true"
						>
							<v-list-item-action>
								<v-icon v-if="$gates.hasPermission(permission)">mdi-check</v-icon>
								<v-icon v-else>mdi-close</v-icon>
							</v-list-item-action>

							<v-list-item-content>
								<v-list-item-title>{{ permission }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
					</v-list>
				</v-card-text>
			</v-card>
		</v-dialog> -->
	</div>
</template>

<script>
export default {
	data() {
		return {
			permissionDialogModel: false,
			permissions: [],
			portalTypeItems: ["client", "ib", "employee"],
			isChangingPortalType: false,
		}
	},
	computed: {
		debugEntityModel: {
			get() {
				return this.$store.state.debug.entity
			},
			set(value) {
				this.$store.commit("debug/setEntity", value)
			},
		},
		debugPermissionsModel: {
			get() {
				return this.$store.state.debug.permissions
			},
			set(value) {
				this.$store.commit("debug/setDebugPermissions", value || false)
			},
		},
	},
	mounted() {
		window.$nuxt.$on("show-permission", (value) => {
			// alert(value);
			this.permissions = value
			this.permissionDialogModel = true
		})
	},
	methods: {
		debugPermissionsHandler(e) {
			// setTimeout(()=>{
			// 	alert('this.debugPermissions '+e);
			// },100)

			this.storage.debugPermissions = e || false
		},
		changePortalType(type) {
			// console.log("changePortalType", type);
			// this.$config.portalType = type;
			// this.$set(this.$config, "portalType", type);
			this.isChangingPortalType = true
			this.$store.dispatch("debug/changePortalType", type)
		},
	},
}
</script>

<style lang="scss">
.permission {
	position: relative;
	&.no-permission:after {
		content: "";
		text-align: center;
		color: #ffffff !important;
		position: absolute;
		background: url(/images/no-permission.png);
		background-size: cover;
		border: 1px dashed red;
		padding: 4px;
		color: rgba(0, 0, 0, 0.6);
		font-weight: bold;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 8;
		&.no-value-permission {
			background: orange url(/images/no-permission.png);
		}
	}
	&.has-permission:after {
		content: "";
		position: absolute;
		background-color: rgba(0, 255, 0, 0.1);
		border: 1px dashed green;
		padding: 4px;
		color: rgba(0, 0, 0, 0.54);
		font-weight: bold;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 8;
	}
}
</style>
