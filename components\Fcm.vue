<template>
	<div>
		<v-tooltip right transition="slide-x-transition" z-index="8">
			<template #activator="{ on }">
				<v-btn
					v-if="permission === 'denied'"
					elevation="2"
					color="warning"
					fab
					class="fcm-btn"
					width="48"
					height="48"
					:value="dialogModel"
					v-on="on"
					@click="showDialog"
				>
					<v-icon size="30">
mdi-bell
</v-icon>
				</v-btn>
			</template>
			<div>{{ $t("common.notifications-blocked") }}</div>
		</v-tooltip>
		<div>
			<v-lazy :value="dialogModel">
				<v-dialog v-model="dialogModel" max-width="600">
					<v-card>
						<v-card-title>
							<div>{{ $t("common.activate-fcm") }}</div>
							<v-spacer />
							<modal-close-btn v-model="dialogModel"
						/>
</v-card-title>
						<v-card-text>
							{{ $t("common.activate-push-notification-description", { app: $t("app.kulshe") }) }}
						</v-card-text>
						<v-card-text>
							<v-sheet class="d-md-flex">
								<div class="pa-4">
									<ol>
										<li>{{ $t("common.activate-push-notification-step-1") }}</li>
										<li>
											{{ $t("common.activate-push-notification-step-2") }}
										</li>
									</ol>
								</div>
								<img width="70%" contain src="/img/fcm.jpg">
							</v-sheet>
						</v-card-text>
						<v-card-actions>
							<v-spacer />
							<v-btn color="primary" text @click="dialogModel = false">
{{ $t("common.close") }}
</v-btn>
						</v-card-actions>
					</v-card>
				</v-dialog>
			</v-lazy>
			<v-lazy :value="beforeRequestModal">
				<v-dialog v-model="beforeRequestModal" max-width="500">
					<v-card>
						<v-card-title class="mb-4">
							{{ $t("common.keep-connected-notifications", { app: $t("app.kulshe") }) }}
						</v-card-title>
						<v-card-text>
							<v-sheet class="d-md-flex align-md-center">
								<div class="me-4 d-flex justify-center">
									<nuxt-img class="flex-grow-0" width="90" src="/img/bell.jpg" />
								</div>
								<!--
                                <div class="text-body-2">
                                    {{ $t("common.please-click") }} <b>{{ wordLocale("allow") }}</b>
                                    {{ $t("common.to-stay-connected") }}
                                </div> -->
								<div>
									<div class="my-2 text-body-2">
										{{ $t("common.before-fcm-request", { app: $t("app.kulshe") }) }}
									</div>
									<div class="text-body-2">
{{ $t("common.before-fcm-request-2") }}
</div>
								</div>
							</v-sheet>
						</v-card-text>
						<v-card-actions>
							<v-btn text @click="beforeRequestModal = false">
{{ $t("common.cancel") }}
</v-btn>
							<v-spacer />
							<v-btn text color="primary" @click="startRequest">
{{ $t("common.ok") }}
</v-btn>
						</v-card-actions>
					</v-card>
				</v-dialog>
			</v-lazy>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			isPermissionGranted: process.browser && "Notification" in window ? Notification.permission === "granted" : false,
			loading: false,
			idToken: null,
			listenersStarted: false,
			dialogModel: false,
			permission: process.browser && "Notification" in window ? Notification.permission : null,
			beforeRequestModal: false,
			isReadyToTeleport: false,
		}
	},

	mounted() {
		if (window.isFCMInitiated) {
			return
		}
		this.initiate()
		this.$nuxt.$on("request-notifications", () => {
			this.startRequest()
		})

		setTimeout(() => {
			this.isReadyToTeleport = true
		}, 3 * 1000)
		window.isFCMInitiated = true
	},
	methods: {
		async initiate() {
			if (!this.$firebase.messaging.isSupported()) {
				console.warn("Notifications are not supported in this browser")
			}

			this.startListeners()
			if (this.isPermissionGranted) {
				await this.getIdToken()

				if (this.storage.lastFcmToken !== this.idToken) {
					if (this.storage.lastFcmToken) { this.removeToken(this.storage.lastFcmToken) }
					if (this.idToken) { this.saveToken(this.idToken) }
				}
				console.log(this.idToken)
			} else {
				// this.beforeRequestModal = true;
			}
		},
		requestPermission() {
			return new Promise((resolve, reject) => {
				if (this.permission && this.isPermissionGranted) {
					this.$nuxt.$emit("request-notifications-response", this.isPermissionGranted)
				} else {
					try {
						Notification.requestPermission().then((permission) => {
							this.isPermissionGranted = permission === "granted"

							this.permission = permission
							this.$nuxt.$emit("request-notifications-response", this.isPermissionGranted)
							if (this.isPermissionGranted) {
								resolve(permission)
							} else {
								reject(new Error("Permission not granted"))
							}
						})

						// console.log("permission", permission);
					} catch (e) {
						// console.error(e);
						reject(new Error(e))
					}
				}
			})
		},
		async getIdToken() {
			this.loading = true
			let currentToken
			try {
				currentToken = await this.$firebase.messaging.getToken()
				// this.saveToken(currentToken);
			} catch (e) {
				console.error("An error occurred while retrieving token. ", e)
				// retry

				this.idToken = ""
				this.loading = false
				// console.log("retrying...");
				// currentToken = await this.$firebase.messaging.getToken();
			}
			if (currentToken) {
				this.idToken = currentToken
				// console.info("idToken", this.idToken);
			} else {
				// Show permission request.
				console.info("No Instance ID token available. Request permission to generate one.")
				// Show permission UI.
				// updateUIForPushPermissionRequired();
				this.idToken = ""
			}
			// alert(`The id token is: ${this.idToken}`);
			this.loading = false
		},
		startListeners() {
			this.startOnMessageListener()

			this.listenersStarted = true
		},
		startOnMessageListener() {
			this.$firebase.messaging.onMessage(({ notification }) => {
				console.info("Message received. ", notification)

				this.$toast.info(notification.body, { title: notification.title })
			})
		},
		saveToken(token) {
			this.$axios.$put("/fcm", {
				token,
				device: "web",
				operation: "a",
			})
			this.storage.lastFcmToken = token
		},
		removeToken(token) {
			this.$axios.$delete("/fcm", {
				token,
				device: "web",
				operation: "d",
			})
			this.storage.lastFcmToken = null
		},
		showDialog() {
			this.dialogModel = true
		},
		startRequest() {
			console.log("fcm startRequest")
			// this.beforeRequestModal = false;
			this.requestPermission()
				.then(async () => {
					await this.getIdToken()
					if (this.idToken) { this.saveToken(this.idToken) }
				})
				.catch(() => {
					console.log("error while requesting notification permission")
				})
				.finally(() => {
					this.beforeRequestModal = false
				})
		},
	},
}
</script>
<style lang="scss">
.fcm-btn {
	position: fixed;
	right: 50px;
	bottom: 50px;
	z-index: 70;
}
</style>
