<template>
	<div>
		<vc-select
			v-model="attributeId"
			:label="$t('common.attribute')"
			api="/v1/lookups/attributes"
			@input="updateAttributeId"
		/>
		<vc-select
			v-model="componentType"
			:label="$t('common.component-type')"
			:items="componentTypes"
			@input="updateComponentType"
		/>
	</div>
</template>

<script>
export default {
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {}
	},
	computed: {
		attributeId() {
			return this.item.attributeId
		},
		componentType: {
			get() {
				return this.item.componentType || null
			},
			set(value) {
				this.updateComponentType(value)
			},
		},
		componentTypes() {
			return [
				{
					text: this.$t("common.checkbox"),
					value: "checkbox",
				},
				{
					text: this.$t("common.radio"),
					value: "radio",
				},

				{
					text: this.$t("common.checkbox"),
					value: "checkbox",
				},
				{
					text: this.$t("common.chips"),
					value: "chips",
				},
			]
		},
	},
	methods: {
		updateComponentType(value) {
			this.$emit("update", {
				...this.item,
				componentType: value,
			})
		},
		updateAttributeId(value) {
			this.$emit("update", {
				...this.item,
				attributeId: value,
			})
		},
	},
}
</script>
