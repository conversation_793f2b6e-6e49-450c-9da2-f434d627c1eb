<template>
	<div>
		<vc-select
			v-model="componentType"
			:label="$t('common.component-type')"
			:items="componentTypes"
			@input="updateComponentType"
		/>
	</div>
</template>

<script>
export default {
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {}
	},
	computed: {
		componentType: {
			get() {
				return this.item.componentType || null
			},
			set(value) {
				this.updateComponentType(value)
			},
		},
		componentTypes() {
			return [
				{
					text: this.$t("common.radio"),
					value: "radio",
				},

				{
					text: this.$t("common.checkbox"),
					value: "checkbox",
				},
				{
					text: this.$t("common.select"),
					value: "select",
				},
				{
					text: this.$t("common.chips"),
					value: "chips",
				},
				{
					text: this.$t("common.brand"),
					value: "brand",
				},
				{
					text: this.$t("common.range"),
					value: "range",
				},
				{
					text: this.$t("common.text"),
					value: "text",
				},
				{
					text: this.$t("common.number"),
					value: "number",
				},
			]
		},
	},

	methods: {
		updateComponentType(value) {
			this.$emit("update", {
				...this.item,
				componentType: value,
			})
		},
	},
}
</script>
