<template>
	<vc-select
		v-model="componentType"
		:label="$t('common.component-type')"
		:items="componentTypes"
		@input="updateComponentType"
	/>
</template>

<script>
export default {
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {}
	},
	computed: {
		componentType: {
			get() {
				return this.item.componentType || null
			},
			set(value) {
				this.updateComponentType(value)
			},
		},
		componentTypes() {
			return [
				{
					text: this.$t("common.checkbox"),
					value: "checkbox",
				},

				{
					text: this.$t("common.chips"),
					value: "chips",
				},
			]
		},
	},
	methods: {
		updateComponentType(value) {
			this.$emit("update", {
				...this.item,
				componentType: value,
			})
		},
	},
}
</script>
