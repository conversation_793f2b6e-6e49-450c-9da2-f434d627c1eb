<template>
	<div>
		<vc-text-field v-model="max" type="number" :label="$t('common.max')" @input="updateMax" />
		<vc-text-field v-model="min" type="number" :label="$t('common.min')" @input="updateMin" />
		<vc-select
			v-model="componentType"
			:label="$t('common.component-type')"
			:items="componentTypes"
			:rules="[$rules.required($t('common.component-type'))]"
			@input="updateComponentType"
		/>
	</div>
</template>

<script>
export default {
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {}
	},
	computed: {
		max: {
			get() {
				return this.item.config ? this.item.config.max : null
			},
			set(value) {
				this.updateMax(value)
			},
		},
		min: {
			get() {
				return this.item.config ? this.item.config.min : null
			},
			set(value) {
				this.updateMin(value)
			},
		},
		componentType: {
			get() {
				return this.item.componentType || null
			},
			set(value) {
				this.updateComponentType(value)
			},
		},
		componentTypes() {
			return [
				{
					text: this.$t("common.range"),
					value: "range",
				},
			]
		},
	},
	methods: {
		updateMax(value) {
			this.$emit("update", {
				...this.item,
				config: {
					...this.item.config,
					max: value,
				},
			})
		},
		updateMin(value) {
			this.$emit("update", {
				...this.item,
				config: {
					...this.item.config,
					min: value,
				},
			})
		},
		updateComponentType(value) {
			this.$emit("update", {
				...this.item,
				componentType: value,
			})
		},
	},
}
</script>
