<template>
	<v-chip small dark text :color="status.color">
		{{ status.text }}
	</v-chip>
</template>

<script>
export default {
	name: "OrderStatus",
	props: {
		value: {
			type: String,
			required: true,
		},
	},
	data() {
		return {

		}
	},
	computed: {
		status() {
			switch (this.value) {
				case "paid":
					return { color: "green", text: this.$t("common.paid") }
				case "unPaid":
					return { color: "red", text: this.$t("common.unpaid") }
				case "partialPaid":
					return { color: "orange", text: this.$t("common.partial-paid") }
				default:
					return {
						color: "grey",
						text: this.$t("common.unknown"),
					}
			}
		},
	},
}
</script>

<style></style>
