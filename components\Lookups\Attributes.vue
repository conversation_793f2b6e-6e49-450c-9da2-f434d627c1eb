 <template>
	<vc-autocomplete
		v-model="localValue"
		chips
		small-chips
		deletable-chips
		multiple
		outlined
		dense
		:api="api"
		:rules="[$rules.required($t('common.attributes'))]"
		:label="$t('common.attributes')"
		v-bind="$attrs"
		v-on="$listeners"
	/>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => [],
		},
		multiple: {
			type: Boolean,
			default: true,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		api() {
			return "/v1/lookups/attributes"
		},
	},
}
</script>
