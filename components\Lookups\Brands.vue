<template>
	<vc-autocomplete
		v-model="localValue"
		:multiple="multiple"
		:api="api"
		:label="$t('common.brands')"
		v-bind="$attrs"
		chips
		deletable-chips
		small-chips
		v-on="$listeners"
	/>
</template>

<script>
export default {
	props: {
		value: {
			type: [Array, Number],
			default: () => [],
		},
		multiple: {
			type: Boolean,
			default: true,
		},
		categories: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		api() {
			return "/v1/lookups/brands" + (this.categories.length ? "?categories=" + this.categories.join(",") : "")
		},
	},
}
</script>
