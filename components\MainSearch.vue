<template>
	<div class="d-md-flex">
		<div id="main-search-input-wrapper" v-click-outside="() => (searchResultsModel = false)"
			@keyup.enter="enterKeySearch" class="flex-grow-1 relative">
			<v-text-field id="main-search-input" v-model="searchText" filled class="main-search me-1"
				height="calc(var(--main-bar-height) - 16px)" append-icon="mdi mdi-magnify" style="max-width: 832px" dense
				outlined single-line :label="$t('common.what-are-you-looking')" hide-details rounded autocomplete="off"
				clearable @keydown.enter="search" @click:append="search" @input="throttledAutocomplete" @click:clear="clear"
				@focus="searchResultsModel = true">
				<!-- <template #progress>
					<v-progress-linear active query class="mx-16" indeterminate color="primary" size="2"></v-progress-linear>
				</template> -->
				<template #prepend-inner>
					<v-btn id="main-search-categories" plain>
						{{ selectedCategory?.text }} <v-icon right>
							mdi-chevron-down
						</v-icon>
					</v-btn>
					<v-divider vertical inset class="me-2" />
				</template>
			</v-text-field>
		</div>
		<v-menu id="main-search-categories-menu" activator="#main-search-categories" offset-y max-height="500">
			<v-list dense class="categories-list">
				<v-list-item link class="level-1" :input-value="!selectedCategory?.value"
					@click="selectCategory({ text: $t('search.all') })">
					<v-list-item-content>
						<v-list-item-title class="text-capitalize">
							{{ $t('search.all') }}
						</v-list-item-title>
					</v-list-item-content>
				</v-list-item>
				<v-list-item v-for="(L1, L1i) in categories" :key="'L1-' + L1i" link
					:input-value="selectedCategory.value === L1.categoryId" class="level-1" @click="selectCategory(L1)">
					<v-list-item-content>
						<v-list-item-title class="text-capitalize" v-text="L1.name" />
					</v-list-item-content>
				</v-list-item>
			</v-list>
		</v-menu>

		<v-menu id="main-search-results" v-model="searchResultsModel" attach="#main-search-input-wrapper"
			activator="#main-search-input" offset-y min-width="calc(100% - 48px)" max-width="calc(100% - 48px)"
			:nudge-right="!isRTL ? '24px' : undefined" :nudge-left="isRTL ? '-150px' : undefined"
			nudge-top="calc(var(--main-bar-height) - 16px * -1)" max-height="500" open-on-focus open-on-click disable-keys>
			<v-card :loading="isSearching" flat>
				<v-list v-show="results.length" dense>
					<v-list-item v-for="(result, r) in results" :key="'result-' + r"
						@click="itemClickHandler(/*result.name + ' ' + */result.name)">
						<v-sheet width="64">
							<v-img v-if="result.media?.cover.length" contain :aspect-ratio="4 / 3" :src="$img(result.media.cover[0].src, {
								format: 'png',
								fit: 'contain',
								width: 128,
								height: (128 * 16) / 9,
								background: 'transparent',
							})
								" />
							<v-img v-else :aspect-ratio="4 / 3" cover src="/images/product-placeholder.webp" />
						</v-sheet>
						<v-list-item-content>
							<v-list-item-title class="text-capitalize">
								{{ result.name }}
							</v-list-item-title>
							<!-- <v-list-item-subtitle class="text-caption" v-text="result.variance.name" /> -->
						</v-list-item-content>
						<v-list-item-action>
							<v-btn icon :to="localePath({
								name: 'product',
								params: {
									productSlug: result.slug,
									varianceSlug: result.variance.slug,
								},
							})
								" @click.stop="searchResultsModel = false">
								<v-icon class="rtl-rotate">
									mdi-chevron-right
								</v-icon>
							</v-btn>
						</v-list-item-action>
					</v-list-item>
				</v-list>
				<div v-show="!results.length">
					<!-- <div v-if="searchText" class="text-center">
                             <v-img class="mx-auto" eager :aspect-ratio="256/237" max-width="256" src="/images/no-results.jpg"></v-img>
										<v-alert text type="warning" color="primary">There is no results matching your search "{{ searchText }}". please reduce your search text and try again</v-alert>
									</div> -->
					<v-banner v-if="searchText && !isSearching">
						<v-icon slot="icon" color="primary" size="36">
							mdi-emoticon-sad-outline
						</v-icon>
						<div class="text-subtitle-1 muted-2">
							<div>
								{{ $t('search.no-result', { searchText }) }}
							</div>
							<div>{{ $t('search.reduce-search-text') }}</div>
						</div>
						<template #actions>
							<v-btn text color="primary" @click="clear">
								{{ $t('search.clear') }}
							</v-btn>
						</template>
					</v-banner>
					<v-banner v-else-if="!isSearching">
						<v-icon slot="icon" color="primary" size="36">
							mdi-magnify
						</v-icon>
						<div class="text-subtitle-1 muted-2">
							{{ $t('search.start-typing') }}
						</div>
					</v-banner>
				</div>
			</v-card>
		</v-menu>
	</div>
</template>

<script>
import { debounce } from "~/modules/Debounce.js"
export default {
	data() {
		return {
			searchText: this.$route.query.q || "",
			results: [],
			selectedCategory: {
				text: "All",
				value: undefined,
			},
			isSearching: false,
			throttledAutocomplete: null,
			searchResultsModel: false,
		}
	},
	computed: {
		categories() {
			return this.$store.state.categories
		},
	},
	created() {
		this.throttledAutocomplete = debounce(this.autocomplete, 300)
	},
	mounted() {
		if (this.$route.query.q) {
			this.autocomplete(this.$route.query.q)
		}
	},
	methods: {
		autocomplete(q) {
			if (!q) { return }

			this.isSearching = true
			this.$axios
				.$get("/v1/autocomplete", {
					params: {
						q,
						category: this.selectedCategory.value,
					},
				})
				.then((res) => {
					this.results = res
					this.searchResultsModel = true
				})
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isSearching = false
				})
		},
		search() {
			this.$router.push(this.localePath({ name: "search", query: { q: this.searchText, category: this.selectedCategory.value } })).catch(() => { })
		},
		enterKeySearch() {
			this.isSearching = false
			this.results = []
			this.searchResultsModel = true
			this.search()
		},
		selectCategory(c) {
			console.log("🚀 ~ selectCategory ~ c:", c)
			this.selectedCategory = {
				text: c.name,
				value: c.categoryId,
			}
		},
		clear() {
			this.searchText = ""
			this.results = []
			this.$router.push({ ...this.$route, query: { ...this.$route.query, q: null } })
		},
		itemClickHandler(item) {
			this.searchText = item
			this.search()
			this.searchResultsModel = false
		},
	},
}
</script>
