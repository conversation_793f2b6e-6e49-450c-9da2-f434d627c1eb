<template>
	<v-card flat>
		<v-card-title>
			<div>{{ collection }}</div>
			<v-spacer />
			<v-btn text color="primary" @click="addMedia">
				<v-icon left>
					mdi-plus
				</v-icon> Add Media
			</v-btn>
		</v-card-title>
		<v-card-text>
			<v-list v-if="value.length">
				<draggable v-model="localValue">
					<v-list-item v-for="(item, i) in localValue" :key="_uid + '-' + i" active-class="colored-bg"
						:color="statusColor(item)" class="ps-0 mb-2" :class="{ 'muted-2': isInactive(item) }" dense
						:input-value="!isInactive(item)" style="min-height: 88px">
						<v-sheet dark width="100" min-height="88" :color="statusColor(item)"
							class="d-flex align-center justify-center me-4 text-h6 rounded-l-lg flex-shrink-0">
							<v-img :src="item.src" class="rounded-l-lg" width="100" height="88" />
						</v-sheet>

						<div class="flex-grow-1">
							<div class="d-flex">
								<v-list-item-content class="text-body-2">
									<v-list-item-subtitle>
										<div v-if="item.publishedAt">
											<b> Published at:</b>
											<span> {{ item.publishedAt | niceDateTime }}</span>
										</div>
										<span v-else> Published</span>
									</v-list-item-subtitle>
									<v-list-item-subtitle>
										<div v-if="item.unPublishedAt">
											<b>Up to</b> {{ item.unPublishedAt | dateTime }}
										</div>
										<div v-else>
											with No Expiry
										</div>
									</v-list-item-subtitle>
								</v-list-item-content>
								<v-list-item-action>
									<div class="d-flex">
										<v-btn icon @click="removeMedia(i)">
											<v-icon>mdi-delete</v-icon>
										</v-btn>
										<v-btn icon @click="editMedia(item)">
											<v-icon>mdi-pencil</v-icon>
										</v-btn>
										<v-btn icon class="handle">
											<v-icon>mdi-drag</v-icon>
										</v-btn>
									</div>
								</v-list-item-action>
							</div>
							<v-card-subtitle v-if="inactiveReason(item)" class="text-caption fill-width pb-2 pt-0 px-0">
								<v-icon small left>
									mdi-information-outline
								</v-icon> {{ inactiveReason(item) }}
							</v-card-subtitle>
						</div>
					</v-list-item>
				</draggable>
			</v-list>
			<v-alert v-else type="warning" text dense>
				You don't have any media yet
			</v-alert>
		</v-card-text>
		<crud ref="crud" v-slot="{ item, isEdit }" width="400" item-name="media" :api="false" :default="defaultItem"
			@created="saveNew" @updated="saveEdit">
			<vc-upload v-model="uploadModel" :value="item.src" :max="isEdit ? 1 : 100" width="160" />

			<vc-text-field v-model="item.alt" label="Description" :rules="[$rules.maxLength('Description', 140)]"
				counter />

			<publish-field v-model="item.isPublished" :published-at.sync="item.publishedAt"
				:un-published-at.sync="item.unPublishedAt" />
		</crud>
	</v-card>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => [],
		},
		collection: {
			type: String,
			default: "",
		},
	},
	data() {
		return {
			defaultItem: {
				mediaId: null,
				alt: null,
				type: "image",
				publishedAt: null,
				unPublishedAt: null,
				isPublished: true,
			},
			uploadModel: [],
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit("input", val)
			},
		},
	},
	methods: {
		supplierHandler(data, item) {
			item.supplierId = data.value
			item.supplier = { ...data.meta, name: data.text }
		},
		addMedia() {
			this.$refs.crud.new()
		},
		editMedia(item) {
			this.uploadModel = item.src
			this.$refs.crud.edit(item)
		},
		removeMedia(index) {
			this.$confirm("Are you sure you want to delete this media?").then((isAccepted) => {
				if (isAccepted) {
					const media = this.$cloneDeep(this.value)
					console.log(media[index].id)
					if (media[index].id !== undefined && media[index].id !== null) {
						this.$axios
							.$delete(`/v1/admin/media/${media[index].id}`)
							.catch((error) => {
								console.log(error)
							})
					}
					media.splice(index, 1)
					this.localValue = media
				}
			})
		},
		saveNew(item) {
			const images = this.$cloneDeep(this.value)
			const alt = item.alt
			this.uploadModel.forEach((path, index) => {
				const clonedItem = this.$cloneDeep(item)
				clonedItem.src = path
				if (clonedItem.alt) {
					clonedItem.alt = alt + "-" + (index + 1)
				}
				clonedItem.tempId = Math.random()
				images.push(clonedItem)
			})

			this.localValue = images
		},
		saveEdit(item) {
			const images = this.$cloneDeep(this.value)
			const index = images.findIndex(image => image.mediaId === item.mediaId || image.tempId === item.tempId)
			images.splice(index, 1, this.$cloneDeep(item))
			// this.$emit("input", stocks);
			this.localValue = images
		},
		isInactive(item) {
			console.log(item.unPublishedAt, item.unPublishedAt, new Date())
			// the quantity = sold or expired or publishedAt is in the future or not published
			return (
				(item.publishedAt && this.$dayjs().isBefore(this.$dayjs(item.publishedAt))) || // publishedAt is in the future
				(item.unPublishedAt && this.$dayjs().isAfter(this.$dayjs(item.unPublishedAt))) || // unPublishedAt is in the past
				!item.isPublished // not published
			)
		},
		inactiveReason(item) {
			if (item.unPublishedAt && this.$dayjs().isAfter(this.$dayjs(item.unPublishedAt))) {
				return "Expired"
			}
			if (item.publishedAt && this.$dayjs().isBefore(this.$dayjs(item.publishedAt))) {
				return "Will be published at " + this.$options.filters.niceDateTime(item.publishedAt)
			}
			if (!item.isPublished) {
				return "Not published"
			}

			return "nothing"
		},
		statusColor(item) {
			// the quantity = sold or expired or publishedAt is in the future or not published
			if (this.isInactive(item)) {
				return "grey"
			} else {
				return "primary"
			}
		},
	},
}
</script>
