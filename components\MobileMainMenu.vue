<template>
	<v-navigation-drawer v-model="model" :left="isRTL" :right="!isRTL" app temporary width="300">
		<template #prepend>
			<v-list-item v-if="!$auth.loggedIn" @click="$nuxt.$emit('auth.show', 'login')">
				<v-list-item-title>{{ $t('auth.login') }}</v-list-item-title>
				<v-list-item-icon>
					<v-icon>mdi-account</v-icon>
				</v-list-item-icon>
			</v-list-item>
			<template v-else>
				<v-list-item :to="localePath({ name: 'my-account' })">
					<v-list-item-avatar>
						<avatar size="42" eager :src="$auth.user.media?.avatar?.preview" />
					</v-list-item-avatar>
					<v-list-item-content>
						<v-list-item-title>{{ $auth.user.fullName }}</v-list-item-title>
						<v-list-item-subtitle>
							<span dir="ltr">{{ $auth.user.phone | phone }}</span>
						</v-list-item-subtitle>
					</v-list-item-content>
				</v-list-item>

				<v-divider class="my-3 mx-2" />
			</template>
		</template>
		<v-list>
			<v-list-item>
				<v-list-item-content>
					<v-list-item-title>
						<v-badge bordered content="new" color="red" offset-x="18" offset-y="12" class="me-4">
							<v-btn text :to="(localePath({ name: 'offers' }))">
								OFFERS
							</v-btn>
						</v-badge>
					</v-list-item-title>
				</v-list-item-content>
			</v-list-item>
			<v-list-group :value="true" no-action sub-group>
				<template #activator>
					<v-list-item-content>
						<v-list-item-title>{{ $t('common.categories') }}</v-list-item-title>
					</v-list-item-content>
				</template>

				<v-list-item v-for="(category, i) in categories" :key="i" link :to="decodeURIComponent(
					localePath({
						name: 'category',
						params: {
							slugs: `${category.slug}`,
						},
					})
				)
					">
					<v-list-item-title v-text="category.name" />

					<!-- <v-list-item-icon>
						<v-icon v-text="icon"></v-icon>
					</v-list-item-icon> -->
				</v-list-item>
			</v-list-group>
		</v-list>
		<template #append>
			<v-list-item v-if="$auth.loggedIn">
				<v-list-item-icon>
					<v-icon>mdi-logout</v-icon>
				</v-list-item-icon>
				<v-list-item-title @click="logout">
					{{ $t("header.logout") }}
				</v-list-item-title>
			</v-list-item>
			<v-list-item :href="decodeURIComponent(switchLocalePath(otherLocale.code))">
				<v-list-item-icon><v-img class="flex-grow-0" width="32" :src="otherLocale.flag" /></v-list-item-icon>
				<v-list-item-content>
					<v-list-item-title>{{ otherLocale.name }}</v-list-item-title>
				</v-list-item-content>
			</v-list-item>
		</template>
	</v-navigation-drawer>
</template>

<script>
export default {
	props: {
		value: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			setVisitorId: false,
			isInitiating: false,
		}
	},
	computed: {
		model: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		categories() {
			return this.$store.state.categories
		},
		otherLocale() {
			return this.$i18n.locales.find(locale => locale.code !== this.$i18n.locale)
		},
	},
	methods: {
		logout() {
			this.$auth
				.logout()
				.then(() => {
					// check if this route has the middleware auth
					this.$router.push(this.localePath({ name: "index" }))
				})
		},
	},
}
</script>
