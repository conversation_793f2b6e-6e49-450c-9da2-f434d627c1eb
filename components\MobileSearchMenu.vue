<template>
	<v-dialog v-model="model" max-height="100%" fullscreen hide-overlay @keydown="enterKeySearch">
		<v-card tile>
			<v-card-text class="pt-2">
				<v-text-field v-model="searchText" :placeholder="$t('search.start-typing')" :loading="isSearching" clearable
					autofocus dense hide-details solo-inverted flat prepend-inner-icon="mdi-magnify"
					@input="throttledAutocomplete" @click:clear="clear">
					<template #append-outer>
						<v-btn height="38" color="primary" text class="my-n2" @click="close">
							{{ $t('common.cancel') }}
						</v-btn>
					</template>
				</v-text-field>
			</v-card-text>
			<v-list v-show="results.length" dense>
				<v-list-item v-for="(result, r) in results" :key="'result-' + r"
					@click="itemClickHandler(/*result.name + ' ' + */result.name)">
					<v-sheet width="64">
						<v-img v-if="result.media?.cover.length" contain :aspect-ratio="4 / 3" :src="$img(result.media.cover[0].preview, {
							format: 'png',
							fit: 'contain',
							width: 128,
							height: (128 * 16) / 9,
							background: 'transparent',
						})
							" />
						<v-img v-else :aspect-ratio="4 / 3" cover src="/images/product-placeholder.webp" />
					</v-sheet>
					<v-list-item-content>
						<v-list-item-title class="text-capitalize">
							{{ result.name }}
						</v-list-item-title>
						<!-- <v-list-item-subtitle class="text-caption" v-text="result.variance.name" /> -->
					</v-list-item-content>
					<v-list-item-action>
						<v-btn icon :to="localePath({
							name: 'product',
							params: {
								productSlug: result.slug,
								varianceSlug: result.variance.slug,
							},
						})
							" @click.stop="searchResultsModel = false">
							<v-icon class="rtl-rotate">
								mdi-chevron-right
							</v-icon>
						</v-btn>
					</v-list-item-action>
				</v-list-item>
			</v-list>
			<div v-show="!results.length">
				<!-- <div v-if="searchText" class="text-center">
                             <v-img class="mx-auto" eager :aspect-ratio="256/237" max-width="256" src="/images/no-results.jpg"></v-img>
										<v-alert text type="warning" color="primary">There is no results matching your search "{{ searchText }}". please reduce your search text and try again</v-alert>
									</div> -->
				<v-banner v-if="searchText && !isSearching">
					<v-icon slot="icon" color="primary" size="36">
						mdi-emoticon-sad-outline
					</v-icon>
					<div class="text-subtitle-1 muted-2">
						<div>
							{{ $t('search.no-result', { searchText }) }}
						</div>
						<div>{{ $t('search.reduce-search-text') }}</div>
					</div>
					<template #actions>
						<v-btn text color="primary" @click="clear">
							{{ $t('search.clear') }}
						</v-btn>
					</template>
				</v-banner>
				<!-- <v-banner v-else-if="!isSearching">
					<v-icon slot="icon" color="primary" size="36">
						mdi-magnify
					</v-icon>
					<div class="text-subtitle-1 muted-2">
						{{ $t('search.start-typing') }}
					</div>
				</v-banner> -->
			</div>
		</v-card>
	</v-dialog>
</template>

<script>
import { debounce } from "~/modules/Debounce.js"

export default {
	props: {
		value: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			searchText: this.$route.query.q || "",
			results: [],
			selectedCategory: {
				text: "All",
				value: undefined,
			},
			isSearching: false,
			throttledAutocomplete: null,
		}
	},
	computed: {
		model: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},

	},
	created() {
		this.throttledAutocomplete = debounce(this.autocomplete, 300)
	},
	mounted() {
		if (this.$route.query.q) {
			this.autocomplete(this.$route.query.q)
		}
	},
	methods: {
		autocomplete(q) {
			if (!q) { return }

			this.isSearching = true
			this.$axios
				.$get("/v1/autocomplete", {
					params: {
						q,
						category: this.selectedCategory.value,
					},
				})
				.then((res) => {
					this.results = res
					this.searchResultsModel = true
				})
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isSearching = false
				})
		},
		search() {
			this.$router.push(this.localePath({ name: "search", query: { q: this.searchText, category: this.selectedCategory.value } })).catch(() => { })
		},
		enterKeySearch(event) {
			if (event.key === "Enter") {
				this.search()
				this.model = false
				this.isSearching = false
				this.results = []
				this.searchText = ""
				this.searchResultsModel = true
			}
		},
		clear() {
			this.searchText = ""
			this.results = []
			this.$router.push({ ...this.$route, query: { ...this.$route.query, q: null } }).catch(() => { })
		},
		itemClickHandler(item) {
			this.searchText = item
			this.search()
			this.close()
		},
		close() {
			this.model = false
			this.searchText = ""
			this.results = []
		},
	},
}
</script>
<style lang="scss">
// .mobile-search-menu {
// 	margin: 0 !important;
// 	max-height: 100% !important;
// }</style>
