<template>
	<vc-text-field v-model="localValue" hide-spin-buttons type="number" v-bind="$attrs" class="money-field">
		<template #prepend-inner>
			<vc-select
				v-model="localCurrency"
				:disabled="disableCurrency"
				api="/v1/lookups/currencies"
				select-first
				solo
				flat
				:outlined="false"
				hide-details
				height="30"
				style="max-width: 90px; max-height: 30px; overflow: hidden"
			/>
		</template>
	</vc-text-field>
</template>

<script>
export default {
	props: {
		value: {
			type: Object,
			default: null,
		},
		disableCurrency: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value?.value
			},
			set(v) {
				this.localObject = {
					...this.value,
					value: v,
				}
				// this.$emit("input", value); // emit the updated value
			},
		},
		localCurrency: {
			get() {
				return this.value?.currencyId
			},
			set(value) {
				this.localObject = {
					...this.value,
					currencyId: value,
				}
			},
		},
		localObject: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value) // emit the updated value
			},
		},
	},
	mounted() {
		if (!this.localValue || !this.localCurrency) {
			this.localObject = {
				value: null,
				currencyId: 1,
			}
		}
	},
}
</script>

<style lang="scss">
.money-field .v-input__append-inner {
	margin-top: auto !important;
	margin-bottom: auto !important;
}
</style>
