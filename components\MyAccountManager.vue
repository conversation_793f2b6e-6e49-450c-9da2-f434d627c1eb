<template>
	<div id="my-account-manager" class="end">
		<v-menu v-model="menuModel" offset-x transition="slide-x-reverse-transition">
			<template #activator="{ on }">
				<v-btn
					v-tooltip="'My Account Manager'"
					small
					:class="{ 'rounded-r-0': !isRTL, 'rounded-l-0': isRTL }"
					color="info"
					fab
					height="40"
					v-on="on"
				>
					<v-icon>mdi-account-question</v-icon>
					<!-- <div :style="{ maxWidth: menuModel ? '250px' : '0' }">Account Manager</div> -->
				</v-btn>
			</template>
			<!-- <v-card>
				<v-sheet dark color="success">
					<v-card-title>
						<v-icon left>mdi-account-question</v-icon>
						<div>Account Manager</div>
						<v-spacer></v-spacer>
						<v-btn icon @click="menuModel = false">
							<v-icon>mdi-close</v-icon>
						</v-btn>
					</v-card-title>
				</v-sheet>
			</v-card> -->
			<v-card>
				<v-img :aspect-ratio="4 / 3" src="/images/user2.jpeg" />

				<v-card-title>Abdallah Ahmad</v-card-title>
				<v-card-subtitle class="muted-2">
Account Manager
</v-card-subtitle>
				<v-card-text class="pb-0">
					<div class="muted-1 text-caption mb-1">
						<b>Email:</b>
						<span class="text-lowercase"><EMAIL></span>
					</div>
					<div class="muted-1 text-caption mb-1">
						<b>Phone:</b>
						+962-7808 50045
					</div>
					<v-divider class="my-2" />
				</v-card-text>
				<v-card-actions>
					<v-btn text color="success">
						<v-icon>mdi-phone</v-icon>
					</v-btn>
					<v-spacer />
					<v-btn text color="warning">
						<v-icon>mdi-email</v-icon>
					</v-btn>
					<v-spacer />
					<v-btn text color="info">
						<v-icon>mdi-chat</v-icon>
					</v-btn>
				</v-card-actions>

				<!-- <v-sheet width="40%" class="overflow-hidden"> </v-sheet> -->
			</v-card>
		</v-menu>
	</div>
</template>

<script>
export default {
	data() {
		return {
			menuModel: false,
		}
	},
}
</script>
<style lang="scss">
#my-account-manager {
	position: fixed;
	top: calc(var(--headerHeight) + 90px);
	z-index: 6;
}
</style>
