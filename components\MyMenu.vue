<template>
	<div>
		<h3 class="mb-4">
			Welcome {{ $auth.user.firstName }}
		</h3>
		<v-tabs vertical align-tabs="start">
			<v-tab v-for="(tab, index) in tabs" :key="index" :to="tab.to" exact class="justify-start">
				<v-icon left>
					{{ tab.icon }}
				</v-icon>
				{{ tab.text }}
			</v-tab>
		</v-tabs>
	</div>
</template>

<script>
export default {
	data() {
		return {
			tabs: [
				//	Changed to account on first render, later implementation required
				// {
				// 	text: this.$t("title.profile"),
				// 	icon: "mdi-message-text",
				// 	to: this.localePath({
				// 		name: "my-profile",
				// 	}),
				// },
				{
					text: this.$t("title.account"),
					icon: "mdi-account",
					to: this.localePath({
						name: "my-account",
					}),
				},
				{
					text: this.$t("title.wishlist"),
					icon: "mdi-heart",
					to: this.localePath({
						name: "my-wishlist",
					}),
				},
				//	hidden for now
				// {
				// 	text: this.$t("title.my-ads"),
				// 	icon: "mdi-message-text",
				// 	to: this.localePath({
				// 		name: "my-ads",
				// 	}),
				// },
				{
					text: this.$t("title.addresses"),
					icon: "mdi-domain",
					to: this.localePath({
						name: "my-addresses",
					}),
				},
				{
					text: this.$t("title.reviews"),
					icon: "mdi-star",
					to: this.localePath({
						name: "my-reviews",
					}),
				},
				{
					text: this.$t("title.orders"),
					icon: "mdi-dropbox",
					to: this.localePath({
						name: "my-orders",
					}),
				},
				{
					text: this.$t("title.wallet"),
					icon: "mdi-message-text",
					to: this.localePath({
						name: "my-wallet",
					}),
				},
			],
		}
	},
}
</script>
