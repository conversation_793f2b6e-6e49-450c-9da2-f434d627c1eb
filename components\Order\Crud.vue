<template>
	<div>
		<v-dialog v-model="editQtyModel" max-width="400">
			<v-form>
				<v-card>
					<v-card-title>
						Edit Quantity
					</v-card-title>
					<v-card-text>
						<v-text-field v-model="qty" label="Quantity" type="number" />
					</v-card-text>
					<v-card-actions>
						<v-btn text @click="editQtyModel = false">
							Cancel
						</v-btn>
						<v-spacer />
						<v-btn color="primary" :loading="isSavingQty" @click="saveNewQty">
							Save
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</v-dialog>

		<v-dialog v-model="editCostModel" max-width="400">
			<v-form>
				<v-card>
					<v-card-title>
						Edit cost
					</v-card-title>
					<v-card-text>
						<money-field v-model="cost" :label="'cost item'" />
					</v-card-text>
					<v-card-actions>
						<v-btn text @click="editCostModel = false">
							Cancel
						</v-btn>
						<v-spacer />
						<v-btn color="primary" :loading="isSavingCost" @click="saveNewCost">
							Save
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</v-dialog>
		<crud ref="crud" width="100%" :default="defaultItem" :item-name="$t('common.order')"
			api="/v1/admin/orders?trans=off" v-on="$listeners">
			<template #default="{ item }">
				<v-tabs v-model="productTabsModel" vertical>
					<v-tab href="#info">
						{{ $t("order.info") }}
					</v-tab>
					<v-tab href="#items">
						{{ $t("order.items") }}
					</v-tab>
					<v-tab href="#payment">
						{{ $t("common.payment") }}
					</v-tab>
					<v-tab href="#shipping">
						{{ $t("order.shipping") }}
					</v-tab>
					<v-tab-item value="info">
						<v-row>
							<v-col cols="12" md="7">
								<v-card flat>
									<v-card-title> {{ $t("order.order-info") }} </v-card-title>
									<v-card-text>
										<v-row dense>
											<v-col cols="6">
												<b> {{ $t("order.order-id") }} :</b>
											</v-col>
											<v-col cols="6">
												{{ item.orderId }}
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b>{{ $t("common.created-at") }}:</b>
											</v-col>
											<v-col cols="6">
												{{ item.createdAt | dateTime }} ({{ item.createdAt | fromNow
												}})
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b> {{ $t("common.status") }} :</b>
											</v-col>
											<v-col cols="6">
												<status :items="orderStatuses" :value="item.status" />
											</v-col>
										</v-row>
											<v-row dense>
													<v-col cols="6">
<b>cost:</b>
</v-col>
													<v-col cols="6">
{{ item.cost }}
</v-col>
												</v-row>
										<v-row dense>
											<v-col cols="6">
												<b> {{ $t("common.total") }} :</b>
											</v-col>
											<v-col cols="6">
												<div>
													<b class="primary--text text-h6">{{ item.total | money("JOD") }}</b>
													<v-btn
														v-if="['editing', 'received'].includes(item.status) && (item.paymentStatus == 'paid' || item.paymentStatus == 'partialPaid')"
														text :loading="isRequestingEdit" @click="refundOrder">
														<v-icon>mdi mdi-information </v-icon> <b>{{ $t("order.refund-to-wallet") }}
														</b>
													</v-btn>
												</div>
											</v-col>
										</v-row>
</v-card-text>
								</v-card>
								<v-divider />
								<v-card flat>
									<v-card-title> {{ $t("common.user") }} </v-card-title>
									<v-card-text>
										<v-row dense>
											<v-col cols="6">
												<b>{{ $t("order.has-profile") }} ?</b>
											</v-col>
											<v-col cols="6">
												<check-mark :value="!!item.userId" />
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b> {{ $t("order.has-ordered-before") }}?</b>
											</v-col>
											<v-col cols="6">
												<check-mark :value="item?.user?.numberOfOrders ?? 0" />
												<span v-if="item?.user?.numberOfOrders">
													{{ $t("order.number-of-orders") }} {{ item?.user?.numberOfOrders ?? 0 }}
												</span>
											</v-col>
										</v-row>

										<v-row v-if="!item.userId" dense>
											<v-col cols="6">
												<b>{{ $t("order.visitor-id") }}:</b>
											</v-col>
											<v-col cols="6">
												{{ item.visitorId }}
											</v-col>
										</v-row>
										<v-row v-else dense>
											<v-col cols="6">
												<b>{{ $t("order.user-id") }}:</b>
											</v-col>
											<v-col cols="6">
												{{ item.userId }}
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b> {{ $t("common.name") }}:</b>
											</v-col>
											<v-col v-if="item.user" cols="6">
												{{ item.user.fullName }}
											</v-col>
											<v-col v-else cols="6">
												{{ item.address?.recipientName }}
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b> {{ $t("common.phone") }}:</b>
											</v-col>
											<v-col v-if="item.user" cols="6">
												<a :href="`tel:${$options.filters.phone(item.user.phone)}`">{{
													item.user.phone | phone }}</a>
											</v-col>
											<v-col v-else cols="6">
												<a :href="`tel:${$options.filters.phone(item.address?.phone)}`">{{
													item.address?.phone | phone }}</a>
											</v-col>
										</v-row>
									</v-card-text>
								</v-card>
								<v-divider />
								<v-card flat>
									<v-card-title> {{ $t("common.address") }} </v-card-title>
									<v-card-text>
										<v-row dense>
											<v-col cols="6">
												<b>{{ $t("common.city") }} </b>
											</v-col>
											<v-col cols="6">
												{{ item.address?.city?.name | trans }}
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b>{{ $t("common.district") }} </b>
											</v-col>
											<v-col cols="6">
												{{ item.address?.district }}
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b>{{ $t("common.street") }} </b>
											</v-col>
											<v-col cols="6">
												{{ item.address?.street }}
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b>{{ $t("common.building-number") }} </b>
											</v-col>
											<v-col cols="6">
												{{ item.address?.buildingNumber }}
											</v-col>
										</v-row>
										<v-row dense>
											<v-col cols="6">
												<b>{{ $t("common.apartment-number") }} </b>
											</v-col>
											<v-col cols="6">
												{{ item.address?.apartmentNumber }}
											</v-col>
										</v-row>
									</v-card-text>
								</v-card>
							</v-col>
							<v-divider vertical />
							<!-- <v-col cols="12" md="4"></v-col> -->
							<v-col cols="12" md="5">
								<v-card flat>
									<v-card-title> {{ $t("common.note") }} </v-card-title>
									<v-list-item v-for="(note, nIndex) in item.notes" :key="'note-' + nIndex">
										<v-list-item-avatar>
											<v-avatar size="40">
												<v-img contain
													:src="note.user?.media?.avatar?.src || '/images/default-avatar.jpg'" />
											</v-avatar>
										</v-list-item-avatar>
										<v-list-item-content>
											<v-list-item-title>{{ note.user?.fullName }}</v-list-item-title>
											<div class="text-body-2">
												{{ note.note }}
											</div>
											<v-list-item-subtitle>
												<small>{{ note.createdAt | dateTime }} ({{ note.createdAt | fromNow
													}})</small>
											</v-list-item-subtitle>
										</v-list-item-content>
									</v-list-item>
									<v-card-text>
										<v-alert v-if="!item.notes?.length" type="info" color="primary" text dense>
											{{ $t("order.there-is-no-any-notes-yet") }}
										</v-alert>
										<vc-textarea v-model="note" hide-details :label="$t('common.note')" rows="3" />
									</v-card-text>
									<v-card-actions class="px-4">
										<v-btn color="primary" block :disabled="!note" :loading="isAddingNote" @click="addNote">
											{{ $t("order.add-note") }}
										</v-btn>
									</v-card-actions>
									<v-card-text>
										<money-field v-model="cost" :label="'cost order'" />
									</v-card-text>
									<v-card-actions class="px-4">
										<v-btn color="primary" block :loading="isUpdatingCost" @click="updateCost">
											{{ $t("order.update-cost") }}
										</v-btn>
									</v-card-actions>
								</v-card>
							</v-col>
						</v-row>
					</v-tab-item>
					<v-tab-item value="items">
						<v-card flat>
							<v-card-title> {{ $t("order.order-items") }} </v-card-title>
							<v-card-text>
								<div class="d-flex justify-end">
									<product-select-modal @input="addOrderItem">
										<template #activator="{ on }">
											<v-btn text color="primary" :disabled="!isEditingMode" v-on="on">
												<v-icon left>
													mdi-plus
												</v-icon> {{ $t("order.add-product") }}
											</v-btn>
										</template>
									</product-select-modal>
								</div>
								<v-simple-table>
									<template #default>
										<thead>
											<tr>
												<th>#</th>
												<th class="text-left" />
												<th class="text-left">
													{{ $t("product.name") }}
												</th>
												<th class="text-left">
													{{ $t("common.attribute") }}
												</th>
												<th class="text-left">
													{{ $t("product.product-sku") }}
												</th>
												<th class="text-left">
													{{ $t("common.price") }}
												</th>
												<th class="text-left">
													{{ $t("common.cost") }}
												</th>
												<th class="text-left">
													{{ $t("product.quantity") }}
												</th>
												<th class="text-left" />
											</tr>
										</thead>
										<tbody>
											<tr v-for="(orderItem, index) in item.orderItems"
												:key="'order-item-' + orderItem.orderItemsId" :class="[orderItem.status]">
												<!-- <td>
													<v-checkbox disabled :input-value="true" />
												</td> -->
												<td> {{ ++index }}</td>
												<td>
<v-img width="46"
														:src="orderItem.snapshot.media.cover?.[0]?.src || orderItem.product.media.cover?.[0]?.src || orderItem.variance.media.cover?.[0]?.src" />
												</td>
												<td>
													<v-list-item>
														<v-list-item-content>
															<v-list-item-title
																:class="{ 'text-decoration-line-through': orderItem.product.deletedAt }">
																{{ orderItem.snapshot.product.name | trans }}
															</v-list-item-title>
															<v-list-item-subtitle v-if="orderItem.product.type !== 'bundle'"
																:class="{ 'text-decoration-line-through': orderItem.variance.deletedAt }">
																{{ orderItem.snapshot.variance.name | trans }}
															</v-list-item-subtitle>
														</v-list-item-content>
														<v-list-item-action>
															<v-btn icon small :href="localePath({
																name: 'product',
																params: {
																	productSlug: orderItem.product.slug,
																	varianceSlug: orderItem.product.type !== 'bundle' ? orderItem.variance?.slug : '',
																},
															})
																" target="_blank">
																<v-icon small>
																	mdi-open-in-new
																</v-icon>
															</v-btn>
														</v-list-item-action>
													</v-list-item>
												</td>
												<td>
													<div v-for="attribute in orderItem.snapshot.attributesWithValue"
														:key="attribute.attributeValuesId">
{{ attribute.name }} : {{ attribute.value
															?? attribute.option_name }}
</div>
												</td>
												<td>
													{{ orderItem.product.SKU }}<span
														v-if="orderItem.product.type == 'alternative'">-</span>
													<span v-if="orderItem.product.type !== 'bundle'"> {{ orderItem.variance.SKU
														}}</span>
												</td>
												<td> <b class="primary--text"> {{ orderItem.price.value | money("JOD") }} </b></td>
												<td> <b class="primary--text"> {{ orderItem.cost.value | money("JOD") }} </b></td>
												<td>{{ orderItem.quantity }}</td>
												<td>
													<v-menu offset-x>
														<template #activator="{ on, attrs }">
															<v-btn icon small v-bind="attrs" v-on="on">
																<v-icon>mdi-dots-vertical</v-icon>
															</v-btn>
														</template>
														<v-list dense :disabled="!isEditingMode">
															<v-list-item :disabled="!isEditingMode"
																@click="openEditQuantityDialog(orderItem.quantity, orderItem.model_id)">
																<v-list-item-action>
																	<v-icon>mdi-pencil</v-icon>
																</v-list-item-action>
																<v-list-item-title> {{ $t("order.edit-quantity") }} </v-list-item-title>
															</v-list-item>

															<v-list-item :disabled="!isEditingMode"
																@click="openEditCostDialog(orderItem.cost, orderItem.orderItemsId)">
																<v-list-item-action>
																	<v-icon>mdi-pencil</v-icon>
																</v-list-item-action>
																<v-list-item-title> {{ $t("order.edit-cost") }} </v-list-item-title>
															</v-list-item>

															<v-list-item :disabled="!isEditingMode"
																@click="deleteProduct(orderItem.orderItemsId)">
																<v-list-item-action>
																	<v-icon>mdi-delete</v-icon>
																</v-list-item-action>
																<v-list-item-title> {{ $t("common.remove") }}</v-list-item-title>
															</v-list-item>
														</v-list>
													</v-menu>
												</td>
											</tr>
										</tbody>
									</template>
								</v-simple-table>
							</v-card-text>
						</v-card>
					</v-tab-item>

					<v-tab-item value="payment">
						<v-row>
							<v-col cols="12" md="12">
								<v-card flat>
									<v-card-title>{{ $t("common.payment-method") }} </v-card-title>
									<v-card-text>
										<v-row align="center">
											<v-col cols="12" md="8">
												<v-row dense>
													<v-col cols="6">
														<b>{{ $t("common.payment-method") }}:</b>
													</v-col>
													<v-col cols="6">
														{{ item.paymentMethod?.name | trans }}
													</v-col>
												</v-row>
												<!-- <v-row dense>
															<v-col cols="6"> <b>Payment Status:</b> </v-col>
															<v-col cols="6"><status :items="paymentStatuses" :value="item.paymentStatus" /></v-col>
														</v-row> -->
												<v-row dense>
													<v-col cols="6">
														<b>{{ $t("common.amount") }} :</b>
													</v-col>
													<v-col cols="6">
														<b class="primary--text text-h6"> {{ item.total | money('JOD') }}</b>
													</v-col>
												</v-row>
												<!-- <v-row dense>
															<v-col cols="6"> <b>Payment ID:</b> </v-col>
															<v-col cols="6">{{ item.paymentId }}</v-col>
														</v-row> -->
											</v-col>
											<v-col>
												<v-img v-if="item.paymentMethod?.media?.logo"
													:src="item.paymentMethod?.media.logo?.src" width="120" />
											</v-col>
										</v-row>
									</v-card-text>
								</v-card>
							</v-col>

							<v-col cols="12" md="12">
								<v-card-title>{{ $t("order.payment-invoices") }} </v-card-title>
								<v-simple-table>
									<template #default>
										<thead>
											<tr>
												<th class="text-left">
													#
												</th>
												<th class="text-left">
													{{ $t("order.invoice-id") }}
												</th>
												<th class="text-left">
													{{ $t("order.version-number") }}
												</th>
												<th class="text-left">
													{{ $t("common.status") }}
												</th>
												<th class="text-left">
													{{ $t("common.payment-status") }}
												</th>
												<th class="text-left">
													{{ $t("order.total") }}
												</th>
												<th class="text-left">
													{{ $t("common.action") }}
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(invoice, indexInvoice ) in item.invoices" :key="invoice.invoiceId"
												:class="[invoice.status]">
												<td>{{ ++indexInvoice }}</td>
												<td>#{{ invoice.invoiceId }}</td>
												<td>{{ invoice.versionNumber }}</td>
												<td>{{ invoice.status }}</td>
												<td><invoice-payment-status :value="invoice.paymentStatus" /></td>
												<td><b class="primary--text"> {{ invoice.total | money('JOD') }}</b></td>
												<td>
													<v-btn v-if="item.status === 'active'" :disabled="item.status !== 'processing'"
														text color="primary" small :loading="isLoading"
														@click="setAsPaid(invoice.invoiceId)">
														{{ $t("order.set-as-paid") }}
													</v-btn>
												</td>
											</tr>
										</tbody>
									</template>
								</v-simple-table>
							</v-col>
						</v-row>
					</v-tab-item>

					<v-tab-item value="shipping">
						<v-card-title>{{ $t("order.deliveries") }}</v-card-title>
						<v-simple-table>
							<template #default>
								<thead>
									<tr>
										<th class="text-left">
											{{ $t("order.order-delivery-id") }}
										</th>
										<th class="text-left">
											{{ $t("common.price") }}
										</th>
										<th class="text-left">
											{{ $t("common.status") }}
										</th>
										<th class="text-left">
											{{ $t("common.note") }}
										</th>
										<th class="text-left">
											{{ $t("common.action") }}
										</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="delivery in item.deliveries" :key="delivery.orderDeliveryId">
										<td>#{{ delivery.orderDeliveryId }}</td>
										<td> <b class="primary--text"> {{ delivery.price | money('JOD') }}</b></td>
										<td>{{ delivery.status }}</td>
										<td>{{ delivery.note }}</td>
										<td>
											<v-btn :disabled="item.status !== 'processing' || delivery.status === 'delivered'"
												small text color="primary" :loading="isLoading"
												@click="setAsDelivered(delivery.orderDeliveryId)">
												{{ $t("order.set-as-delivered") }}
											</v-btn>
										</td>
									</tr>
								</tbody>
							</template>
						</v-simple-table>
					</v-tab-item>
				</v-tabs>
			</template>
			<template #actions="{ item }">
				<v-sheet width="100%">
					<v-divider inset />
					<v-card flat>
						<v-card-text v-if="['editing'].includes(item.status)" class="d-flex align-center">
							<v-avatar slot="icon" :color="editingColor" size="40" class="me-4">
								<v-icon>{{ isEditingMode ? "mdi-alert" : "mdi-pencil" }}</v-icon>
							</v-avatar>

							<div class="text-subtitle-1">
								{{ isEditingMode ? "You are in edit mode" : "You are not in edit mode" }}
							</div>
						</v-card-text>

						<v-card-actions>
							<v-btn text :disabled="item.status === 'canceled'" @click="changeStatus('canceled')">
								{{ $t('order.cancel-order') }}
							</v-btn>
							<v-btn v-if="(item.paymentStatus == 'paid' || item.paymentStatus == 'partialPaid') && !['completed', 'forRefund', 'refunded'].includes(item.status)"
								text
								:disabled="item.status === 'forRefund'" @click="changeStatus('forRefund')">
								{{ $t('order.for-refund') }}
							</v-btn>

							<v-btn v-if="(item.paymentStatus == 'paid' || item.paymentStatus == 'partialPaid') && (item.status === 'forRefund')"
								text
								@click="changeStatus('refunded')">
								{{ $t('order.refunded') }}
							</v-btn>
							<v-spacer />
							<v-btn v-if="['editing', 'received'].includes(item.status)" text :loading="isRequestingEdit"
								@click="toggleEdit">
								{{ isEditingMode ? $t('order.end-edit') : $t('order.start-edit') }}
							</v-btn>
							<v-btn v-if="item.status === 'draft'" text color="primary" @click="changeStatus('received')">
								Move As Received
							</v-btn>
							<v-btn v-else :disabled="item.status !== 'received'" text color="primary"
								@click="changeStatus('processing')">
								{{ $t('order.start-processing') }}
							</v-btn>

							<v-btn v-if="item.status === 'processing'" text color="primary" @click="changeStatus('completed')">
								{{ $t('order.completed') }}
							</v-btn>
						</v-card-actions>
					</v-card>
				</v-sheet>
			</template>
		</crud>
	</div>
</template>

<script>
import { orderStatuses } from '~/config/Enum'
import crudInterface from '~/mixins/crudInterface'
export default {
	mixins: [crudInterface],
	data() {
		return {
			// isEditingMode: false,
			isRequestingEdit: false,
			isLoading: false,
			productTabsModel: 'info',
			note: '',
			isAddingNote: false,
			isUpdatingCost: false,
			orderStatuses,
			defaultItem: {
				note: {},
				shippingPrice: null,
				status: null,
				tax: null,
				userId: null,
				shippingCarrierId: null,
				addressId: null,
				orderItems: [],
			},
			isAddingProduct: false,
			editQtyModel: false,
			editCostModel: false,
			qty: null,
			model_id: null,
			isSavingQty: false,
			isSavingCost: false,
			orderItemsId: null,
			cost: {
					value: null,
					// basePrice: null,
					currencyId: 1,
				},
		}
	},
	computed: {
		canAddOrderItem() {
			return ['received'].includes(this.item.status)
		},
		editingColor() {
			return this.isEditingMode ? 'warning' : 'info'
		},
		isEditingMode() {
			return this.item.editors?.find(editor => editor.userId === this.$auth.user.userId && !editor.endAt)
		},
		currentEditor() {
			return this.item.editors?.[this.item.editor.length - 1]?.user
		},
	},
	methods: {
		updateOrder(order) {
			this.$set(this.$refs.crud, 'item', order)
		},
		addNote() {
			this.isAddingNote = true
			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/add-note`, { note: this.note })
				.then(this.updateOrder)
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isAddingNote = false
					this.note = ''
				})
		},
		updateCost() {
			this.isUpdatingCost = true
			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/update-cost`, { cost: this.cost })
				.then(this.updateOrder)
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isUpdatingCost = false
				})
		},
		addOrderItem(product) {
			/// {{APIVersion}}/admin/orders/:orderId/add-product
			this.isAddingProduct = true
			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/add-product`, {
					productId: product.productId,
					varianceId: product.variance.varianceId,
					quantity: 1,
				})
				.then(this.updateOrder)
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isAddingProduct = false
				})
		},
		removeOrderItem(orderItem) {
			this.$axios
				.$put(`/v1/admin/orders/${this.item.orderId}/items/${orderItem.orderItemsId}/cancel`)
				.then(this.updateOrder)
				.catch(this.genericErrorHandler)
		},
		setAsPaid(invoiceId) {
			this.isLoading = true

			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/deliver-money`, { invoiceId })
				.then((res) => {
					this.updateOrder(res)
				})
				.finally(() => {
					this.isLoading = false
				})
		},
		setAsDelivered(orderDeliveryId) {
			this.isLoading = true

			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/change-delivery-status`, { orderDeliveryId, status: 'delivered' })
				.then((res) => {
					this.updateOrder(res)
				})
				.finally(() => {
					this.isLoading = false
				})
		},
		toggleEdit() {
			this.isRequestingEdit = true
			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/${this.isEditingMode ? 'end' : 'start'}-editing`)
				.then((res) => {
					// this.isEditingMode = !this.isEditingMode
					this.refresh()
				})
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isRequestingEdit = false
				})
		},
		deleteProduct(orderItemsId) {
			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/delete-product`, { orderItemsId })
				.then((res) => {
					this.refresh()
				}).catch(this.genericErrorHandler)
		},
		changeStatus(status) {
			this.isLoading = true
			this.$axios
				.$put(`/v1/admin/orders/${this.item.orderId}/change-status/${status}`)
				.then((res) => {
					this.refresh()
				})
				.finally(() => {
					this.isLoading = false
				})
		},
		refresh() {
			this.$refs.crud.get()
		},
		openEditQuantityDialog(qty, modelId) {
			this.model_id = modelId
			this.qty = qty
			this.editQtyModel = true
		},
		openEditCostDialog(cost, orderItemsId) {
			console.log("openEditCostDialog", cost, orderItemsId)
			this.orderItemsId = orderItemsId
			this.cost.value = cost.value
			this.cost.currencyId = cost.currencyId
			this.editCostModel = true
		},

		refundOrder() {
			this.isLoading = true
			this.$confirm("Are you sure you want to refund money for this order?", {
				title: "Refund Order",
			}).then((isAccepted) => {
				if (isAccepted) {
					const orderId = this.item.orderId
					this.$axios.$post(`/v1/admin/orders/${orderId}/refund-order`)
						.then((res) => {
							this.updateOrder(res)
						})
						.catch(this.genericErrorHandler)
						.finally(() => {
							this.isLoading = false
						})
				}
			})
			this.isLoading = false
		},
		saveNewQty() {
			this.isSavingQty = true
			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/quantity-product`, {
					model_id: this.model_id,
					quantity: this.qty,
				})
				.then((res) => {
					this.updateOrder(res)
					this.editQtyModel = false
				})
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isSavingQty = false
				})
		},
		saveNewCost() {
			this.isSavingCost = true
			this.$axios
				.$post(`/v1/admin/orders/${this.item.orderId}/update-cost-item`, {
					orderItemsId: this.orderItemsId,
					cost: this.cost,
				})
				.then((res) => {
					this.updateOrder(res)
					this.editCostModel = false
				})
				.catch(this.genericErrorHandler)
				.finally(() => {
					this.isSavingCost = false
				})
			},
	},
}
</script>

<style>
tr.cancelled,
tr.canceled {
	position: relative;
}

tr.cancelled td,
tr.canceled td {
	opacity: 0.5;
}

tr.cancelled td::before,
tr.canceled td::before {
	content: " ";
	position: absolute;
	top: 50%;
	left: 0;
	border-bottom: 1px solid #111;
	width: 100%;
}
</style>
