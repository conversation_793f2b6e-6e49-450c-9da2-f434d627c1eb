<template>
	<div class="overlay" :style="{'--opacity':opacity,'--frosted':frosted,'--frosted-at-hover':frostedAtHover}">
<div class="wrapper">
      <slot />
    </div>
</div>
</template>

<script>
export default {

	props: {
    opacity: {
      type: String,
      default: "0.5",
    },
    frosted: {
      type: String,
      default: "0",
    },
    frostedAtHover: {
      type: String,
      default: "0",
    },
  },
	data() {
		return {}
	},
	created() {},
	methods: {},
}
</script>

<style lang="scss" scoped>
.overlay {
height:100%;

  .wrapper{
    backdrop-filter: blur(calc(var(--frosted) * 1px));
   background: rgba(0, 0, 0, var(--opacity));
    height:100%;
    width:100%;
    transition: backdrop-filter 0.2s;
    border-radius: inherit;
    &:hover{
      backdrop-filter: blur(calc(var(--frosted-at-hover) * 1px));
      transition: backdrop-filter 0.3s;
    }
  }
}
</style>
