<template>
	<v-sheet color="transparent" class="page" min-height="calc(100vh - 126px)">
		<v-sheet
			elevation="0"
			tile
			min-height="100"
			color="transparent"
			class="d-none d-md-flex flex-md-row align-center justify-space-between px-md-4"
		>
			<div>
				<h1 class="font-weight-regular">
					{{ pageTitle }}
					<!-- <v-btn v-tooltip="$t('common.watch-tutorial-video')" small icon @click="showVideo"
						><v-icon small color="blue darken-2">mdi-help-circle</v-icon></v-btn
					> -->
				</h1>
				<!-- <div class="muted-2 text-body-2">
					{{ desc || "In nisi minim duis non adipisicing. Labore qui pariatur enim pariatur proident quis officia." }}
				</div> -->
			</div>
			<div class="d-flex align-center my-2 my-md-0">
				<v-spacer />
				<slot name="actions" />
			</div>
		</v-sheet>
		<Teleport to="#page-title">
{{ pageTitle }}
</Teleport>
		<div class="content">
			<slot name="prepend" />

			<v-row>
				<v-col cols="12" class="pa-0 pa-md-3 hhh">
					<v-tabs
						v-if="tabs.length"
						ref="tabs"
						v-model="localValue"
						background-color="transparent"
						:class="!isMobile && 'elevation-0 rounded-t '"
						show-arrows
						center-active
						:icons-and-text="isMobile"
						:grow="isMobile"
						class="custom-tabs"
					>
						<v-tab
							v-for="(tab, t) in tabs"
							:key="tab.text"
							:to="tab.to"
							class="inherit-border"
							:class="{
								'inactive-tab': !isMobile,
								'active-tab elevation-3 rounded-t': t === localValue || tab.isActive,
							}"
							:exact-active-class="isMobile ? 'active-tab' : 'active-tab elevation-3 rounded-t'"
							:exact-path="exactPath && !exact"
							:exact="exact"
							>
<span>{{ tab.text }} </span>
							<v-icon v-if="tab.icon" left class="order-md-first">
{{ tab.icon }}
</v-icon>
							<nuxt-img v-if="tab.img" :src="tab.img" width="24" left class="order-md-first me-3" />
						</v-tab>
					</v-tabs>

					<v-card :class="{ 'rounded-tr-0 rounded-tl-0': tabs.length }">
						<div class="relative">
							<v-progress-linear :value="100" absolute top :indeterminate="loading" :active="loading" />
							<div v-show="loading" class="absolute top left right text-center text-body-2 muted-2 py-2">
								{{ $vuetify.lang.translator("dataIterator.loadingText") }}
							</div>
						</div>
						<div style="min-height: 64px">
<slot />
</div>
						<slot name="tabs" v-bind="{ tabsModel }" />
					</v-card>
				</v-col>
			</v-row>
			<slot name="append" />
		</div>
		<video-player v-model="videoModel" :data="activeVideo" />
	</v-sheet>
</template>

<script>
export default {
	props: {
		tabs: {
			type: Array,
			default: () => [],
		},
		loading: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: null,
		},
		desc: {
			type: String,
			default: null,
		},
		value: {
			type: [String, Number],
			default: null,
		},
		exactPath: {
			type: Boolean,
			default: true,
		},
		exact: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			tabsModel: 0,
			activeVideo: {},
			videoModel: false,
		}
	},
	head() {
		return {
			title: this.pageTitle,
		}
	},
	computed: {
		pageTitle() {
			return this.title || this.$t(this.$route.meta.title)
		},
		localValue: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit("input", val)
			},
		},
	},
	mounted() {
		setTimeout(() => {
			this.$nextTick(() => {
				if (this.$refs.tabs) { this.$refs.tabs.callSlider() }
			})
		}, 100)
	},
	// created() {
	// 	this.$nuxt.$emit("page-change", {
	// 		title: this.pageTitle,
	// 		url: this.$route.path,
	// 	});
	// },
	methods: {
		getTabByIndex(index) {
			console.log("🚀 ~ file: Page.vue ~ line 119 ~ getTabByIndex ~ index", index)

			return this.$cloneDeep(this.tabs[index])
		},
		showVideo() {
			this.activeVideo = {
				id: 1,
				title: "How to Change your Profile Picture",
				subtitle: "Fitz & The Trantrums 1",
				time: "01:10:00",
				url: "https://www.w3schools.com/html/mov_bbb.mp4",
			}
			this.videoModel = true
		},
	},
}
</script>
<style lang="scss">
@import "~/assets/mixins";
.v-tabs.custom-tabs {
	@include responsive("width", 100%, 100%, unset);
	@include responsive("flex", 1 1 auto, 1 1 auto, unset);
}
.page {
	.content {
		& > .row > .col {
			@include responsive("padding", 0);
		}
	}
}

// width: unset; flex: unset; max-width: 100%
</style>
