<template>
	<v-img v-if="imgSrc" :src="imgSrc" max-width="100" max-height="40" contain />
	<span v-else>{{ $slots.default[0].text }}</span>
</template>

<script>
export default {
	computed: {
		imgSrc() {
			switch (this.$slots?.default?.[0]?.text) {
				case "Bank Transfer":
					return "/images/payments/wire-transfer.png"
				case "Visa":
					return "/images/payments/cc.png"
				case "Bitcoin":
					return "/images/payments/bitcoin.png"
				case "Ethereum":
					return "/images/payments/eth.png"
				case "Tether":
					return "/images/payments/teather.png"
				case "Netseller":
					return "/images/payments/Netseller.png"
				default:
					return ""
			}
		},
	},
}
</script>
