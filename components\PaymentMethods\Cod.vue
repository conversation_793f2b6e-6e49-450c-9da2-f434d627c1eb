<template>
	<v-card flat>
		<v-card-title> {{ $t("order.pay-on-delivery") }} </v-card-title>
		<v-card-actions>
			<v-spacer />
			<v-btn color="primary" :loading="isPaying" @click="pay">
				{{ $t("order.pay-by-cash") }}
			</v-btn>
		</v-card-actions>
	</v-card>
</template>

<script>
import paymentMethodsMixin from "~/mixins/paymentMethods"
export default {
	mixins: [paymentMethodsMixin],
	props: {
		order: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			isPaying: false,
		}
	},
	computed: {
		currentWalletBalance() {
			return (this.$auth.loggedIn && this.order.user?.wallet) || 0
		},
		orderAmount() {
			return this.item.total
		},
		orderShipping() {
			return this.order.shippingCarrier.name
		},
		orderAddressStreet() {
			return this.order.address.street
		},
		orderAddressDistrict() {
			return this.order.address.district
		},
	},
	methods: {
		pay() {
			this.isPaying = true
			/// {{APIVersion}}/orders/:orderId/pay
			this.$axios
				.$post(`/v1/orders/${this.order.orderId}/finalize`, {
					// paymentMethod: "wallet",
				})
				.then((response) => {
					this.$store.dispatch("cart/finalize")
					this.$fb.track('Purchase', {
						value: this.order.total.value,
						currency: this.order.total.currency,
						num_items: this.order.orderItems.length,
						contents: this.order.orderItems.map(item => ({
							id: item.productId,
							quantity: item.quantity,
							item_price: item.price.value,
						})),
						content_type: 'product',
						shipping_method: this.order.shippingCarrier.name,
						shipping_cost: this.order.shippingPrice.value,
						address: this.order.address.district,
					})
					this.$emit("success")
				}).catch((e) => {
					if (e.response.status === 423) {
						this.$toast.error(e.response.data.message)
					}
				}).finally(() => {
					this.isPaying = false
				})
		},
	},
}
</script>
