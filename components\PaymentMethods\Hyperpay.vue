<template>
	<!-- <v-skeleton-loader type="paragraph@2" :loading="$fetchState.pending"> -->
	<v-card flat>
		<v-card-title> {{ $t("order.pay-by-credit-card") }} </v-card-title>
		<v-card-text>
			<v-window v-model="windowModel">
				<v-window-item>
					<v-container fluid>
						<v-sheet max-width="400">
							<v-form ref="paramsForm">
								<vc-text-field v-model="params.firstName" :rules="[$rules.required($t('common.first-name'))]"
									:label="$t('common.first-name')" />
								<vc-text-field v-model="params.lastName" :rules="[$rules.required($t('common.last-name'))]"
									:label="$t('common.last-name')" />
								<vc-text-field v-model="params.email" :rules="[$rules.required($t('common.email'))]"
									:label="$t('common.email')" />
								<v-btn color="primary" :loading="isLoading" @click="setParams">
									{{ $t("common.next") }}
								</v-btn>
							</v-form>
						</v-sheet>
					</v-container>
				</v-window-item>
				<v-window-item>
					<payment-methods-hyperpay-script v-bind="$props" />
				</v-window-item>
			</v-window>
		</v-card-text>
	</v-card>
	<!-- </v-skeleton-loader> -->
</template>

<script>
import paymentMethodsMixin from "~/mixins/paymentMethods"
export default {
	mixins: [paymentMethodsMixin],
	data() {
		return {
			windowModel: 0,
			params: {
				email: this.$auth.user?.email || "",
				firstName: this.$auth.user?.firstName || "",
				lastName: this.$auth.user?.lastName || "",
			},
			isLoading: false,
		}
	},
	computed: {
		isPay() {
			return this.item.paymentStatus === "paid"
		},
	},
	methods: {
		setParams() {
			if (!this.$refs.paramsForm.validate()) { return }
			this.isLoading = true
			this.$axios
				.$post(`/v1/orders/${this.$route.params.orderId}/set-payment-params`, {
					...this.params,
				})
				.then((res) => {
					this.item = res
					this.windowModel = 1
				}).catch((e) => {
					if (e.response.status === 423) {
						this.$toast.error(e.response.data.message)
					}
				}).finally(() => {
					this.isLoading = false
				})
		},
	},
	created() {
		if (this.isPay) {
			this.$emit('success')
		}
	},
	// async fetch() {
	// 	const checkoutId = this.$route.query.id
	// 	const orderId = this.$route.params.orderId
	// 	const resourcePath = this.$route.query.resourcePath
	// 	if (checkoutId) {
	// 		//! TODO: fix:url, convert to GET  /v1/orders/${orderId}/verify, pass all query as query
	// 		await this.$axios
	// 			.$post(`/v1/orders/${orderId}/verify-order-payment-hyperpay`, {
	// 				resourcePath,
	// 				checkoutId,
	// 			})
	// 			.then((res) => {
	// 				// this.item = res
	// 				this.$emit('success')
	// 			})
	// 			.catch(this.genericErrorHandler)
	// 	}
	// },

}
</script>
