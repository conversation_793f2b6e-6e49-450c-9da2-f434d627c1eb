<template>
	<form method="GET" class="paymentWidgets" data-brands="VISA MASTER" :action="formAction">
		<input v-for="(value, name) in formParams" :key="name" type="hidden" :name="name" :value="value">

		<input id="signature" type="hidden" name="signature" value="sign($formParams) }}">
		<input id="submit" type="submit" value="Confirm">
		<input id="submit" class="btn btn-purple btn-block" type="submit" value="">
	</form>
</template>

<script>
import paymentMethodsMixin from "~/mixins/paymentMethods"
export default {
	mixins: [paymentMethodsMixin],
	head() {
		return {
			script: [
				{
					body: true,
					innerHTML: `
                        var wpwlOptions = {
                            paymentTarget: "_top",
                            locale: "${this.$i18n.locale}",
                            iframeStyles: {
                                'card-number-placeholder': {
                                    'direction': 'ltr',
                                    'text-align': 'center',
                                },
                                'cvv-placeholder': {
                                    'direction': 'ltr',
                                    'text-align': 'center',
                                },

                            },
                            onReady: function() {

                               
                            }
                        }
                    `,
				},
				{
					src: (this.isDev ? "https://test.oppwa.com" : "https://oppwa.com") + "/v1/paymentWidgets.js?checkoutId=" + this.checkoutId,
					body: true,
					hid: "hyperpay",
					// callback: () => {
					//     console.log("Hyperpay script loaded");
					// },
				},
			],
		}
	},
	computed: {
		formParams() {
			return this.item.formSign
		},
		formAction() {
			console.log("params", this.$route.params)
			const baseUrl = window.location.origin
			const locale = this.$i18n.locale
			const orderId = this.$route.params.orderId // Replace with dynamic value as needed
			const step = 3 // Replace with dynamic value as needed
			return `${baseUrl}/${locale}/checkout/${orderId}/${step}`
		},
		checkoutId() {
			return this.item.formSign.checkoutId
		},
	},
}
</script>
