<template>
	<v-card flat>
		<v-card-title> {{ $t("order.pay-by-wallet") }} </v-card-title>
		<v-card-text>
			<v-list v-if="$auth.loggedIn" dense flat tile subheader>
				<v-list-item :link="false">
					<v-list-item-title> {{ $t("order.your-current-wallet-balance-is") }} </v-list-item-title>
					<v-list-item-subtitle class="text-right">
						<span :class="amountColor(currentWalletBalance.value) + '--text'">{{ currentWalletBalance | money }}
						</span>
					</v-list-item-subtitle>
				</v-list-item>
				<v-list-item>
					<v-list-item-title> {{ $t("order.your-order-amount") }} </v-list-item-title>
					<v-list-item-subtitle class="text-right">
						<span :class="amountColor(orderAmount?.value) + '--text'">{{ orderAmount | money }} </span>
					</v-list-item-subtitle>
				</v-list-item>
				<v-divider />
				<v-list-item>
					<v-list-item-title>
						<b>{{ $t("order.remaining-balance") }}</b>
					</v-list-item-title>
					<v-list-item-subtitle class="text-right">
						<span :class="amountColor(afterPaymentBalance?.value) + '--text'">{{ afterPaymentBalance | money }}
						</span>
					</v-list-item-subtitle>
				</v-list-item>
			</v-list>
			<div v-else>
				<v-alert type="warning" outlined>
					{{ $t("order.you-need-to-login-to-use-your-wallet") }}
					<v-btn small color="warning" outlined @click="$nuxt.$emit('auth.show')">
						{{ $t("auth.login") }}
					</v-btn>
				</v-alert>
			</div>
		</v-card-text>
		<v-card-actions>
			<v-spacer />
			<v-btn color="primary" :loading="isPaying" :disabled="!canPayByWallet" @click="pay">
				{{ $t("order.pay-by-wallet") }}
			</v-btn>
		</v-card-actions>
	</v-card>
</template>

<script>
import paymentMethodsMixin from "~/mixins/paymentMethods"
export default {
	mixins: [paymentMethodsMixin],
	props: {
		order: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			isPaying: false,
		}
	},
	computed: {
		currentWalletBalance() {
			return (this.$auth.loggedIn && this.order.user?.wallet) || 0
		},
		orderAmount() {
			return this.item.total
		},
		afterPaymentBalance() {
			return {
				value: this.currentWalletBalance.value - this.orderAmount.value,
				currency: this.orderAmount?.currency || "JOD",
			}
		},
		canPayByWallet() {
			return this.currentWalletBalance.value >= this.orderAmount?.value
		},
	},
	methods: {
		pay() {
			if (this.canPayByWallet) {
				this.isPaying = true
				/// {{APIVersion}}/orders/:orderId/pay
				this.$axios
					.$post(`/v1/orders/${this.order.orderId}/finalize`, {
						// paymentMethod: "wallet",
					})
					.then((response) => {
						this.$store.dispatch("cart/finalize")
						this.$fb.track('Purchase', {
							value: this.order.total.value,
							currency: this.order.total.currency,
							num_items: this.order.orderItems.length,
							contents: this.order.orderItems.map(item => ({
								id: item.productId,
								quantity: item.quantity,
								item_price: item.price.value,
							})),
							content_type: 'product',
							shipping_method: this.order.shippingCarrier.name,
							shipping_cost: this.order.shippingPrice.value,
							address: this.order.address.district,
						})
						this.$emit("success")
					})
					.finally(() => {
						this.isPaying = false
					})
			}
		},
	},
}
</script>
