<template>
	<div>
		<div class="d-flex">
			<v-sheet width="36" color="transparent" class="flex-shrink-0">
				<v-btn v-show="index === 0" icon @click="$emit('expand')">
<v-icon>mdi-menu-down</v-icon>
</v-btn>
			</v-sheet>
			<vc-checkbox
				:true-value="true"
				:false-value="false"
				class="mt-0"
				hide-details
				:value="item.value"
				:label="item.text"
				:disabled="isAllSelected || !isAllowed || isSelectedByRule"
				:input-value="isItemSelected || isAllSelected || isSelectedByRule"
				:indeterminate="(isSomeChildrenSelected && !isAllChildrenSelected) || hasChildrenSelectedByRule"
				@change="select(item.value, $event)"
			/>
		</div>
		<v-row v-if="isExpanded" dense class="ps-8">
			<v-col>
				<permissions-field
					v-for="child in item.children"
					:key="child.value"
					v-model="localValue"
					:item="child"
					:all-wild-value="[allWildValue, itemMetaAll]"
					:disable-not-inherited="disableNotInherited"
					@selected="parentSelectHandler"
				/>
			</v-col>
		</v-row>
	</div>
</template>

<script>
import permissions from "~/mixins/permissions.js"
export default {
	mixins: [permissions],
	props: {
		isExpanded: {
			type: Boolean,
			default: false,
		},
		index: {
			type: Number,
			required: true,
		},
	},
	data() {
		return {}
	},
	methods: {},
}
</script>
