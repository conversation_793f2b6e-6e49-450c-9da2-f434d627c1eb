<template>
	<div>
		<div class="d-flex">
			<v-sheet width="36" color="transparent">
				<v-btn v-if="item.children" icon @click="expand">
<v-icon>mdi-menu-down</v-icon>
</v-btn>
			</v-sheet>
			<vc-checkbox
				:true-value="true"
				:false-value="false"
				class="mt-0"
				hide-details
				:indeterminate="(isSomeChildrenSelected && !isAllChildrenSelected) || hasChildrenSelectedByRule"
				:input-value="isItemSelected || isAllSelected || isSelectedByRule"
				:value="item.value"
				:label="item.text"
				:disabled="isAllSelected || !isAllowed || isSelectedByRule"
				@change="select(item.value, $event)"
			/>
		</div>
		<div v-if="isExpanded" class="ps-8 pb-2">
			<div v-for="child in item.children" :key="child.value">
				<permissions-field
					v-model="localValue"
					:item="child"
					:all-wild-value="allWildValue.concat(itemMetaAll)"
					:disable-not-inherited="disableNotInherited"
					@selected="parentSelectHandler"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import permissions from "~/mixins/permissions.js"
export default {
	mixins: [permissions],
	props: {},
	data() {
		return {
			isExpanded: false,
		}
	},
	methods: {
		expand() {
			this.isExpanded = !this.isExpanded
		},
	},
}
</script>

<style scoped>
.v-label {
	-webkit-line-clamp: 1;
	overflow: hidden;
	padding-bottom: 0 !important;
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-all;
	word-break: break-word;
	hyphens: auto;
}
</style>
