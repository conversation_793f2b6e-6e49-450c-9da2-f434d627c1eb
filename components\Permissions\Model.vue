<template>
	<div>
		<div class="d-flex">
			<v-sheet width="36" color="transparent">
				<v-btn v-if="item.children" icon @click="expand">
<v-icon>mdi-menu-down</v-icon>
</v-btn>
			</v-sheet>
			<vc-checkbox
				:true-value="true"
				:false-value="false"
				class="mt-0"
				hide-details
				:value="item.value"
				:label="item.text"
				:disabled="!isAllowed || isSelectedByRule"
				:input-value="isItemSelected || isAllSelected || isSelectedByRule"
				:indeterminate="(isSomeChildrenSelected && !isAllChildrenSelected) || hasChildrenSelectedByRule"
				@change="select(item.value, $event)"
			/>
		</div>
		<div v-if="isExpanded" class="ps-8">
			<v-row dense>
				<v-col
					v-for="(child, i) in item.children"
					:key="child.value"
					:cols="'auto'"
					:class="{ 'flex-grow-1': child.children && child.children.length }"
				>
					<permissions-action
						v-model="localValue"
						:index="i"
						:item="child"
						:is-expanded="isSubExpanded"
						:input-value="isItemSelected || isAllSelected"
						:all-wild-value="itemMetaAll"
						:disable-not-inherited="disableNotInherited"
						@selected="parentSelectHandler"
						@expand="expandSub"
					/>
				</v-col>
			</v-row>
		</div>
	</div>
</template>

<script>
import permissions from "~/mixins/permissions.js"
export default {
	mixins: [permissions],
	props: {},
	data() {
		return {
			isExpanded: false,
			isSubExpanded: false,
		}
	},
	methods: {
		expand() {
			this.isExpanded = !this.isExpanded
		},
		expandSub() {
			this.isSubExpanded = !this.isSubExpanded
		},
	},
}
</script>
