<template>
	<div>
		<v-toolbar flat>
			<vc-text-field
				v-model="searchModel"
				placeholder="Search"
				clearable
				append-icon="mdi-magnify"
				style="max-width: 250px"
			/>
			<v-spacer />
			<v-btn text @click="selectAll">
Select All
</v-btn>
			<v-btn text @click="unselectAll">
Unselect All
</v-btn>
		</v-toolbar>
		<v-skeleton-loader type="text@3" :loading="$fetchState.pending">
			<div>
				<permissions-root
					v-for="item in parsedPermissions"
					:key="item.value"
					v-model="localValue"
					:item="item"
					:disable-not-inherited="disableNotInherited"
				/>
				<v-alert v-if="!parsedPermissions.length" type="warning" dense>
					<div v-if="searchModel">
						No result found matching your search "{{ searchModel }}".
						<v-btn small text @click="searchModel = null">
Clear Search
</v-btn>
					</div>
					<div else>
No permissions found.
</div>
				</v-alert>
			</div>
		</v-skeleton-loader>
	</div>
</template>

<script>
import permissions from "~/mixins/permissions.js"
export default {
	mixins: [permissions],
	props: {
		value: {
			type: Array,
			default: () => [],
		},
		roleId: {
			type: [Number, String],
			default: undefined,
		},
		userId: {
			type: [Number, String],
			default: undefined,
		},
	},
	data() {
		return {
			permissions: [],
			searchModel: null,
		}
	},
	async fetch() {
		return await this.$axios
			.$get(`/v1/lookups/permissions`, {
				params: {
					role_id: this.roleId,
					user_id: this.userId,
				},
			})
			.then((resp) => {
				this.permissions = resp
			})
		// return await new Promise((resolve, reject) => {
		// 	this.permissions = [
		// 		{
		// 			id: "users.*",
		// 			value: "users.*",
		// 			name: "users",
		// 			children: [
		// 				{
		// 					value: "users.create",
		// 					name: "create",
		// 					children: [
		// 						{
		// 							value: "users.create.id",
		// 							name: "id",
		// 						},
		// 						{ value: "users.create.name", name: "name" },
		// 						{ value: "users.create.email", name: "email" },
		// 						{ value: "users.create.password", name: "password" },
		// 						{ value: "users.create.role_id", name: "role_id" },
		// 						{ value: "users.create.permissions", name: "permissions" },
		// 						{ value: "users.create.websites", name: "websites" },
		// 						{ value: "users.create.countries", name: "countries" },
		// 						{ value: "users.create.created_at", name: "created_at" },
		// 						{
		// 							value: "users.create.application",
		// 							name: "application",
		// 							children: [
		// 								{ value: "users.create.application.id", name: "id" },
		// 								{ value: "users.create.application.name", name: "name" },
		// 							],
		// 						},
		// 					],
		// 				},
		// 				{
		// 					value: "update",
		// 					name: "update",
		// 					children: [
		// 						{ value: 21, name: "id" },
		// 						{ value: 22, name: "name" },
		// 						{ value: 23, name: "email" },
		// 						{ value: 24, name: "password" },
		// 						{ value: 25, name: "role_id" },
		// 						{ value: 26, name: "permissions" },
		// 						{ value: 27, name: "websites" },
		// 						{ value: 28, name: "countries" },
		// 						{ value: 29, name: "created_at" },
		// 						{
		// 							value: "application",
		// 							name: "application",
		// 							children: [
		// 								{ value: 30, name: "id" },
		// 								{ value: 31, name: "name" },
		// 							],
		// 						},
		// 					],
		// 				},
		// 				{
		// 					value: "users.view.*",
		// 					name: "view",
		// 					children: [
		// 						{ value: 40, name: "id" },
		// 						{ value: 41, name: "name" },
		// 						{ value: 42, name: "email" },
		// 						//	{ value: 43, name: "password" },
		// 						{ value: 44, name: "role_id" },
		// 						{ value: 45, name: "permissions" },
		// 						{ value: 46, name: "websites" },
		// 						{ value: 47, name: "countries" },
		// 						{ value: 48, name: "created_at" },
		// 						{
		// 							value: "application",
		// 							name: "application",
		// 							children: [
		// 								{ value: 49, name: "id" },
		// 								{ value: 50, name: "name" },
		// 							],
		// 						},
		// 					],
		// 				},
		// 				{ value: "users.delete", name: "delete" },
		// 			],
		// 		},
		// 		{
		// 			id: "applications",
		// 			name: "applications",
		// 		},
		// 		{
		// 			id: "transactions",
		// 			name: "transactions",
		// 		},
		// 	];
		// 	resolve();
		// });
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		parsedPermissions() {
			// return only the permissions that has the searchModel string
			if (!this.searchModel) { return this.permissions }

			return this.permissions.filter(permission => permission.text.includes(this.searchModel?.toLowerCase()))
		},
	},
	methods: {
		selectAll() {
			const local = []
			this.parsedPermissions.forEach((permission) => {
				if (permission.value) {
					local.push(permission.value)
				}
			})
			this.localValue = local
		},
		unselectAll() {
			this.localValue = []
		},
	},
}
</script>
