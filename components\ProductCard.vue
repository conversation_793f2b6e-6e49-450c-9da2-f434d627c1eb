<template>
	<v-skeleton-loader type="card" :loading="computedLoading" :max-width="maxWidth" class="fill-height" :width="width">
		<v-card class="product-card d-flex flex-column mb-2 fill-height" :class="{ 'has-discount': hasDiscount }" flat
			hover :max-width="maxWidth" :width="width" :elevation="isHovered ? 5 : 0" v-bind="cardProps"
			@mouseenter="carouselStartHandler" @mouseleave="carouselStopHandler">
			<div>
				<div>
					<v-sheet color="accent13" class="image-wrapper">
						<v-carousel v-model="carouselModel" height="auto" :show-arrows="isHovered" :cycle="isHovered"
							:hide-delimiters="covers.length <= 1 || !isHovered" hide-delimiter-background>
							<template #next="{ on }">
								<v-btn v-show="covers.length > 1" icon v-on="on" @click.prevent>
									<v-icon>mdi-chevron-right</v-icon>
								</v-btn>
							</template>
							<template #prev="{ on }">
								<v-btn v-show="covers.length > 1" icon v-on="on" @click.prevent>
									<v-icon>mdi-chevron-left</v-icon>
								</v-btn>
							</template>
							<v-carousel-item v-for="(image, i) in covers" :key="i" class="pa-4" :eager="isHovered">
								<!-- <nuxt-picture provider="s3" sizes="xs:170px md:240px" format="webp" quality="80"
									:src="image.src" /> -->
								<v-img contain eager :aspect-ratio="4 / 3" :src="$img(image.src, {
									format: 'webp',
									fit: 'contain',
									background: 'transparent',
									sizes: 'xs:170px md:240px',
									quality: 90,
								}, { provider: 's3' })" />
							</v-carousel-item>
						</v-carousel>

						<div class="overlay">
							<div class="c-row">
								<div>
									<v-sheet v-if="hasDiscount" shaped dark class="discount-label">
										<div class="me-1">
											<span class="number enlarge px"> {{ discount }} % </span>
											<small>
												<count-down :date="discountExpiresIn" />
											</small>
											<!-- {{ $t('off') }} -->
										</div>
									</v-sheet>
									<!--<p-new v-if="data.isNew" class="mt-1" />-->
									<v-sheet v-if="isPreOrder" shaped dark class="pre-order-label">
										<div class="me-1">
											<span> {{ $t('product.pre-order') }}</span>
										</div>
									</v-sheet>
								</div>

								<div class="d-flex flex-column">
									<v-btn v-if="!$store.getters['wishlist/isWishlisted'](item.productId)" icon
										:loading="isWishlisting" @click.prevent="addToWishlist(item)">
										<v-icon> mdi-heart-outline</v-icon>
									</v-btn>
									<v-btn v-else icon @click.prevent="removeFromWishlist">
										<v-icon color="wishlist">
											mdi mdi-heart
										</v-icon>
									</v-btn>
									<!-- <v-tooltip v-if="categoryId" top>
										<template #activator="{ on }">
											<v-btn
												:loading="isComparing"
												:color="
													isAllowedToAddToComparison(categoryId)
														? hasComparisonItems
															? 'primary'
															: undefined
														: 'muted-4'
												"
												icon
												v-on="on"
												@click.prevent="addToComparison"
											>
												<v-icon> mdi-compare-horizontal</v-icon>
											</v-btn>
										</template>
										{{ $t("compare") }}
									</v-tooltip> -->
									<v-spacer />
								</div>
							</div>
							<v-spacer />
							<div class="c-row justify-end align-end">
								<!--<p-cashback v-if="data.cashback">
                <small>{{ data.currency }}</small> {{ data.cashback }}
 </p-cashback>-->
							</div>
						</div>
						<v-progress-linear :value="100" height="2" />
					</v-sheet>
				</div>

				<v-card-text>
					<div class="d-flex align-center pt-1">
						<!-- <div v-if="item.category" class="text--secondary text-subtitle-2">
							{{ item.category.name }}
						</div> -->
						<v-spacer />
						<v-rating :value="item.avgRate" background-color="default" dense readonly small color="warning"
							length="5" />
					</div>
					<div class="pb-1">
						<component :is="headingLevel" class="text-subtitle-1">
{{ item.name }}
</component>
						<!-- <div v-if="isTypeAlternative" class="text-subtitle-2  text--secondary">
							{{ item.variance.name }}
						</div> -->

						<!-- <small>{{ item.type }}</small> -->
					</div>
					<price-product :item="item" />
				</v-card-text>
			</div>
		</v-card>
	</v-skeleton-loader>
</template>

<script>
import { mapGetters } from "vuex"

export default {
	props: {
		item: {
			type: [Object, Number],
			default: () => ({}),
		},
		loading: {
			type: Boolean,
			default: false,
		},
		categoryId: {
			type: Number,
			default: 0,
		},
		width: {
			type: [Number, String],
			default: "100%",
		},
		maxWidth: {
			type: [Number, String],
			default: "100%",
		},
		heading: {
			type: String,
			default: "h3",
		},
	},

	data() {
		return {
			carouselModel: 0,
			isHovered: false,
			isWishlisting: false,
			isComparing: false,
		}
	},

	computed: {
		...mapGetters({
			isAllowedToAddToComparison: "comparison/isAllowedToAdd",
		}),
		varianceSlug() {
			if (this.isTypeBundle) {
				return this.item.slug
			} else {
				return this.item.variance?.slug
			}
		},
		headingLevel() {
			const levels = {
				h1: 'h1',
				h2: 'h2',
				h3: 'h3',
				h4: 'h4',
				h5: 'h5',
			}
			return levels[this.heading] || 'h3'
		},
		stock() {
			if (this.isTypeBundle) {
				return this.item.stock
			}

			if (this.isTypeAlternative || this.isTypeSimple) {
				return this.item.variance?.stock
			}

			return false
		},

		isPreOrder() {
			return !!this.stock && this.stock.isPreOrder
		},
		hasStock() {
			return !!this.stock && this.stock.quantity > 0
		},

		hasVariance() {
			return !!this.item?.variance && this.item?.variance?.name
		},

		hasDiscount() {
			if (this.hasStock) {
				return this.stock.isOffer
			}

			return false
		},
		hasComparisonItems() {
			return !!this.$store.state.comparison.items.length
		},

		discount() {
			if (this.hasDiscount) {
				return 100 - Math.round((this.stock?.price.value / this.stock?.priceBeforeOffer.value) * 100)
			}
			return 0
		},
		discountAmount() {
			if (this.hasStock && this.hasDiscount) {
				return {
					value: this.stock?.priceBeforeOffer?.value - this.stock?.price.value,
					currency: this.stock?.price.currency,
				}
			}
			return 0
		},
		discountExpiresIn() {
			if (this.hasDiscount) {
				return this.stock.unPublishedAt
			}
			return 0
		},

		price() {
			if (this.hasStock) {
				return this.stock.price
			}
			return null
		},
		priceObject() {
			if (this.hasStock) {
				const symbol = this.stock.price.currency
				const formatter = new Intl.NumberFormat(this.$i18n.localeProperties.iso, {
					// style: "number",
					minimumFractionDigits: 0,
					maximumFractionDigits: 2,
				})
				const value = formatter.format(this.stock.price.value)
				return { symbol, value }
			}

			return null
		},

		priceBeforeOffer() {
			return this.stock?.priceBeforeOffer.value
		},

		isTypeSimple() {
			return this.item.type === "simple"
		},
		isTypeBundle() {
			return this.item.type === "bundle"
		},
		isTypeAlternative() {
			return this.item.type === "alternative"
		},
		computedLoading() {
			return this.loading
		},
		covers() {
			return this.item?.media?.cover || this.item.variance?.media?.cover || []
		},
		cardProps() {
			if ((!this.item.slug || !this.item.variance?.slug) && !this.isTypeBundle) { return {} }
			return {
				to: this.localePath({
					name: "product",
					params: {
						productSlug: this.item.slug,
					},
				}),
				native: true,
			}
		},
	},
	methods: {
		carouselStartHandler() {
			this.isHovered = true
			if (process.browser) {
				setTimeout(() => {
					if (this.isHovered) { this.carouselModel = 1 }
				}, 200)
			}
		},
		carouselStopHandler() {
			this.isHovered = false

			if (process.browser) {
				setTimeout(() => {
					if (!this.isHovered) { this.carouselModel = 0 }
				}, 200)
			}
		},
		addToWishlist() {
			this.isWishlisting = true
			this.$store.dispatch("wishlist/add", this.item).finally(() => {
				this.isWishlisting = false
			})
		},
		addToComparison() {
			// let isConfirmed = true;
			// if (!this.isAllowedToAddToComparison(this.categoryId)) {
			// 	isConfirmed = false;
			// 	isConfirmed = await this.$confirm(
			// 		"You can compare products from the same category. Do you want to clear the list and add this item?",
			// 		{
			// 			// confirmButtonText: "Remove",
			// 			// cancelButtonText: "Cancel",
			// 			title: "Comparison can't be done!",
			// 		}
			// 	).then((isAccepted) => {
			// 		if (isAccepted) {
			// 			this.$store.dispatch("comparison/removeAll");
			// 		}

			// 		return isAccepted;
			// 	});
			// }
			// if (isConfirmed) {
			this.isComparing = true
			this.$store
				.dispatch("comparison/add", {
					productSlug: this.item.slug,
					varianceSlug: this.varianceSlug,
				})
				.finally(() => {
					this.isComparing = false
				})
			// }
		},
		removeFromWishlist() {
			this.isWishlisting = true
			const id = this.item.productId
			this.$store.dispatch("wishlist/remove", this.item).finally(() => {
				this.isWishlisting = false
				this.$emit("wishlist:remove", id)
			})
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/variables.scss";

.product-card {
	.image-wrapper {
		position: relative;
		overflow: hidden;

		img {
			//	filter: $product-drop-shadow;
			padding: 16px 0;
			margin-inline: auto;
			display: block;
		}

		.overlay {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			flex-direction: column;

			.c-row {
				padding: 8px;
				display: flex;
				justify-content: space-between;
				flex: content;
			}
		}
	}

	.price-wrapper {
		.value {
			font-size: 20px !important;
		}
	}

	&.has-discount {
		.image-wrapper {
			img {
				padding-top: 36px;
			}
		}
	}

	.v-carousel__controls__item.v-btn {
		color: #000 !important;

		.v-icon {
			width: 18px;
			height: 18px;
			margin: 0 4px !important;
			//opacity: 0.9;
		}
	}
}

// .pre-order-label {
// 	background-color: red !important; // Choose your pre-order label color
// 	color: white !important;
// 	padding: 8px;

// 	padding: 2px 8px;
// 	border-radius: 4px;
// 	font-size: 14px;
// 	font-weight: bold;
// 	color: #fff;
// 	display: inline-block;
// 	position: absolute;
// 	top: 10px; // Adjust the position to your needs
// 	left: 10%; // Centers the label horizonta

// 	.pre-order-text {
// 		color: white;
// 		font-weight: bold;
// 		text-transform: uppercase;
// 		font-size: 12px;
// 	}
// }</style>
