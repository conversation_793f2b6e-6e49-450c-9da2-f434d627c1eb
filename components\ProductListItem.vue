<template>
	<v-skeleton-loader type="list-item" :loading="loading" class="fill-height" :width="width">
		<v-list-item link>
			<div class="flex-grow-0 flex-shrink-0 me-3">
				<v-img width="80" contain :src="media.covers[0].url" />
			</div>
			<v-list-item-content>
				<v-list-item-title> Samsung A31 German Volkman </v-list-item-title>
				<v-list-item-subtitle> Samsung A31 German Volkman </v-list-item-subtitle>
				<v-list-item-subtitle>
					<span class="price--text">{{ 500 | money }}</span>
				</v-list-item-subtitle>
			</v-list-item-content>
		</v-list-item>
	</v-skeleton-loader>
</template>

<script>
import { mapGetters } from "vuex"

export default {
	props: {
		item: {
			type: [Object, Number],
			default: () => ({}),
		},
		loading: {
			type: Boolean,
			default: false,
		},
		categoryId: {
			type: Number,
			default: 0,
		},
	},

	data() {
		return {
			carouselModel: 0,
			isHovered: false,
			media: {
				covers: [
					{
						url: "/images/demo1.png",
					},
					{
						url: "/images/demo2.png",
					},
					{
						url: "/images/demo1.png",
					},
				],
			},
			isWishlisting: false,
			isComparing: false,
		}
	},

	computed: {
		...mapGetters({
			isAllowedToAddToComparison: "comparison/isAllowedToAdd",
		}),
		varianceSlug() {
			if (this.isTypeBundle) {
				return this.item.bundle.slug
			} else {
				return this.item.default?.slug
			}
		},
		stock() {
			if (this.isTypeBundle) {
				return this.item.bundle.stock
			}

			if (this.isTypeAlternative || this.isTypeSimple) {
				return this.item.default?.stock
			}

			return false
		},
		hasStock() {
			return this.stock && this.stock.quantity > 0
		},

		hasDiscount() {
			if (this.hasStock) {
				return this.stock.isOffer
			}

			return false
		},
		hasComparisonItems() {
			return !!this.$store.state.comparison.items.length
		},

		discount() {
			if (this.hasDiscount) {
				return 100 - Math.round((this.stock.price.value / this.stock.priceBeforeOffer) * 100)
			}
			return 0
		},

		price() {
			if (this.hasStock) {
				return this.stock.price
			}
			return null
		},

		priceBeforeOffer() {
			return this.stock.priceBeforeOffer
		},

		isTypeSimple() {
			return this.item.type === "simple"
		},
		isTypeBundle() {
			return this.item.type === "bundle"
		},
		isTypeAlternative() {
			return this.item.type === "alternative"
		},
	},
	methods: {
		carouselStartHandler() {
			this.isHovered = true
			if (process.browser) {
				setTimeout(() => {
					if (this.isHovered) { this.carouselModel = 1 }
				}, 200)
			}
		},
		carouselStopHandler() {
			this.isHovered = false
			if (process.browser) {
				setTimeout(() => {
					if (!this.isHovered) { this.carouselModel = 0 }
				}, 200)
			}
		},
		addToWishlist() {
			this.isWishlisting = true
			this.$store.dispatch("wishlist/add", this.item).finally(() => {
				this.isWishlisting = false
			})
		},
		addToComparison() {
			// let isConfirmed = true;
			// if (!this.isAllowedToAddToComparison(this.categoryId)) {
			// 	isConfirmed = false;
			// 	isConfirmed = await this.$confirm(
			// 		"You can compare products from the same category. Do you want to clear the list and add this item?",
			// 		{
			// 			// confirmButtonText: "Remove",
			// 			// cancelButtonText: "Cancel",
			// 			title: "Comparison can't be done!",
			// 		}
			// 	).then((isAccepted) => {
			// 		if (isAccepted) {
			// 			this.$store.dispatch("comparison/removeAll");
			// 		}

			// 		return isAccepted;
			// 	});
			// }
			// if (isConfirmed) {
			this.isComparing = true
			this.$store.dispatch("comparison/add", { productSlug: this.item.slug, varianceSlug: this.varianceSlug }).finally(() => {
				this.isComparing = false
			})
			// }
		},
		removeFromWishlist() {
			this.isWishlisting = true
			const id = this.item.productId
			this.$store.dispatch("wishlist/remove", this.item).finally(() => {
				this.isWishlisting = false
				this.$emit("wishlist:remove", id)
			})
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/variables.scss";

.product-card {
	.image-wrapper {
		position: relative;
		overflow: hidden;

		img {
			//	filter: $product-drop-shadow;
			padding: 16px 0;
			margin-inline: auto;
			display: block;
		}

		.overlay {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			flex-direction: column;

			.c-row {
				padding: 8px;
				display: flex;
				justify-content: space-between;
				flex: content;
			}
		}
	}

	.price-wrapper {
		.value {
			font-size: 20px !important;
		}
	}

	&.has-discount {
		.image-wrapper {
			img {
				padding-top: 36px;
			}
		}
	}

	.v-carousel__controls__item.v-btn {
		color: #000 !important;

		.v-icon {
			width: 18px;
			height: 18px;
			margin: 0 4px !important;
			//opacity: 0.9;
		}
	}
}
</style>
