<template>
	<div>
		<slot name="activator" v-bind="{ on }" />
		<v-dialog v-model="dialogModel" max-width="800">
			<v-card>
				<v-card-title> Select Product </v-card-title>
				<v-card-text>
					<v-toolbar flat>
						<v-text-field
							v-model="search"
							append-icon="mdi-magnify"
							label="Search"
							single-line
							:loading="$fetchState.pending"
							hide-details
							@keydown.enter="$fetch"
							@input="debouncedFetch"
						/>
						<v-spacer />
					</v-toolbar>
					<v-divider />

					<transition-group name="list" class="row" tag="div">
						<v-col v-for="product in products" :key="product.variance.varianceId" cols="12" md="3">
							<admin-product-card :item="product" @click.stop="selectProduct(product)" />
						</v-col>
					</transition-group>
				</v-card-text>
			</v-card>
		</v-dialog>
	</div>
</template>

<script>
import debounce from "debounce"

export default {
	props: {
		inStock: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			dialogModel: false,
			products: [],
			cancelToken: null,
			pagination: {},
			search: "",
		}
	},
	async fetch() {
		if (this.cancelToken && typeof this.cancelToken.cancel === "function") {
			this.cancelToken.cancel()
		}
		this.cancelToken = this.$axios.CancelToken.source()
		await this.$axios
			.$get(`/v1/products`, {
				params: {
					orderBy: this.orderBy,
					q: this.search,
				},
				cancelToken: this.cancelToken.token,
			})
			.then((resp) => {
				this.products = resp.items
				// this.filters = resp.filters;
				this.pagination = resp.pagination
			})
			.catch(() => {
				this.products = []
				this.hasError = true
			})
	},
	computed: {
		on() {
			return {
				click: this.open,
			}
		},
	},
	methods: {
		open() {
			this.dialogModel = true
		},
		close() {
			this.dialogModel = false
		},
		selectProduct(product) {
			// update v-model
			this.$emit("input", product)
			this.close()
		},
		debouncedFetch() {
			return debounce(this.$fetch, 500)()
		},
	},
}
</script>
