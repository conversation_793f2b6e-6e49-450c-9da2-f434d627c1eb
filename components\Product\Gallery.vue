<template>
	<div class="product-gallery">
		<!-- <v-responsive :aspect-ratio="4 / 3"> -->
		<v-carousel v-model="carouselModel" show-arrows hide-delimiter-background hide-delimiters height="auto">
			<v-carousel-item v-for="(item, index) in items" :key="`galley-${index}`" :eager="!index" class="test">
				<v-sheet class="rounded-xl text-center" width="100%" height="100%">
					<v-img :aspect-ratio="4 / 3" :eager="!index" contain :src="$img(item.preview,
						{
							format: 'webp',
							fit: 'contain',
							//width: 540, height: 405,
							// width, height,
							background: 'transparent',
							sizes: 'xs:100vw md:00px',
						},
						{
							provider: 's3'
						}
					)" />
					<!-- <nuxt-img provider="s3" :src="item.src" sizes="xs:100vw md:600px" format="webp" width="auto"
							max-width="100%" /> -->
					<!-- <client-only>
							<zoom-on-hover class="rounded-xl" :alt-text="item.description" :disabled="false" :img-normal="$img(item.src,
								{
									format: 'webp',
									fit: 'contain',
									//width: 540, height: 405,
									width, height,
									background: 'transparent',
									sizes: 'xs:100vw md:400px',
								},
								{
									provider: 's3'
								}
							)
								" :img-zoom="$img(item.src,
									{
										format: 'webp',
										fit: 'contain',
										background: 'transparent',
									},
									{
										provider: 's3'
									}
								)
									" />
						</client-only> -->
				</v-sheet>
			</v-carousel-item>
		</v-carousel>
		<!-- </v-responsive> -->
		<div class="mt-4 mx-auto">
			<v-slide-group v-model="carouselModel" center-active mandatory>
				<v-slide-item v-for="(item, index) in items" :key="`thumbnail-${index}`" v-slot="{ active, toggle }">
					<v-card outlined :color="active ? 'primary' : ''" class="ma-4 pa-1" height="75" width="100"
						@click="toggle">
						<v-sheet color="white" rounded class="fill-height d-flex align-center justify-center">
							<v-img max-height="100%" contain :src="$img(item.preview, {
								format: 'webp',
								fit: 'contain',
								quality: 70,
								width: 100,
								height: 75,
								background: 'transparent',
							},
								{
									provider: 's3'
								}
							)
								" />
						</v-sheet>
					</v-card>
				</v-slide-item>
			</v-slide-group>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		items: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			carouselModel: 0,
		}
	},
}
</script>
<style>
/* stylelint-disable selector-class-pattern */

.product-gallery .v-slide-group__content {
	justify-content: center !important;
}

/* .product-gallery .zoom-on-hover img {
	max-width: auto !important;
	max-height: auto !important;
} */
</style>
