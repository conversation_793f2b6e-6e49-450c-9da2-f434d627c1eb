<template>
	<v-skeleton-loader type="paragraph@4" :loading="isFetching">
		<v-row>
			<v-col cols="12">
				<h2>{{ $t("product.customer-ratings") }}</h2>
			</v-col>
			<v-col cols="12" md="6">
				<div class="mb-8 ms-n2">
					<div class="d-flex align-center">
						<!-- ADD AVERAGE RATING OUT OF 5 HERE -->
						<v-rating readonly :value="4.7" color="gold" class="me-4" />
						<b>{{ ratings.avgRating }} {{ $t("product.rate-out-of") }} 5</b>
					</div>
					<!-- ADD TOTAL RATING HERE -->
				</div>

				<v-sheet class="d-flex flex-column justify-space-between" height="200">
					<div v-for="(rating, r) in ratings.ratings" :key="r" class="d-flex align-center">
						<b class="me-4 flex-shrink-0">{{ rating.rating }}</b>
						<v-progress-linear color="gold" height="5" :value="rating.percentage" class="me-4" />
						<span class="muted-2 me-2 flex-shrink-0">{{ rating.percentage }}%</span>
						<!-- ADD TOTAL RATING HERE -->
						<span class="muted-2 flex-shrink-0 text-subtitle-2">{{ rating.totalReviews }} {{ $t("product.reviews")
							}}</span>
					</div>
				</v-sheet>
			</v-col>
			<v-divider vertical class="my-4" />
			<!-- All reviews section -->
			<v-col id="write-a-review" cols="12" md="6">
				<v-form>
					<v-card v-for="(rating, ind) in ratings.ratings" :key="ind" flat max-width="600">
						<div v-if="rating.reviews">
							<div v-for="(review, i) in rating.reviews" :key="i">
								<v-card-subtitle>
									<avatar :width="20" :aspect-ratio="4 / 3"
										:src="review?.user?.media?.avatar?.src || '/images/default-avatar.jpg'" />
									<span class="ml-4">{{ review?.user?.firstName }}</span>
								</v-card-subtitle>
								<v-rating small color="gold" readonly :value="rating.rating" />
								<v-card-title class="pl-4">
									{{ review?.review }}
								</v-card-title>
								<v-divider max-width="auto" />
							</div>
						</div>
					</v-card>
				</v-form>

				<!-- submit a review -->
				<v-form ref="form" @submit.prevent="submitReview">
					<v-card flat max-width="800">
						<v-card-title> {{ $t("product.write-your-review") }} </v-card-title>
						<v-rating v-model="reviewForm.rating" class="px-2" />
						<v-card-text>
							<v-text-field disabled :label="$t('users.full-name')" outlined
								:rules="[$rules.required($t('users.full-name'))]"
								:value="(!$auth.loggedIn) ? '' : $auth?.user?.firstName + ' ' + $auth?.user?.lastName" />
							<v-textarea v-model="reviewForm.review" :rules="[$rules.required($t('product.your-review'))]"
								:label="$t('product.your-review')" outlined />
						</v-card-text>
						<v-card-actions>
							<v-btn type="submit" color="primary" large block max-width="400">
								{{ $t("product.submit-review") }}
							</v-btn>
						</v-card-actions>
					</v-card>
				</v-form>
			</v-col>
		</v-row>
	</v-skeleton-loader>
</template>

<script>
export default {
	fetchOnServer: false,
	props: {
		id: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			ratings: {},
			reviewForm: {
				rating: 0,
				review: "",
			},
		}
	},
	async fetch() {
		await this.$axios.$get(`/v1/ratings/${this.id}`).then((res) => {
			this.ratings = res
		})
	},
	methods: {
		submitReview() {
			if (!this.$auth.loggedIn) {
				this.$nuxt.$emit("auth.show", "login")
			}

			if (!this.$refs.form.validate()) { return }

			this.$axios
				.post(`/v1/products/${this.id}/rating`, this.reviewForm)
				.then((resp) => {
					this.$toast.success(`${this.$t("common.review-submit-on-product-success")}`)
					this.reviewForm = {
						rating: 0,
						review: "",
					}
				})
				.catch((e) => {
					console.log("error in POST:productSlug/rating", e)
					this.$toast.warning(`${this.$t("common.review-submit-on-product-failed")}`)
				})
				.finally(() => {
					this.$refs.form.reset()
					this.$refs.reviewForm.reset()
					this.reviewForm = {
						rating: 0,
						review: "",
					}
				})
		},
	},
}
</script>
