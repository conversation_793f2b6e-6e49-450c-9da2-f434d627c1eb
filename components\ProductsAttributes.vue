<template>
	<v-card :loading="isFetching" min-height="100" flat>
		<!-- show overlay when loading -->
		<vc-label for="">
			Attributes
		</vc-label>
		<v-alert v-if="!categories.length" type="warning" text>
			Please select a category first
		</v-alert>
		<template v-else>
			<div v-for="item in attributeItems" :key="item.id" class="d-flex align-baseline">
				<component :is="`attributes-insert-${item.type}`" :value="item.value" :disabled="isFetching"
					:name="item.name" :options="item.options" :prefix="item.prefix" :extra="item.extra" :suffix="item.suffix"
					item-text="name" item-value="attributeOptionId" :multiple="item.multiple" class="flex-grow-1 mb-2"
					clearable @input="inputHandler($event, item.attributeId)"
					@extra="extraHandler($event, item.attributeId)">
					{{ item.name }}<v-divider />
				</component>
				<v-btn v-if="hasVariation" text @click="setAsVariance(item)">
					Set As Variance
				</v-btn>
			</div>
			<v-alert v-if="!attributeItems.length" text type="info" dense border="left">
				No attributes found.
			</v-alert>
			<template v-if="hasVariation">
				<vc-label for="">
					Variation Attributes
				</vc-label>
				<p>You can generate variances based on below attributes in "Variances" tab.</p>
				<div v-for="item in variationAttributesItems" :key="item.id" class="d-flex align-baseline">
					<v-list-item>
						<v-list-item-content>
							<v-list-item-title class="text--primary">
								{{ item.name }}
							</v-list-item-title>
						</v-list-item-content>
						<v-list-item-action>
							<v-btn text class="ms-2" @click="removeAsVariance(item)">
								Remove Variation
							</v-btn>
						</v-list-item-action>
					</v-list-item>
				</div>
				<v-alert v-if="!variationAttributesItems.length" text type="info" dense border="left">
					No variation attributes found.
				</v-alert>
			</template>
		</template>
	</v-card>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => [],
		},
		categories: {
			type: [Array],
			default: () => [],
		},
		variationAttributes: {
			type: [Array],
			default: () => [],
		},
		hasVariation: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			items: [],
		}
	},
	async fetch() {
		if (this.categories.length) {
			this.items = []
			return await this.$axios
				.$get("/v1/admin/products/attributes", {
					params: {
						categories: this.categories,
					},
				})
				.then((res) => {
					// TBD: not working, when changing categories all values are lost
					// preserve old values
					const oldItems = this.$cloneDeep(this.items)
					this.items = res
					// set value to items
					this.items.forEach((item) => {
						// set items values from old items
						const oldItem = oldItems.find(v => v.attributeId === item.attributeId)
						if (oldItem) {
							item.value = oldItem.value
						}
						const value = this.value.find(v => v.attributeId === item.attributeId)
						if (value) {
							item.value = value.attributeOptionId
						}
					})
				})
				.catch((error) => {
					console.log(error)
				})
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		localVariationAttributes: {
			get() {
				return this.variationAttributes
			},
			set(value) {
				this.$emit("update:variationAttributes", value)
			},
		},
		attributeItems() {
			console.log("this.items", this.items)
			return this.items.filter((item) => {
				return !this.localVariationAttributes.includes(item.attributeId)
			})
		},
		variationAttributesItems() {
			return this.items.filter((item) => {
				return this.localVariationAttributes.includes(item.attributeId)
			})
		},
	},
	watch: {
		categories: {
			handler(val) {
				this.$fetch()
			},
			deep: true,
		},
	},
	methods: {
		inputHandler(value, attributeId) {
			// if (!value) { return }

			console.log("inputHandler index", attributeId, value)
			const i = this.items.findIndex((v) => {
				return v.attributeId === attributeId
			})
			if (i > -1) {
				this.$set(this.items[i], "value", value)
			} else {
				alert(`attributeId ${attributeId} not found`)
			}

			this.update()
		},
		extraHandler(value, attributeId) {
			const i = this.items.findIndex((v) => {
				return v.attributeId === attributeId
			})
			if (i > -1) {
				this.$set(this.items[i], "extra", value)
			} else {
				alert(`attributeId ${attributeId} not found`)
			}

			this.update()
		},
		update() {
			// filter out which doesn't have value, then map array to attributeId and attributeOptionId
			const items = this.items
				.filter(item => item.value)
				.map(item => ({
					attributeId: item.attributeId,
					attributeOptionId: item.value,
					extra: item.extra,
				}))
			this.localValue = items
		},
		mapComponent(type) {
			switch (type) {
				case "select":
					return "vc-select"
				case "chips":
					return "vc-chips"
				case "radio":
					return "vc-radio"
				case "checkbox":
					return "vc-checkbox"
				case "text":
					return "vc-text-field"
				case "textarea":
					return "vc-textarea"
				case "date":
					return "vc-date-picker"
				case "time":
					return "vc-time-picker"
				case "datetime":
					return "vc-datetime-picker"
				default:
					return "vc-text-field"
			}
		},
		setAsVariance(item) {
			this.localVariationAttributes.push(item.attributeId)
		},
		removeAsVariance(item) {
			const i = this.localVariationAttributes.findIndex(v => v === item.attributeId)
			this.localVariationAttributes.splice(i, 1)
		},
	},
}
</script>
