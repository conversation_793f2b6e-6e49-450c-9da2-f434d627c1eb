<template>
	<v-banner v-model="bannerModel" class="comparison-banner" app color="#fff">
		<!-- <v-icon slot="icon" color="warning" size="36"> mdi-wifi-strength-alert-outline c</v-icon> -->
		<v-container>
			<v-row no-gutters align="center" class="flex-nowrap">
				<v-col cols="auto">
<v-subheader class="title">
Compare products
</v-subheader>
</v-col>
				<v-col>
					<v-row class="overflow-x-scroll flex-nowrap">
						<v-col v-for="(item, itemIndex) in items" :key="itemIndex" cols="auto">
							<v-sheet width="350">
								<v-list-item link>
									<v-img class="me-2" src="/images/demo1.png" max-width="40" />
									<v-list-item-content>
										<v-list-item-title> {{ item.name }}</v-list-item-title>
										<v-list-item-subtitle class="text--price">
500 JOD
</v-list-item-subtitle>
									</v-list-item-content>
									<v-list-item-action>
										<v-btn icon @click="remove(item)">
											<v-icon>mdi mdi-close</v-icon>
										</v-btn>
									</v-list-item-action>
								</v-list-item>
							</v-sheet>
						</v-col>
					</v-row>
				</v-col>
			</v-row>
		</v-container>
		<template #actions>
			<v-btn text :disabled="!$store.state.comparison.items.length" @click="confirmRemoveAll">
Clear
</v-btn>
			<v-btn color="primary" text :disabled="items.length < 2">
Compare
</v-btn>
		</template>
	</v-banner>
</template>

<script>
import { mapActions } from "vuex"
export default {
	data() {
		return {
			bannerModel: false,
		}
	},
	computed: {
		items() {
			return this.$store.state.comparison.items
		},
		count() {
			return this.items.length
		},
	},
	watch: {
		count(now, before) {
			if (before === 0 && now > 0) {
				this.bannerModel = true
			} else if (now === 0) {
				this.bannerModel = false
			}
		},
		bannerModel(now) {
			this.$emit("update:bannerModel", now)
		},
	},
	mounted() {
		this.$nuxt.$on("comparison:show", this.open)
		this.$nuxt.$on("comparison:hide", this.close)
		this.$nuxt.$on("comparison:add", this.add)
		this.$nuxt.$on("comparison:remove", this.remove)
		this.topPositionAdjuster()
	},
	methods: {
		...mapActions("comparison", { add: "add", remove: "remove", removeAll: "removeAll" }),
		confirmRemoveAll() {
			this.$confirm("Are you sure you want to clear the comparison list?").then((x) => {
				if (x) {
					this.removeAll()
				}
			})
		},
		topPositionAdjuster() {
			// Select the .main-header element
			const mainHeader = document.querySelector(".main-header")

			// Create a new MutationObserver
			const observer = new MutationObserver((x) => {
				// console.log("🚀 ~ file: ProductsComparison.vue:79 ~ observer ~ x:", x);
				// Retrieve the current translateY value from the element's transform property
				const transformValue = window.getComputedStyle(mainHeader).getPropertyValue("transform")
				const translateY = transformValue.split(",")[5].trim()

				// Convert the translateY value to pixels
				const translateYInPixels = parseInt(translateY)

				// Adjust the top value based on the translateY value
				const adjustedTop = 64 - translateYInPixels + "px"
				// console.log("🚀 ~ file: ProductsComparison.vue:89 ~ observer ~ adjustedTop:", adjustedTop);

				// Update the top value of your target element
				// Replace 'target-element' with the appropriate selector for your target element
				const targetElement = document.querySelector(".comparison-banner")
				targetElement.style.top = adjustedTop
			})

			// Start observing changes in the .main-header element's style
			observer.observe(mainHeader, { attributes: true, attributeFilter: ["style"] })
		},
	},
}
</script>

<style lang="scss">
// .main-header.v-app-bar--is-scrolled ~ .comparison-banner {
// 	top: 64px !important;
// 	transition: top 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
// }
.comparison-banner {
	transition: top 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
	z-index: 2;
}
</style>
