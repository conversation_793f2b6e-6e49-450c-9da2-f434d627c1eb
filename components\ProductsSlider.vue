<template>
	<div class="my-4">
		<div class="d-flex justify-space-between">
			<h2 class="font-weight-light">
				<template v-if="toRoute">
					<nuxt-link :to="toRoute" class="text-decoration-none inherit--text">
						{{ title }}
					</nuxt-link>
				</template>
				<template v-else>
					{{ title }}
				</template>
			</h2>
			<nuxt-link v-if="toRoute" :to="toRoute" class="text-decoration-none inherit--text">
				{{ $t("common.see-more") }} <v-icon class="rtl-rotate" right>
					mdi-chevron-right
				</v-icon>
			</nuxt-link>
		</div>
		<v-slide-group class="hide-disabled-arrows">
			<v-slide-item v-for="(item, i) in items" :key="i" class="me-3 pa-2">
				<product-card :loading="loading" :item="item" max-width="250" width="250" />
			</v-slide-item>
		</v-slide-group>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			required: true,
		},
		items: {
			type: Array,
			required: true,
		},
		loading: {
			type: <PERSON><PERSON>an,
			default: false,
		},
		to: {
			type: String,
			default: "",
		},
	},
	computed: {
		toRoute() {
			return this.to ? this.to : undefined
		},
	},
}
</script>
