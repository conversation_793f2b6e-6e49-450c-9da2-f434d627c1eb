<template>
	<v-form class="d-flex">
		<div v-if="!isEdit">
{{ requestName.firstName }} {{ requestName.lastName }}
</div>
		<div v-else>
			<v-form ref="requestName" class="d-flex">
				<vc-text-field
					v-model="requestName.firstName"
					:label="$t('common.first-name')"
					:rules="[$rules.required($t('common.first-name'))]"
					class="me-3"
				/>

				<vc-text-field
					v-model="requestName.lastName"
					:label="$t('common.last-name')"
					:rules="[$rules.required($t('common.last-name'))]"
				/>
			</v-form>
		</div>
		<v-spacer />
		<v-btn v-if="!isEdit" color="primary" class="mb-2" text @click.prevent="isEdit = true">
{{ $t("common.edit") }}
</v-btn>
		<template v-else>
			<v-btn text @click.prevent="isEdit = false">
				{{ $t("common.close") }}
			</v-btn>
			<v-btn color="primary" text type="submit" :loading="loading" @click.prevent="isEdit = false" @submit.prevent="updateName">
				{{ $t("common.save") }}
			</v-btn>
		</template>
	</v-form>
</template>

<script>
export default {
	data() {
		return {
			isEdit: false,
			requestName: {
				firstName: this.$auth.user.firstName,
				lastName: this.$auth.user.lastName,
			},
			loading: false,
			dialogName: false,
		}
	},

	methods: {
		updateName() {
			if (!this.$refs.requestName.validate()) {
				this.scrollToError()
				return
			}
			this.loading = true
			this.$axios
				.$post(`v1/my/request-change`, this.requestName)
				.then(() => {
					this.requestName = ""
					this.$toast.success(`${this.$t("common.the-name-will-be-changed-upon-approval-of-the-application-by-the-manager")}`)
					this.closeNameDialog()
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.requestName)
				})
				.finally(() => {
					this.loading = false
				})
		},
	},
}
</script>
