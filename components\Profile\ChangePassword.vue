<template>
	<div>
		<div class="d-flex justify-space-between">
			<div class="mt-1 mx-4">
				<p>{{ $t("common.password") }}</p>
			</div>
			<div class="pb-4">
				<v-btn v-if="isButtonActive === false" color="primary" text @click="togglePassword">
					{{ $t("common.change-password") }}
				</v-btn>
			</div>
		</div>
		<v-form v-if="isActive" ref="password" class="mx-6 mt-4" @submit.prevent="updatePassword">
			<div class="d-flex justify-space-between">
				<v-row>
					<v-col cols="12" lg="6">
						<vc-password
							v-model="password.password"
							name="password"
							no-strength
							:rules="[$rules.required($t('common.password')), $rules.minLength($t('common.password'), 8)]"
							:label="$t('common.password')"
						/>
					</v-col>

					<v-col cols="12" lg="6">
						<vc-password
							v-model="password.passwordConfirmation"
							name="passwordConfirmation"
							no-strength
							:rules="[
								$rules.required($t('common.password-confirmation')),
								$rules.minLength($t('common.password-confirmation'), 8),
							]"
							:label="$t('common.password-confirmation')"
						/>
					</v-col>
				</v-row>
				<div v-if="isButtonActive === true" class="d-flex">
					<v-btn text @click="togglePassword">
{{ $t("common.cancel") }}
</v-btn>
					<v-btn color="primary" text type="submit" :loading="loading">
{{ $t("common.save") }}
</v-btn>
				</div>
			</div>
		</v-form>
	</div>
</template>

<script>
export default {
	data() {
		return {
			password: {
				password: "",
				passwordConfirmation: "",
			},
			loading: false,
			isActive: false,
			isButtonActive: false,
		}
	},

	methods: {
		togglePassword() {
			this.isActive = !this.isActive
			this.isButtonActive = !this.isButtonActive
		},

		updatePassword() {
			if (!this.$refs.password.validate()) {
				this.scrollToError()
				return
			}
			this.loading = true
			this.$axios
				.$put(`v1/my/update-password`, this.password)
				.then(() => {
					this.password = {
						password: "",
						passwordConfirmation: "",
					}
					this.$toast.success(`${this.$t("common.password-has-been-updated-successfully")}`)
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.password)
				})
				.finally(() => {
					this.loading = false
				})
		},
	},
}
</script>
