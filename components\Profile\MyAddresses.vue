<template>
	<v-card outlined>
		<v-card-text>
			<v-list-item three-line>
				<v-list-item-content>
					<v-list-item-title v-if="item.default == 1" class="mb-2">
						<v-badge color="primary" class="ml-2" max-width="50" :content="$t('common.default')"
							transition="slide-x-transition" />
					</v-list-item-title>
					<v-list-item-title class="mb-2">
						{{ item.recipientName }}
					</v-list-item-title>
					<v-list-item-title class="mb-2">
						{{ item.phone.code }} {{ item.phone.number }}
					</v-list-item-title>
					<v-list-item-title class="mb-2">
						{{ item.district }}
					</v-list-item-title>
					<v-list-item-title class="mb-2">
						{{ item.street }}
					</v-list-item-title>
					<v-list-item-title class="mb-2">
						{{ item.apartmentNumber }}
					</v-list-item-title>
					<v-list-item-title class="mb-2">
						{{ item.buildingNumber }}
					</v-list-item-title>
				</v-list-item-content>
			</v-list-item>
		</v-card-text>
		<v-card-actions>
			<v-btn text :loading="isDeleting" @click="deleteAddress(item.addressId)">
				<v-icon small>
					mdi-delete
				</v-icon>
				<span>{{ $t("common.delete") }}</span>
			</v-btn>
			<v-spacer />

			<v-btn color="primary" :loading="isLoading" outlined :disabled="item.default === 1"
				@click="setDefaultAddress(item)">
				<v-icon small>
					mdi-pencil
				</v-icon>

				{{ $t("common.default-address") }}
			</v-btn>
			<v-btn color="primary" :to="localePath({
				name: 'address-edit',
				params: {
					id: item.addressId,
				},
			})
				">
				<v-icon small>
					mdi-pencil
				</v-icon>

				{{ $t("common.edit") }}
			</v-btn>
		</v-card-actions>
	</v-card>
</template>

<script>
export default {
	props: {
		item: {
			type: [Object],
			default: () => ({}),
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			isDeleting: false,
			isLoading: false,
			disabled: true,
		}
	},
	methods: {
		setDefaultAddress(address) {
			this.isLoading = true
			return this.$axios
				.$put("v1/my/addresses/set-default-address/" + address.addressId)
				.then(() => {
					this.$toast.success(`${this.$t("common.has-been-set-default-address-successfully")}`)
					this.$emit("fetchAddress")
				})
				.catch((error) => {
					console.log(error)
				})
				.finally(() => {
					this.isLoading = false
				})
		},

		deleteAddress(addressId) {
			this.$confirm(this.$t("messages.confirm-delete-text"), {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					this.isDeleting = true
					this.$axios
						.$delete(`v1/my/addresses/${addressId}`)
						.then((res) => {
							this.isDeleting = false
							this.$toast.success(this.$t("messages.has-been-deleted-successfully"))
							this.$emit("deleted", addressId)
						})
						.catch((e) => {
							this.genericErrorHandler(e)
						})
				}
			})
		},
	},
}
</script>
