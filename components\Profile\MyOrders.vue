<template>
	<v-list-item two-line>
		<!-- <v-list-item-avatar v-for="(image, i) in item.product.media.cover" :key="i" tile size="80"> -->
		<v-list-item-avatar tile size="80">
			<!-- <img :src="'/images/demo1.png'" height="200" /> -->
			<img :src="item.product?.media?.cover[0]?.src || item.product?.media?.gallery[0]?.src || '/images/demo1.png'"
				height="200">
		</v-list-item-avatar>
		<v-list-item-content>
			<v-list-item-title>{{ item?.product?.name }} </v-list-item-title>
			<v-list-item-subtitle> {{ $t("common.order-price") }}{{ item.price?.value | money }} </v-list-item-subtitle>
			<v-list-item-subtitle> {{ $t("common.order-quantity") }} {{ item.quantity }}</v-list-item-subtitle>
		</v-list-item-content>
	</v-list-item>
</template>

<script>
export default {
	props: {
		item: {
			type: [Object],
			default: () => ({}),
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			media: {
				covers: [
					{
						url: "/images/demo1.png",
					},
				],
			},
		}
	},
}
</script>
