<template>
	<v-card flat class="d-flex justify-space-between">
		<div class="d-flex">
			<v-btn text color="primary" @click="openDialogPicture">
{{ $t("common.upload") }}
</v-btn>
		</div>

		<v-dialog v-model="dialogPicture" max-width="300" max-height="300">
			<v-card>
				<v-card-title>
					<span class="text-h5"> {{ $t("common.upload-image") }} </span>
				</v-card-title>

				<upload v-model="avatar" :max="1" width="278" height="270" />

				<v-card-actions>
					<!-- <v-spacer></v-spacer> -->
					<v-btn color="blue darken-1" text @click="closeDialogPicture()">
						{{ $t("common.close") }}
					</v-btn>
					<v-btn :loading="isUploading" :disabled="!avatar" color="blue darken-1" text @click="uploadProfileImage()">
						{{ $t("common.upload") }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</v-card>
</template>

<script>
export default {
	data() {
		return {
			avatar: null,
			dialogPicture: false,
			isUploading: false,
		}
	},
	methods: {
		uploadProfileImage() {
			this.isUploading = true
			this.$axios
				.$post(`v1/my/update-image`, {
					media: this.avatar,
				})
				.then((res) => {
					this.$toast.success(`${this.$t("common.has-been-updated-successfully")}`)
					this.closeDialogPicture()
					this.$auth.fetchUser()
				})
				.catch((e) => {
					this.$toast.warning(`${this.$t("common.has-not-been-updated-successfully")}`)
				})
				.finally(() => {
					this.isUploading = false
				})
		},

		openDialogPicture() {
			this.dialogPicture = true
		},
		closeDialogPicture() {
			this.dialogPicture = false
			this.avatar = null
			this.$emit("update", this.avatar)
		},
	},
}
</script>
