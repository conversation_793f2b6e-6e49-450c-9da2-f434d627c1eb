<template>
	<div>
		<v-switch v-model="localValue" :disabled="disabled" :label="$t('common.published')" />
		<v-row>
			<v-col cols="10">
				<vc-date-time ref="publishedAtPicker" v-model="localPublishedAt" :text-field-props="{ outlined: true, dense: true }"
							is24hr append-icon="mdi-calendar" :label="$t('common.published-at')" :disabled="computedDisabled"
							@click:clear="clearPublishedAt"
							@input="updatePublishedAt"
							/>
						</v-col>
						<v-col cols="2">
				<!-- <v-btn icon @click="clearPublishedAt">
					<v-icon>mdi-close</v-icon>
				</v-btn> -->
			</v-col>
		</v-row>

		<v-row>
			<v-col cols="10">
				<vc-date-time ref="unPublishedAtPicker"
v-model="localUnpublishAt"
			:text-field-props="{ outlined: true, dense: true }" is24hr
			:date-picker-props="{ min: minimumUnpublishAtDate }" :time-picker-props="{ min: minimumUnpublishAtTime }"
			append-icon="mdi-calendar" :label="$t('common.unpublish-at')"
			:rules="[$rules.dateGreaterThan('Unpublish At', localPublishedAt)]"
			:disabled="computedDisabled"
			@click:clear="clearUnPublishedAt"
			@input="updateUnPublishedAt"
			/>
						</v-col>
						<v-col cols="2">
				<!-- <v-btn icon @click="clearUnPublishedAt">
					<v-icon>mdi-close</v-icon>
				</v-btn> -->
			</v-col>
		</v-row>
</div>
</template>

<script>
export default {
	props: {
		publishedAt: {
			type: String,
			default: null,
		},
		unPublishedAt: {
			type: String,
			default: null,
		},
		value: {
			type: Boolean,
			default: false,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(v) {
				this.$emit("input", v) // emit the updated value
			},
		},
		computedDisabled() {
			return !this.localValue || this.disabled
		},
		localPublishedAt: {
			get() {
				return this.publishedAt
			},
			set(v) {
				this.$emit("update:publishedAt", v) // emit the updated value
			},
		},
		localUnpublishAt: {
			get() {
				return this.unPublishedAt
			},
			set(v) {
				this.$emit("update:unPublishedAt", v) // emit the updated value
			},
		},
		localPublishedAtDate() {
			// extract the date part from the datetime iso format string then convert it to browser time zone using dayjs
			return this.publishedAt
				? this.$dayjs(this.publishedAt).tz(this.$dayjs.tz.guess()).format("YYYY-MM-DD")
				: null
		},
		localPublishedAtTime() {
			// extract the time part from the datetime iso format string then convert it to browser time zone using dayjs
			return this.publishedAt
				? this.$dayjs(this.publishedAt).tz(this.$dayjs.tz.guess()).format("HH:mm")
				: null
		},

		localUnpublishAtDate() {
			// extract the date part from the datetime iso format string then convert it to browser time zone using dayjs
			return this.unPublishedAt
				? this.$dayjs(this.unPublishedAt).tz(this.$dayjs.tz.guess()).format("YYYY-MM-DD")
				: null
		},

		localUnpublishAtTime() {
			// extract the time part from the datetime iso format string then convert it to browser time zone using dayjs
			return this.unPublishedAt ? this.$dayjs(this.unPublishedAt).tz(this.$dayjs.tz.guess()).format("hh:mm a") : null
		},

		minimumUnpublishAtDate() {
			return this.localPublishedAtDate
		},

		minimumUnpublishAtTime() {
			// if the date is the same, then the minimum time is the published time
			// otherwise, the minimum time is 00:00
			if (this.localPublishedAtDate === this.localUnpublishAtDate) {
				return this.localPublishedAtTime // Same day: limit to the published time or later
			}
			console.log(this.localPublishedAtDate, this.localUnpublishAtDate, this.localPublishedAtTime, this.localUnpublishAtTime)
			if (this.localPublishedAtDate === null) {
				return this.localPublishedAtTime
			}
			return "00:00"
		},
	},
	methods: {
		clearPublishedAt() {
		// Set the value to null through the computed property
		this.localPublishedAt = null
		this.$emit("update:publishedAt", null)
		},
		clearUnPublishedAt() {
			this.localUnpublishAt = null
			this.$emit("update:unPublishedAt", null)
		},
		updatePublishedAt(value) {
			this.$emit("update:publishedAt", value)
		},
		updateUnPublishedAt(value) {
			this.$emit("update:unPublishedAt", value)
		},
	},
}
</script>
