<template>
	<v-responsive v-bind="$attrs">
		<slot v-bind="{ width, height }" />
	</v-responsive>
</template>

<script>
export default {
	data() {
		return {
			width: 0,
			height: 0,
		}
	},
	mounted() {
		this.$nextTick(() => {
			this.setDim()
			window.addEventListener("resize", this.setDim)
		})
	},
	destroyed() {
		window.removeEventListener("resize", this.setDim)
	},
	methods: {
		setDim() {
			this.width = this.$el.offsetWidth
			this.height = this.$el.offsetHeight
		},
	},
}
</script>
