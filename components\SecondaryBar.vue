<template>
	<v-system-bar height="48" color="transparent" class="flex-grow-1 px-8">
		<div class="d-flex align-center fill-width">
			<!-- hidden for now -->
			<!-- <div class="text-caption">Download Our App:</div> -->

			<!-- <v-btn icon small class="me-2">
				<v-icon>mdi-apple</v-icon>
			</v-btn>

			<v-btn icon small>
				<v-icon>mdi-android</v-icon>
			</v-btn> -->
			<v-spacer />

			<!-- <v-btn id="currency-selector" text small>
				<v-icon left>
					mdi-currency-usd
				</v-icon>{{ currentCurrency?.name }}
			</v-btn>

			<v-menu activator="#currency-selector" offset-y close-delay="200" :open-on-hover="!isMobile">
				<v-list dense>
					<v-list-item-group>
						<v-list-item v-for="currency in currencies" :key="currency.code"
							:input-value="currency.code === currentCurrency.code" link @click="setCurrency(currency.code)">
							<v-list-item-content>
								<v-list-item-title>{{ currency.name }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
					</v-list-item-group>
				</v-list>
			</v-menu> -->
			<v-badge bordered content="new" color="red" offset-x="18" offset-y="12" class="me-4">
				<v-btn text small :to="(localePath({ name: 'offers' }))">
					{{ $t('common.offers') }}
				</v-btn>
			</v-badge>
			<v-btn text small :href="decodeURIComponent(switchLocalePath(otherLocale.code))">
				<v-icon left>
					mdi-translate
				</v-icon>{{ otherLocale.name }}
			</v-btn>
		</div>
	</v-system-bar>
</template>

<script>
export default {
	name: "SecondaryBar",
	data() {
		return {
			locales: [
				{
					code: "en",
					name: "English",
				},
				{
					code: "ar",
					name: "العربية",
				},
			],
			currencies: [
				{
					code: "JOD",
					name: this.$t("common.jod"),
				},
				{
					code: "USD",
					name: this.$t("common.usd"),
				},
				{
					code: "SAR",
					name: this.$t("common.sar"),
				},
			],
		}
	},

	computed: {
		otherLocale() {
			return this.locales.find(locale => locale.code !== this.$i18n.locale)
		},
		currentCurrency() {
			const currency = this.$store.state.currency
			return this.currencies.find(c => c.code === currency)
		},
	},
	methods: {
		setCurrency(currency) {
			this.$store.commit("setCollectionItem", { key: "currency", item: currency })
			this.$cookies.set("currency", currency)
			window.location.reload()
		},
	},
}
</script>
