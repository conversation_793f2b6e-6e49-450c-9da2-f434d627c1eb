<template>
	<div justify="center">
		<!-- {{ $t("common.share") }} -->
		<v-btn icon color="blue darken-3" @click="shareOnFacebook">
			<v-icon>mdi-facebook</v-icon>
		</v-btn>
		<v-btn icon color="green darken-1" @click="shareOnWhatsApp">
			<v-icon>mdi-whatsapp</v-icon>
		</v-btn>
		<v-btn icon color="blue" @click="shareOnTwitter">
			<v-icon>mdi-twitter</v-icon>
		</v-btn>
		<v-btn icon color="red darken-1" @click="shareOnEmail">
			<v-icon>mdi-email</v-icon>
		</v-btn>
	</div>
</template>

<script>
export default {
	props: {
		product: {
			type: Object,
			required: true,
		},
	},
	computed: {
		fullPath() {
			const domain = window.location.origin
			return `${domain}${this.localePath({
				name: "product",
				params: {
					productSlug: this.product.slug,
					varianceSlug: this.product.variance?.slug,
				},
			})}`
		},
		facebookShareUrl() {
			return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(this.fullPath)}`
		},
		whatsappShareUrl() {
			return `https://api.whatsapp.com/send?text=${encodeURIComponent(this.fullPath)}`
		},
		twitterShareUrl() {
			return `https://x.com/intent/tweet?text=${encodeURIComponent(this.fullPath)}`
		},
		emailShareUrl() {
			const subject = this.product.name
			const body = encodeURIComponent(`${this.product.name} \n\nCheck it out here: ${this.fullPath}`)
			return `mailto:?subject=${subject}&body=${body}`
		},
	},
	methods: {
		shareOnFacebook() {
			window.open(this.facebookShareUrl, "_blank")
		},
		shareOnInstagram() {
			alert("Instagram doesn't support direct link sharing. You can manually share the product.")
		},
		shareOnWhatsApp() {
			window.open(this.whatsappShareUrl, "_blank")
		},
		shareOnTwitter() {
			window.open(this.twitterShareUrl, "_blank")
		},
		shareOnEmail() {
			window.open(this.emailShareUrl, "_self")
		},
	},
}
</script>

<style scoped>
.share-buttons {
	display: flex;
	gap: 0px;
	padding: 0px;
}

button:hover {
	cursor: pointer;
}

.inline-list {
	display: flex;
	flex-wrap: nowrap;
	/* Prevent wrapping to a new line */
	justify-content: flex-start;
	/* Align items to the start */
	gap: 0px;
	padding: 0px;
	width: 50%;
	/* Add some space between the buttons */
}
</style>
