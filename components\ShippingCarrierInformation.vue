<template>
	<div>
		<vc-select
			v-model="item.shippingsCarriers"
			multiple
			:label="$t('common.shippings-carriers')"
			api="/v1/lookups/shipping-carriers"
		/>
		<vc-select
			v-model="item.config.country"
			:label="$t('common.country')"
			:rules="[$rules.required($t('common.country'))]"
			api="/v1/lookups/countries"
		/>
		<vc-select
			v-model="item.config.city"
			:label="$t('common.city')"
			:rules="[$rules.required($t('common.city'))]"
			api="/v1/lookups/cities"
		/>
		<money-field v-model="item.config.price" label="Price" />
	</div>
</template>

<script>
export default {
	data() {
		return {}
	},
}
</script>
