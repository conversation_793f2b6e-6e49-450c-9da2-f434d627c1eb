<template>
	<div>
		<div class="d-flex justify-end">
			<v-btn class="text-lowercase" color="primary" @click="insertConfig(item.config)">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.add-new-config") }}</span>
			</v-btn>
		</div>

		<div v-for="(con, key) in item.config" :key="key">
			<v-card-text>
				<vc-select
					v-model="con.countryId"
					:label="$t('common.country')"
					:rules="[$rules.required($t('common.country'))]"
					api="/v1/lookups/countries   "
				/>
			</v-card-text>

			<v-card-text>
				<vc-select
					v-model="con.cityId"
					:label="$t('common.city')"
					:rules="[$rules.required($t('common.city'))]"
					:api="'/v1/lookups/cities?countryId=' + con.countryId"
					:disabled="!con.cityId"
				/>
			</v-card-text>

			<v-card-text>
				<money-field v-model="con.price" label="Price" />
			</v-card-text>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		item: {
			type: [String, Object, Array],
			default: null,
		},
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},

		insertConfig(config) {
			config.push({
				countryId: null,
				cityId: null,
				price: {
					value: "",
					basePrice: "",
					currencyId: "",
				},
			})
		},
	},
}
</script>
