<template>
	<div>
		<v-carousel v-model="model" hide-delimiter-background class="slideshow mt-2" cycle @keydown.capture="onKeydown">
			<!-- <template #next="{ on, attrs }">
				<v-icon size="124" v-bind="attrs" v-on="on">mdi-chevron-right</v-icon>
			</template> -->
			<!-- <v-carousel-item v-for="(slide, i) in data" :key="i">
				<v-img :src="slide.bg" tile >
					<v-container class="fill-height">
						<v-row class="fill-height" align="center" justify="center">
							<v-col cols="12" md="5">
								<v-chip shaped small color="accent12" class="mb-1 rounded-bl rounded-tr" dark pill>
									Exclusive
								</v-chip>
								<div class="emphasis display-2 mb-4">
									{{ slide.title }}
								</div>
								<p>
									{{ slide.text }}
								</p>
								<div class="d-flex flex-wrap px-2 justify-end">
									<v-btn outlined rounded color="#09596D" class="me-2">
										Read more
									</v-btn>
									<v-btn rounded color="#008589" dark>
										Pre-order now
									</v-btn>
								</div>
							</v-col>
							<v-col cols="12" md="7">
								<v-img contain :src="data.img" />
							</v-col>
						</v-row>
					</v-container>
				</v-img>
			</v-carousel-item> -->
			<v-carousel-item v-for="n in [21, 20, 12, 13, 14, 15]" :key="n" :eager="n === 12">
				<v-img
					:src="$img(`images/slide-show/${n}/1.png`, { format: 'webp', sizes: 'xs:100vw', quality: 80, format: 'webp' })"
					:eager="n === 21" tile height="100%">
					<v-sheet color="transparent">
						<v-container class="fill-height">
							<v-row class="fill-height" align="center" justify="center">
								<v-col cols="12" md="5" class="text-center">
									<nuxt-img v-if="n !== 25" crossorigin width="300" sizes="xs:100vw md:600px" contain
										:class="n === 21 || n === 20 || n === 22 ? 'responsive-img new-year' : 'responsive-img'"
										:src="`/images/slide-show/${n}/2.png`" format="webp" />
									<div>
										<!-- <v-btn rounded color="primary" dark size="large" large>
											Pre-order Now
										</v-btn> -->
										<v-sheet color="transparent" height="10" />
									</div>
									<nuxt-img v-if="n === 12" crossorigin width="auto" sizes="xs:60vw md:400px" contain
										class="hidden-sm-and-down" :src="`/images/slide-show/${n}/5.png`" format="webp" />
								</v-col>
								<v-col cols="12" md="7" class="text-end">
									<nuxt-img v-if="n !== 25" crossorigin
										:sizes="n === 21 || n === 20 || n === 22 ? 'xs:80vw md:550px' : 'xs:100vw md:700px'"
										:src="`/images/slide-show/${n}/3.png`" format="webp"
										:class="n === 20 ? 'left-banner' : ''" />
								</v-col>
							</v-row>
						</v-container>
					</v-sheet>
				</v-img>
			</v-carousel-item>
		</v-carousel>
	</div>
</template>

<script>
export default {
	data() {
		return {
			model: 0,
			data: [
				{
					bg: "/images/slide1.webp",
					title: "The NEW Samsung AI S24",
					text: "Experience the future of display technology with the Samsung S24 Monitor, where every pixel is a testament to innovation and engineering excellence. ",
					// img: require("~/assets/uploads/mac-book-pro.png"),
				},
				{
					bg: "/images/slide2.webp",
					title: "IPhone 14 Pro is Back",
					text: "The iPhone 14 is Apple's latest addition to their smartphone lineup as of my knowledge cutoff date in early 2023. Below are some key points about the iPhone 14 ",
					// img: require("~/assets/uploads/mac-book-pro.png"),
				},
			],
		}
	},
	methods: {
		onKeydown(e) {
			e.stopPropagation() // do not propagate the keydown event
		},
	},
}
</script>
<style lang="scss">
@import "~/assets/mixins.scss";

.slideshow {
	@include responsive("height", 40dvh, 50dvh, 70dvh);
}

.responsive-img {
	width: 170px;
	/* Default for xs */
}

/* Override for larger breakpoints */
@media (min-width: 768px) {
	.responsive-img {
		width: 405px;
		/* md breakpoint */
	}
}

@media (min-width: 1200px) {
	.responsive-img {
		width: 405px;
		/* xl breakpoint */
	}

	.new-year {
		width: 474px !important;
		padding-bottom: 75px;
	}

	.left-banner {
		padding-bottom: 75px;
		margin-top: -128px !important;
	}
}
</style>
