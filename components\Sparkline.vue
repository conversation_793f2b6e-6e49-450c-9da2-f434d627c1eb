
<script>
import { VSparkline } from "vuetify/lib"
import { genPoints } from "vuetify/lib/components/VSparkline/helpers/core"
import { genPath } from "vuetify/lib/components/VSparkline/helpers/path"
export default {
	name: "Sparkline",
	extends: VSparkline,
	props: {
		gradientFill: {
			type: Array,
			default: () => [],
		},
		gradientFillDirection: {
			type: String,
			validator: val => ["top", "bottom", "left", "right"].includes(val),
			default: "bottom",
		},
	},
	methods: {
		genFillGradient() {
			const gradientDirection = this.gradientFillDirection
			const gradient = this.gradientFill.slice()

			// Pushes empty string to force
			// a fallback to currentColor
			if (!gradient.length) { gradient.push("") }

			const len = Math.max(gradient.length - 1, 1)
			const opacityStep = 0.4 / len
			const stops = gradient.reverse().map((color, index) =>
				this.$createElement("stop", {
					attrs: {
						offset: index / len,
						"stop-color": color || "currentColor",
						"stop-opacity": 0.5 - opacityStep * (index + 1),
					},
				}),
			)
			/*
      <linearGradient id="SvgjsLinearGradient1335" x1="0" y1="0" x2="0" y2="1">
          <stop id="SvgjsStop1336" stop-opacity="0.45" stop-color="rgba(25,118,210,0.45)" offset="0.2"></stop>
          <stop id="SvgjsStop1337" stop-opacity="0.05" stop-color="rgba(255,255,255,0.05)" offset="1"></stop>
          <stop id="SvgjsStop1338" stop-opacity="0.05" stop-color="rgba(255,255,255,0.05)" offset="1"></stop>
          <stop id="SvgjsStop1339" stop-opacity="0.45" stop-color="rgba(25,118,210,0.45)" offset="1"></stop>
      </linearGradient>
			*/
			return this.$createElement("defs", [
				this.$createElement(
					"linearGradient",
					{
						attrs: {
							id: "fill-" + this._uid,
							gradientUnits: "userSpaceOnUse",
							x1: gradientDirection === "left" ? "100%" : "0",
							y1: gradientDirection === "top" ? "100%" : "0",
							x2: gradientDirection === "right" ? "100%" : "0",
							y2: gradientDirection === "bottom" ? "100%" : "0",
						},
					},
					stops,
				),
			])
		},
		genPath() {
			const points = genPoints(this.normalizedValues, this.boundary)

			return this.$createElement("path", {
				attrs: {
					d: genPath(points, this._radius, this.fill, this.parsedHeight),
					fill: this.fill ? `url(#fill-${this._uid})` : "none",
					stroke: `url(#${this._uid})`,
				},
				ref: "path",
			})
		},
		genTrend() {
			return this.$createElement(
				"svg",
				this.setTextColor(this.color, {
					attrs: {
						...this.$attrs,
						display: "block",
						"stroke-width": this._lineWidth || 1,
						viewBox: `0 0 ${this.width} ${this.totalHeight}`,
					},
				}),
				[this.genFillGradient(), this.genGradient(), this.hasLabels && this.genLabels(-(this._lineWidth / 2)), this.genPath()],
			)
		},
	},
}
</script>
