<template>
	<v-card flat>
		<v-card-title>
			<div>Stocks</div>
			<v-spacer />
			<v-btn text color="primary" @click="addStock">
				<v-icon left>
					mdi-plus
				</v-icon> Add Stock
			</v-btn>
		</v-card-title>
		<v-card-text>
			<v-list v-if="value.length">
				<draggable v-model="localValue">
					<v-list-item v-for="(item, i) in localValue" :key="i" active-class="colored-bg"
						:color="statusColor(item)" class="ps-0 mb-2" dense :input-value="!isInactive(item)">
						<v-sheet dark width="100" min-height="88" :color="statusColor(item)"
							class="d-flex align-center justify-center me-4 text-h6 rounded-l-lg flex-shrink-0">
							{{ item.price?.value | money(item.price?.currency) }}
						</v-sheet>

						<div class="flex-grow-1">
							<div class="d-flex">
								<v-list-item-content class="flex-grow-1">
									<v-list-item-title>{{ item.supplier.name | trans }}</v-list-item-title>
									<v-list-item-subtitle>
										{{ item.quantity }} pcs. <span>Sold {{ item.sold }} pcs.</span>
									</v-list-item-subtitle>
								</v-list-item-content>
								<v-list-item-content class="text-body-2">
									<v-list-item-subtitle>
										<div v-if="item.publishedAt">
											<b> Published at:</b>
											<span> {{ item.publishedAt | niceDateTime }}</span>
										</div>
										<span v-else> Published</span>
									</v-list-item-subtitle>
									<v-list-item-subtitle>
										<div v-if="item.unPublishedAt">
											<b>Up to</b> {{ item.unPublishedAt | dateTime }}
										</div>
										<div v-else>
											with No Expiry
										</div>
									</v-list-item-subtitle>
								</v-list-item-content>
								<v-list-item-action>
									<div class="d-flex">
										<v-btn icon @click="removeStock(i)">
											<v-icon>mdi-delete</v-icon>
										</v-btn>
										<v-btn icon @click="editStock(item, i)">
											<v-icon>mdi-pencil</v-icon>
										</v-btn>
										<v-btn icon class="handle">
											<v-icon>mdi-drag</v-icon>
										</v-btn>
									</div>
								</v-list-item-action>
							</div>
							<v-card-subtitle class="text-caption fill-width pb-2 pt-0 px-0">
								<v-icon small left>
									mdi-information-outline
								</v-icon> {{ inactiveReason(item) }}
							</v-card-subtitle>
						</div>
					</v-list-item>
				</draggable>
			</v-list>
			<v-alert v-else type="warning" text dense>
				You don't have any stock yet
			</v-alert>
		</v-card-text>
		<crud ref="crud" v-slot="{ item }" width="400" item-name="stock" :api="false" :default="defaultStock"
			@created="saveNew" @updated="saveEdit">
			<vc-autocomplete v-model="item.supplierId" return-object label="Supplier" api="/v1/lookups/suppliers"
				:rules="[$rules.required('Supplier')]" @change="supplierHandler($event, item)" />
			<vc-text-field v-model.number="item.quantity" type="number" min="1" label="Quantity" hide-spin-buttons :rules="[$rules.required('quantity')]" />
			<vc-text-field v-model.number="item.maxPerUser" type="number" min="1" label="Max Per User" hide-spin-buttons />
			<vc-checkbox v-model="item.isOffer" label="Offer?" @change="isOfferHandler" />
			<money-field v-if="item.isOffer" v-model="item.priceBeforeOffer" :label="priceBeforeOfferLabel"
				:rules="[$rules.max('Price Before Offer', item.price)]" />
			<money-field v-model="item.price" :label="priceLabel" :rules="[$rules.required('Price')]"
				@input="updateCostCurrency" />
			<money-field v-model="item.cost" disable-currency label="Cost" />
			<v-switch v-model="item.isPreOrder" label="Pre-order" />
			<translatable v-if="item.isPreOrder" v-slot="{ value, update, on, dir }" v-model="item.note">
				<vc-text-field :label="$t('common.note')" :value="value" :dir="dir" @input="update" v-on="on" />
			</translatable>

			<!-- <v-switch v-model="item.isPublished" label="Published"></v-switch> -->
			<publish-field v-model="item.isPublished" :published-at.sync="item.publishedAt"
				:un-published-at.sync="item.unPublishedAt" />
		</crud>
	</v-card>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			currentEditIndex: null,
			priceBeforeOfferLabel: "Price Before Offer",
			priceLabel: "Price",
			defaultStock: {
				stockId: null,
				quantity: 100,
				sold: 0,
				maxPerUser: 50,
				isOffer: false,
				publishedAt: null,
				unPublishedAt: null,
				sort: 43,
				isPublished: true,
				isPreOrder: false,
				note: null,
				supplierId: null,
				price: {
					value: null,
					// basePrice: 69.19,
					currencyId: 1,
				},
				cost: {
					value: null,
					// basePrice: 1.91,
					currencyId: 1,
				},
				priceBeforeOffer: {
					value: null,
					// basePrice: null,
					currencyId: 1,
				},
			},
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit("input", val)
			},
		},
	},
	methods: {
		supplierHandler(data, item) {
			console.log("🚀 ~ file: Stocks.vue:137 ~ supplierHandler ~ data, item:", data, item)

			item.supplierId = data.value
			item.supplier = { ...data.meta, name: data.text }
		},
		addStock() {
			this.$refs.crud.new()
		},
		editStock(item, index) {
			console.log("editStock", item)
			this.currentEditIndex = index
			this.$refs.crud.edit(item)
		},
		removeStock(index) {
			this.$confirm("Are you sure you want to delete this stock?").then((isAccepted) => {
				if (isAccepted) {
					const stocks = this.$cloneDeep(this.value)
					stocks.splice(index, 1)
					this.localValue = stocks
				}
			})
		},
		saveNew(item) {
			item = this.$cloneDeep(item)
			// generate random stockId
			// item.stockId = Math.floor(Math.random() * 1000000);
			const stocks = this.$cloneDeep(this.value)
			stocks.push(item)
			this.$emit("input", stocks)
		},
		saveEdit(item) {
			item = this.$cloneDeep(item)
			const stocks = this.$cloneDeep(this.value)
			// const index = stocks.findIndex((stock) => stock.stockId === item.stockId);
			const index = this.currentEditIndex
			stocks.splice(index, 1, item)
			this.$emit("input", stocks)
		},
		isInactive(item) {
			console.log(item.unPublishedAt, item.unPublishedAt, new Date())
			// the quantity = sold or expired or publishedAt is in the future or not published
			return (
				item.quantity === item.sold || // sold out
				(item.publishedAt && this.$dayjs().isBefore(this.$dayjs(item.publishedAt))) || // publishedAt is in the future
				(item.unPublishedAt && this.$dayjs().isAfter(this.$dayjs(item.unPublishedAt))) || // unPublishedAt is in the past
				!item.isPublished // not published
			)
		},
		inactiveReason(item) {
			if (item.quantity === item.sold) {
				return "Sold out"
			}
			if (item.unPublishedAt && this.$dayjs().isAfter(this.$dayjs(item.unPublishedAt))) {
				return "Expired"
			}
			if (item.publishedAt && this.$dayjs().isBefore(this.$dayjs(item.publishedAt))) {
				return "Will be published at " + this.$options.filters.niceDateTime(item.publishedAt)
			}
			if (!item.isPublished) {
				return "Not published"
			}

			return "nothing"
		},
		statusColor(item) {
			// the quantity = sold or expired or publishedAt is in the future or not published
			if (this.isInactive(item)) {
				return "grey"
			} else {
				return "primary"
			}
		},
		updateCostCurrency(value) {
			if (value) { this.$refs.crud.item.cost.currencyId = value.currencyId }
		},
		isOfferHandler(value) {
			if (!value) {
				// not offer
				this.$refs.crud.item.price.value = this.$refs.crud.item.priceBeforeOffer?.value
				if (!this.$refs.crud.item.priceBeforeOffer) {
					this.$refs.crud.item.priceBeforeOffer = {
						value: null,
						currencyId: 1,
					}
				}
				this.$refs.crud.item.priceBeforeOffer.value = null
				this.priceLabel = "Price"
				this.priceBeforeOfferLabel = "Price"
			} else {
				// offer
				// console.log(this.$refs.crud.item.priceBeforeOffer)
				if (!this.$refs.crud.item.priceBeforeOffer) {
					this.$refs.crud.item.priceBeforeOffer = {
						value: null,
						currencyId: 1,
					}
				}
				if (!this.$refs.crud.item.priceBeforeOffer) {
					this.$refs.crud.item.priceBeforeOffer = {
						value: null,
						currencyId: 1,
					}
				}
				this.$refs.crud.item.priceBeforeOffer.value = this.$refs.crud.item.price.value
				this.$refs.crud.item.price.value = null
				this.priceLabel = "Price After Offer"
				this.priceBeforeOfferLabel = "Price Before Offer"
			}
		},
	},
}
</script>
