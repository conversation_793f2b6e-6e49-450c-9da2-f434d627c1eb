<template>
	<toasts-snackbars ref="snackbar" v-slot="options" content-class="pt-0" :objects.sync="objects">
		<v-progress-linear
			dark
			color="rgba(255,255,255,0.2)"
			reverse
			absolute
			background-color="rgba(255,255,255,0.5)"
			:active="!!options.timeout"
			:value="options.remaining"
		/>
		<slot name="title">
			<div class="font-weight-bold mb-2 d-flex align-center pt-3">
				<template v-if="options.title">
					<v-icon v-if="!!options.icon" dark :left="!isRTL" :right="isRTL" class="vts__icon" :color="options.iconColor">
						{{ options.icon }}
					</v-icon>
					<div v-if="options.title">
{{ options.title }}
</div>
					<template v-if="options.showClose">
						<v-spacer />
						<v-btn :icon="!options.closeText" :text="!!options.closeText" :color="options.closeColor" @click="options.close">
							<v-icon v-if="!options.closeText">
{{ options.closeIcon }}
</v-icon>
							<span v-if="!!options.closeText">{{ options.closeText }}</span>
						</v-btn>
					</template>
				</template>
			</div>
		</slot>
		<div>
			<v-icon
				v-if="!!options.icon & (!options.title && !options.showClose)"
				dark
				:left="!isRTL"
				:right="isRTL"
				:color="options.iconColor"
			>
				{{ options.icon }}
			</v-icon>
			<ul v-if="Array.isArray(options.message)">
				<template v-for="array in options.message"
					>
<li v-for="(line, li) in array" :key="li">
{{ line }}
</li>
</template
				>
			</ul>
			<span v-else v-html="options.message" />
			<slot />
		</div>
	</toasts-snackbars>
</template>

<script>
export default {
	name: "Toasts",

	props: {},
	data() {
		return {
			objects: [],
		}
	},
	mounted() {
		this.$nuxt.$on("toast.add", this.add)
	},

	methods: {
		add(obj) {
			this.objects.push(obj)
		},
	},
}
</script>
