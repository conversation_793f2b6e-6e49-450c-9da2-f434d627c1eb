<template>
	<v-scale-transition leave-absolute>
		<div class="flex-grow-1" :class="{ fullscreen: isFullscreen }">
			<v-toolbar flat>
				<v-badge dot :value="isAnyFiltration" overlap class="me-2">
					<v-btn
						v-if="$scopedSlots.filter"
						v-tooltip="'Advanced Filter'"
						icon
						width="32"
						height="32"
						@click="filterDrawerModel = !filterDrawerModel"
					>
						<v-icon>mdi-filter-cog-outline</v-icon>
					</v-btn>
				</v-badge>
				<vc-text-field
					v-model="localSearch"
					chips
					multiple
					single-line
					hide-details=""
					clearable
					placeholder="Search"
					append-icon="mdi-magnify"
					style="max-width: 350px"
					@input="search"
					@keyup.enter="filter"
					@click:append="filter"
					@click:clear="clearSearch"
				/>
				<v-spacer />
				<slot name="prepend-action" v-bind="{ filter: filterObject, models, refresh, options }" />
				<v-btn v-tooltip="'Fullscreen'" class="me-2" icon width="32" height="32" @click="isFullscreen = !isFullscreen">
					<v-icon v-if="isFullscreen">
mdi-fullscreen-exit
</v-icon>
					<v-icon v-else>
mdi-fullscreen
</v-icon>
				</v-btn>
				<v-btn v-tooltip="'Refresh'" :loading="loading" class="me-2" icon width="32" height="32" @click="$fetch">
					<v-icon>mdi-refresh</v-icon>
</v-btn
				>
				<slot name="append-action" v-bind="{ filter: filterObject, models, refresh, options }" />

				<v-btn class="me-2" text height="32">
<v-icon left>
mdi-export
</v-icon>Export
</v-btn>
			</v-toolbar>
		</div>
	</v-scale-transition>
</template>

<script>
export default {
	props: {
		loading: {
			type: Boolean,
			default: false,
		},
		search: {
			type: String,
			default: "",
		},
	},
	computed: {
		localSearch: {
			get() {
				return this.search
			},
			set(val) {
				this.$emit("update:search", val)
			},
		},
	},
	methods: {
		filter() {
			this.$emit("filter")
		},
	},
}
</script>
