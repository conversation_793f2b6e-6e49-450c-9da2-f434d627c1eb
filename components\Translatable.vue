<template>
	<div class="translatable">
		<div
			ref="thisComponent"
			v-click-outside="{
				handler: clickOutsideHandler,
				closeConditional: closeConditionalHandler,
			}"
			:class="{ 'is-focussed': isFocused }"
		>
			<div class="content d-flex align-baseline">
				<div
					v-for="(item, i) in languages"
					v-show="item.value === tab"
					:ref="'inputs'"
					:key="'slot-' + item.value"
					class="flex-grow-1"
					:data-locale="item.value"
					@keypress.enter="switchToNextTab(i)"
					@keydown.esc="blurInput(i)"
				>
					<slot v-bind="bind(item.value)" />
				</div>
				<v-badge bordered overlap :color="hasError ? 'error' : 'warning'" dot :value="anyEmptyValue">
					<v-btn icon class="ms-2" @click="isFocused = true"
						>
<v-icon :color="hasError ? 'error' : undefined">
mdi-web
</v-icon>
</v-btn
					>
				</v-badge>
			</div>
			<div v-if="$refs.thisComponent">
				<v-tabs
					v-show="isFocused"
					v-model="tab"
					height="32"
					class="mt-2"
					background-color="bg-color"
					show-arrows
					center-active
					:style="{ maxWidth: $refs.thisComponent.clientWidth + 'px' }"
					@change="localeChangeHandler"
				>
					<v-tab
						v-for="item in languages"
						:key="'x' + item.value"
						:href="'#' + item.value.toLowerCase()"
						:class="{
							'bg-color': !isMobile,
							'active-tab elevation-3 rounded-t': item.value === tab,
						}"
						:exact-active-class="isMobile ? 'active-tab' : 'active-tab elevation-3 rounded-t'"
					>
						<v-img contain width="16" height="12" :src="'/images/languages/' + item.value + '.svg'" />
						<v-badge dot :value="value && !value[item.value]" color="warning">
{{ item.value }}
</v-badge>
					</v-tab>
				</v-tabs>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		value: {
			type: Object,
			default: () => ({}),
		},
		rules: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			languages: [],
			tab: this.$i18n.locale,
			isFocused: false,
			observer: null,
			watchedNodes: {},
			watchedNodesResult: {},
		}
	},
	async fetch() {
		await this.$axios.$getOnce("/v1/lookups/languages").then((res) => {
			this.languages = res.map(x => ({ ...x, value: x.meta.abbreviation }))
			this.$nextTick(() => {
				this.elWatcher()
			})
		})
	},
	computed: {
		currentLanguage() {
			return this.languages?.find(x => x.value === this.tab)
		},

		anyEmptyValue() {
			return Object.values(this.model).some(x => !x)
		},
		model() {
			const abbreviations = this.languages.map(x => x.value)
			const model = {}
			abbreviations.forEach((x) => {
				model[x] = this.value ? this.value[x] : null
			})
			return model
		},
		hasError() {
			// has any false value in watchedNodesResult
			console.log(
				"🚀 ~ file: Translatable.vue:106 ~ hasError ~ bject.values(this.watchedNodesResult):",
				Object.values(this.watchedNodesResult),
				Object.values(this.watchedNodesResult).includes(false),
			)
			return Object.values(this.watchedNodesResult).includes(false)
		},
	},
	mounted() {},
	destroyed() {
		// this.observer.disconnect();
	},
	methods: {
		update(value) {
			const localValue = { ...this.value, [this.tab]: value }
			this.$emit("input", localValue)
		},
		clickOutsideHandler() {
			this.isFocused = false
		},
		closeConditionalHandler(e) {
			// return true when the event is clicked from outside the element or its children
			return !this.$el.contains(e.target)
		},
		bind(locale) {
			return {
				value: this.value ? this.value[locale] : null,
				update: this.update,
				dir: this.currentLanguage?.meta?.direction,
				on: {
					focus: () => {
						this.isFocused = true
					},
				},
				handler: () => {
					this.isFocused = true
				},
				key: this._uid + "-" + locale,
				locale,
				rules: this.rules,
			}
		},
		elWatcher() {
			// setup observer if any element has .error--text in .content container set hasError to true
			console.log(this.$refs)
			this.$refs.inputs.forEach((node) => {
				const locale = node.dataset.locale
				const mutation = new MutationObserver((mutations) => {
					mutations.forEach((mutation) => {
						this.$set(this.watchedNodesResult, locale, !mutation.target.classList.contains("error--text"))
					})
				})
				console.log(
					"🚀 ~ file: Translatable.vue:152 ~ this.$refs.inputs.forEach ~ node.firstChild.querySelector(.v-input):",
					node.querySelector(".v-input"),
				)
				mutation.observe(node.querySelector(".v-input"), {
					attributes: true,
					attributeFilter: ["class"],
				})
				this.$set(this.watchedNodes, locale, mutation)
			})
		},
		localeChangeHandler(locale) {
			const index = this.languages.findIndex(x => x.value === locale)
			this.$nextTick(() => {
				console.dir(this.$refs.inputs[index].getElementsByTagName("input"))
				const input = this.$refs.inputs[index].getElementsByTagName("input")[0]
				if (input?.length) { input.focus() }
			})
		},
		switchToNextTab(index) {
			const nextIndex = index + 1
			if (nextIndex < this.languages.length) {
				this.tab = this.languages[nextIndex].value
			} else {
				this.tab = this.languages[0].value
			}
			this.localeChangeHandler(this.tab)
		},
		blurInput(index) {
			const input = this.$refs.inputs[index].getElementsByTagName("input")
			if (input?.length) { input[0].blur() }
			this.isFocused = false
		},
	},
}
</script>

<style lang="scss" >
@import "~/assets/mixins";
$bg-color: #a3a3a323;
$tabs-height: 32px;
$border-width: 2px;
.translatable {
	--bg-color: #{$bg-color};
	position: relative;
	.content {
		& > *:first-child {
			flex-grow: 1;
		}
	}
	.bg-color {
		background-color: var(--bg-color);
	}
	.is-focussed {
		//background-color: var(--bg-color);
		//position: absolute;

		top: 0;
		left: -8px;
		right: -8px;
		//padding: 0 0 0 8px;

		border-top-right-radius: 8px;
		border-bottom-right-radius: 8px;
		box-shadow: 0px 3px 1px -2px rgb(20 21 26 / 4%), 0px 2px 2px 0px rgb(71 77 87 / 4%), 0px 1px 5px 0px rgb(20 21 26 / 10%);
		@include dir("border-top-right-radius", 8px, 0);
		@include dir("border-bottom-right-radius", 8px, 0);
		@include dir("border-top-left-radius", 0, 8px);
		@include dir("border-bottom-left-radius", 0, 8px);
		// &:before {
		// 	//content: "";
		// 	position: absolute;

		// 	bottom: -$tabs-height;
		// 	width: 8px;
		// 	background-color: var(--bg-color);
		// 	@include dir("left", -8px, 0);
		// 	@include dir("right", 0, -8px);
		// 	@include dir("border-top-left-radius", 8px);
		// 	@include dir("border-bottom-left-radius", 8px);
		// 	@include dir("border-top-right-radius", 0, 8px);
		// 	@include dir("border-bottom-right-radius", 0, 8px);
		// }
	}
	.v-tabs {
		width: auto !important;
		bottom: -$tabs-height;
		z-index: 1;
		left: 0;
		border-radius: 0;
		position: absolute;

		border-top: 0;
		margin: 0 !important;
		background-color: var(--bg-color);
		box-shadow: 0px 3px 1px -2px rgb(20 21 26 / 4%), 0px 2px 2px 0px rgb(71 77 87 / 4%), 0px 1px 5px 0px rgb(20 21 26 / 10%);
		.v-tab {
			min-width: 64px;
			padding: 0 8px;
			&.v-tab--active {
				//	background-color: #f5f5f5;
			}
		}
		&::after {
			content: "";
			position: absolute;
			top: -4px;
			height: 4px;
			left: 0;
			right: 0;
			background-color: #fff;
		}
	}
}
</style>
