<template>
	<div>
		<slot v-bind="{ on }">
			<v-btn icon v-on="on">
				<v-icon>mdi-plus</v-icon>
			</v-btn>
		</slot>
		<v-dialog v-model="dialog" persistent max-width="1000">
			<v-form ref="form">
				<v-card>
					<v-card-title>
						{{ $t("common.add-translation") }}
						<v-spacer />
						<dialog-close v-model="dialog" />
					</v-card-title>
					<v-row no-gutters>
						<v-col md="8">
							<v-card-text>
								<span class="text-body-2">{{ $t("common.new-key") }}: </span>
								<div class="d-flex">
									<translations-group v-model="form.group" />
									<!-- <translations-sub-group v-model="form.sub_group" :group="form.group" /> -->
									<translations-key v-model="form.key" :group="form.group" :sub-group="form.sub_group" />
								</div>

								<vc-text-field readonly :disabled="!fullNewKey" dense background-color="grey lighten-3" outlined
									:rules="[(v) => !$te(v) || $t('validation.error.key-exists', { key: v })]"
									append-icon="mdi-content-copy" :value="fullNewKey" />

								<span class="text-body-2"> {{ $t("common.translations") }} :</span>
								<translations-placeholders :placeholders="form.placeholders"
									@add="form.placeholders.push($event)" @insert="insertPlaceHolder"
									@delete="deletePlaceHolder" />
								<translations-editor ref="editor" :item="form" />
							</v-card-text>
						</v-col>
						<v-col cols="auto">
							<v-divider vertical />
						</v-col>
						<v-col>
							<v-card-text>
								<span class="text-body-2">Search in old files</span>
								<translations-search-old-files @change="oldFilesHandler" />

								<div class="d-flex justify-space-between align-center py-2">
									<span class="text-body-2">{{ $t("common.is-published") }} ? </span>
									<translations-is-published v-model="form.is_published" class="my-0" />
								</div>
								<span class="text-body-2">{{ $t("common.show-on") }} </span>
								<translations-tags v-model="form.tags" />
							</v-card-text>
						</v-col>
					</v-row>
					<v-card-actions>
						<v-spacer />
						<v-btn text @click="dialog = false">
							{{ $t("common.cancel") }}
						</v-btn>
						<v-btn :loading="isInserting" text color="primary" @click="insert">
							{{ $t("common.save") }}
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</v-dialog>
	</div>
</template>

<script>
import translations from "~/mixins/translations"
export default {
	mixins: [translations],
	data() {
		return {
			dialog: false,
			isInserting: false,
			tabsModel: 0,
			isLoaded: false,
			form: {
				group: null,
				sub_group: null,
				key: null,
				languages: [],
				placeholders: [],
				isPublished: true,
			},
			oldLocales: {},
		}
	},
	async fetch() {
		await this.$axios.$get("/v1/lookups/languages").then((res) => {
			this.form.languages = res.map((item) => {
				return {
					language_id: item.id,
					abbreviation: item.abbreviation,
					value: null,
					direction: item.direction,
				}
			})
		})
	},
	computed: {
		oldFile() {
			return this.en
		},

		on() {
			const self = this
			return {
				click(e) {
					self.dialog = true
				},
			}
		},
		activeTab() {
			return this.form.languages[this.tabsModel]
		},
		fullNewKey() {
			if (!this.form.key) { return null }
			// if (!this.sub_groups) return `${this.form.group}.${this.form.key}`;
			return `${this.form.group}.${this.form.key}`
		},
	},
	watch: {
		"$route.name"() {
			this.form.sub_group = this.getRouteBaseName()
		},
		dialog(v) {
			if (!v) {
				this.$refs.form.reset()
				this.key1 = "page"
			} else if (!this.isLoaded) {
				// const locales = await import("~/old-locale").then((m) => m.default);
				// console.log("🚀 ~ file: Add.vue ~ line 111 ~ dialog ~ locales", locales);
				// this.oldLocales = locales;
				// // for (const locale in locales) {
				// // 	this.$set(this.oldLocales, locale, locales[locale]);
				// // }
				// this.isLoaded = true;
			}
		},
	},
	methods: {
		setValue(localeCode, value) {
			console.log(localeCode, value)
			const locale = this.form.languages.find(item => item.abbreviation === localeCode)
			locale.value = value // this.$te(this.fullNewKey, locale) ? this.$t(this.fullNewKey, locale) : locale.value;
		},
		oldFilesHandler(messages) {
			const fullKey = this.toKebabCase(messages[this.$i18n.locale].key)
			const fullNewKey = fullKey.split(".")
			let group, subGroup, key
			if (fullNewKey.length === 2) {
				group = fullNewKey[0]

				key = fullNewKey[1]
			} else {
				// group = null; //no need to do anything just keep it as its

				key = fullNewKey[0]
			}
			console.log(group, subGroup, key)
			if (this.groups.includes(group)) {
				this.form.group = group
			}
			if (Array.isArray(this.subGroups) && this.subGroups.includes(subGroup)) {
				this.form.sub_group = subGroup
			}
			this.form.key = key

			// this.setKey()
			this.setValues(messages)
		},
		setValues(messages) {
			for (const locale in messages) {
				this.setValue(locale, messages[locale].value)
			}
		},
		toKebabCase(str) {
			return (
				str &&
				str
					.match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g)
					.map(x => x.toLowerCase())
					.join("-")
			)
		},
		insert() {
			/*	if (!this.$refs.form.validate()) return;

			this.isInserting = true;
			this.$axios
				.$post("/v1/portal/static-translations", { ...this.form })
				.then((resp) => {
					this.dialog = false;
					this.$toast.success(this.fullNewKey + " has been saved successfully");

					const currentLocaleValue = this.form.languages.find((item) => item.abbreviation === this.$i18n.locale);

					this.$i18n.setLocaleMessage(this.$i18n.locale, {
						...this.$i18n.messages[this.$i18n.locale],
						[this.fullNewKey]: currentLocaleValue.value,
					});

					this.copyKey(this.form);
					this.$emit("updated");
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.form);
				})
				.finally(() => {
					this.isInserting = false;
				});
		  */
		},
		insertPlaceHolder(value) {
			this.$refs.editor.insertPlaceHolder(value, this.activeTab.abbreviation)
		},
		showPlaceHolderDialog() {
			this.placeHolderDialogModel = true
		},
		addPlaceHolder() {
			this.form.placeholders.push({ text: this.newPlaceholder.label, value: this.newPlaceholder.value })
			this.placeHolderDialogModel = false
		},
	},
}
</script>
