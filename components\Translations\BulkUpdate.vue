<template>
<div>
    <input ref="importFile" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" type="file" class="d-none" @change="inputChangeHandler">
    <v-dialog v-model="dialogModel" max-width="500">
        <v-card>
            <v-card-title>  $t('common.bulk-update')  </v-card-title>
            <v-card-text>
                <v-form ref="form">
                    <vc-select v-model="form.sourceLanguage" :rules="[$rules.required('common.source-language')]" :items="sourceLanguageItems" :label="$t('common.translate-from')" return-object />
                    <vc-select v-model="form.targetLanguage" :rules="[$rules.required('common.target-language')]" :items="targetLanguageItems" :label="$t('common.to-language')" return-object />
                    <label> {{ $t('common.tags') }}  </label>
                    <translations-tags v-model="form.tags" :rules="[$rules.required('common.target-language')]" />
                </v-form>
            </v-card-text>
            <v-card-actions>
                <v-btn text @click="close">
{{ $t("common.cancel") }}
</v-btn>
                <v-spacer />
                <v-btn color="primary" text :loading="isImporting" @click="importHandler">
{{ $t("common.import") }}
</v-btn>
                <v-btn color="primary" text :loading="isExporting" @click="exportHandler">
{{ $t("common.export") }}
</v-btn>
                <v-btn color="primary" text @click="compareHandler">
Side by side
</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
    <crud-drawer ref="crudDrawer" v-model="crudDrawerModel" :loading="isGetting" width="1000" title="Language Comparison" @save="save">
        <v-simple-table fixed-header>
            <template #default>
                <thead>
                    <tr>
                        <th colspan="3">
                            <v-toolbar flat class="px-0 font-weight-regular mb-1 no-padding" dense height="48">
                                <vc-text-field v-model="filterModel" clearable dense label="Filter" hide-details append-icon="mdi-magnify" />
                                <v-spacer />
                                <v-switch v-model="ShowMissingModel" hide-details class="me-2" :label="$t('common.show-only-missing') " />
                            </v-toolbar>
                        </th>
                    </tr>
                    <tr>
                        <!-- <th class="text-left" style="width: 20%">Key</th> -->
                        <th class="text-left" style="width: 50%">
                            Source <span class="text-uppercase">({{ form?.sourceLanguage?.meta?.abbreviation }})</span>
                        </th>
                        <th class="text-left" style="width: 50%">
                            Target <span class="text-uppercase">({{ form?.targetLanguage?.meta?.abbreviation }})</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="key in filteredKeys">
                        <tr :key="'key' + key" class="grey lighten-4">
                            <td colspan="2">
                                <div class="text-caption font-weight-bold">
                                    <span :id="key">{{ key }}</span>
                                    <v-btn small icon right @click="copyKey(key)">
                                        <v-icon small>
mdi-content-copy
</v-icon>
                                    </v-btn>
                                </div>
                            </td>
                        </tr>
                        <tr :key="key">
                            <td>
                                {{ source[key] }}
                            </td>
                            <td>
                                <div class="py-3">
                                    <vc-textarea v-model="target[key]" :dir="form.targetLanguage.meta.direction" outlined hide-details dense @focus="focusedKey = key" />
                                    <!-- <component
                      :is="`translations-editor-${item.type}`"
                      ref="editorComponent"
                      v-model="target[key]"

:dir="form.targetLanguage.meta.direction"
                      outlined

hide-details

dense
                      @focus="focusedKey = key"
                    ></component> -->
                                </div>
                                <!-- @blur="focusedKey === key ? (focusedKey = null) : null" -->
                            </td>
                        </tr>
                    </template>
                    <tr v-if="!filteredKeys.length">
                        <td colspan="3">
                            <v-alert type="info" class="mt-2" dense text>
There is no data matching your filter criteria
</v-alert>
                        </td>
                    </tr>
                </tbody>
            </template>
        </v-simple-table>
    </crud-drawer>
</div>
</template>

<script>
import dot from "dot-object"
import translations from "~/mixins/translations"
export default {
    mixins: [translations],
    data() {
        return {
            dialogModel: false,
            crudDrawerModel: false,
            form: {
                sourceLanguage: null,
                targetLanguage: null,
                tags: [],
            },
            source: {},
            target: {},
            // keys: ["key1", "key2", "key3", "key1", "key2", "key3", "key1", "key2", "key3", "key1", "key2", "key3"],
            isGetting: false,
            ShowMissingModel: false,
            filterModel: null,
            focusedKey: null,
            isSaving: false,
            isExporting: false,
            isImporting: false,
            languages: [],
        }
    },
    async fetch() {
        await this.$axios.$getOnce("/v1/lookups/languages?based_country=false").then((res) => {
            this.languages = res
        })
    },
    computed: {
        sourceLanguageItems() {
            return this.languages
        },
        targetLanguageItems() {
            return this.languages.filter(x => x.value !== this.form.sourceLanguage?.value) || []
        },
        keys() {
            return Object.keys(this.source)
        },
        filteredKeys() {
            if (this.ShowMissingModel) {
                return this.keys.filter(x => !this.target[x] || x === this.focusedKey)
            }

            if (this.filterModel) {
                return this.keys.filter(x => x.toLowerCase().includes(this.filterModel.toLowerCase()))
            }

            return this.keys
        },
    },

    methods: {
        new() {
            this.dialogModel = true
        },
        close() {
            this.dialogModel = false
            this.$refs.form.reset()
        },
        compareHandler() {
            if (!this.$refs.form.validate()) {
                return
            }
            this.dialogModel = false
            this.isGetting = true
            this.crudDrawerModel = true

            const tags = this.form.tags.map(tag => tag.text)
            const promises = Promise.all([
                this.$axios.$get(`/v1/lookups/translations/${this.form.sourceLanguage.meta.abbreviation}?tags=${tags.toString()}`),

                this.$axios.$get(`/v1/lookups/translations/${this.form.targetLanguage.meta.abbreviation}?tags=${tags.toString()}`),
            ])

            promises.then((resp) => {
                this.source = dot.dot(resp[0])
                this.target = dot.dot(resp[1])
                this.isGetting = false
            })
        },

        exportHandler() {
            if (!this.$refs.form.validate()) {
                return
            }
            this.isExporting = true
            this.$axios
                .$post(
                    `/v1/admin/portal/translations/export`, {
                        source_language: this.form.sourceLanguage.meta.abbreviation,
                        target_language: this.form.targetLanguage.meta.abbreviation,
                        tags: this.form.tags,
                    }, {
                        responseType: "blob",
                    },
                )
                .then((resp) => {
                    const FileDownload = require("js-file-download")
                    FileDownload(
                        resp,
                        `translations-from-${this.form.sourceLanguage.meta.abbreviation}-to-${this.form.targetLanguage.meta.abbreviation}.xlsx`,
                    )
                })
                .catch((error) => {
                    this.genericErrorHandler(error)
                })
                .finally(() => {
                    this.isExporting = false
                })
        },

        importHandler() {
            this.$refs.importFile.click()
        },
        inputChangeHandler() {
            const file = this.$refs.importFile.files[0]
            if (!file) {
                return
            }
            this.isImporting = true
            const formData = new FormData()
            formData.append("file", file)
            formData.append("source_language", this.form.sourceLanguage.meta.abbreviation)
            formData.append("target_language", this.form.targetLanguage.meta.abbreviation)

            this.$axios
                .$post(`/v1/admin/portal/translations/import`, formData)
                .then((resp) => {
                    // this.source = dot.dot(resp.source);
                    // this.target = dot.dot(resp.target);
                    this.close()
                    this.$emit("updated")
                })
                .catch((error) => {
                    this.genericErrorHandler(error)
                })
                .finally(() => {
                    this.$refs.importFile.value = ""
                    this.isImporting = false
                })
        },
        save() {
            this.isSaving = true
            const target = this.$cloneDeep(this.target)
            // remove null value from target
            Object.keys(target).forEach(key => target[key] == null && delete target[key])

            this.$axios
                .$patch(`/v1/admin/portal/translations/${this.form.targetLanguage.meta.abbreviation}`, target)
                .then((resp) => {
                    this.crudDrawerModel = false
                    this.$toast.success("Data has been successfully saved")
                })
                .catch((error) => {
                    this.genericErrorHandler(error)
                })
                .finally(() => {
                    this.isSaving = false
                })
        },
    },
}
</script>
