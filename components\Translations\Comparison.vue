<template>
	<div>
		<v-dialog v-model="dialogModel" max-width="500">
			<v-card>
				<v-card-title> Language Comparison </v-card-title>
				<v-card-text>
					<v-form ref="form">
						<div class="d-flex align-baseline">
							<!-- <label class="me-2">I want to translate</label> -->
							<vc-select
								v-model="form.sourceLanguage"
								:rules="[$rules.required('Source Language')]"
								:items="sourceLanguageItems"
								label="Source Language"
								item-text="name"
								item-value="abbreviation"
								return-object
							/>
						</div>
						<vc-select
							v-model="form.targetLanguage"
							:rules="[$rules.required('Target Language')]"
							:items="targetLanguageItems"
							label="Target Language"
							item-text="name"
							item-value="abbreviation"
							return-object
						/>
					</v-form>
				</v-card-text>
				<v-card-actions>
					<v-btn text @click="close">
Close
</v-btn>
					<v-spacer />
					<v-btn color="primary" text @click="compare">
Compare
</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
		<crud-drawer ref="crudDrawer" v-model="crudDrawerModel" :loading="isGetting" :api="api" title="Language Comparison" @save="save">
			<v-simple-table fixed-header>
				<template #default>
					<thead>
						<tr>
							<th colspan="3">
								<v-toolbar flat class="px-0 font-weight-regular mb-1 no-padding" dense height="48">
									<vc-text-field
										v-model="filterModel"
										clearable
										dense
										label="Filter"
										hide-details
										append-icon="mdi-magnify"
									/>
									<v-spacer />
									<v-switch v-model="ShowMissingModel" hide-details class="me-2" label="Show only missing" />
								</v-toolbar>
							</th>
						</tr>
						<tr>
							<th class="text-left">
Key
</th>
							<th class="text-left">
								Source <span class="text-uppercase">({{ form.sourceLanguage.name }})</span>
							</th>
							<th class="text-left">
								Target <span class="text-uppercase">({{ form.targetLanguage.name }})</span>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="key in filteredKeys" :key="key">
							<td>{{ key }}</td>
							<td>
								{{ source[key] }}
							</td>
							<td>
								<vc-text-field v-model="target[key]" outlined hide-details dense @focus="focusedKey = key" />
								<!-- @blur="focusedKey === key ? (focusedKey = null) : null" -->
							</td>
						</tr>
						<tr v-if="!filteredKeys.length">
							<td colspan="3">
								<v-alert type="info" class="mt-2" dense text>
There is no data matching your filter criteria
</v-alert>
							</td>
						</tr>
					</tbody>
				</template>
			</v-simple-table>
		</crud-drawer>
	</div>
</template>

<script>
import dot from "dot-object"
export default {
	data() {
		return {
			dialogModel: false,
			crudDrawerModel: false,
			form: {
				sourceLanguage: {},
				targetLanguage: {},
			},
			source: {},
			target: {},
			// keys: ["key1", "key2", "key3", "key1", "key2", "key3", "key1", "key2", "key3", "key1", "key2", "key3"],
			isGetting: false,
			ShowMissingModel: false,
			filterModel: null,
			focusedKey: null,
			isSaving: false,
		}
	},
	computed: {
		sourceLanguageItems() {
			return this.$store.getters.languages
		},
		targetLanguageItems() {
			return this.$store.getters.languages.filter(x => x.abbreviation !== this.form.sourceLanguage.abbreviation)
		},
		keys() {
			return Object.keys(this.source)
		},
		filteredKeys() {
			if (this.ShowMissingModel) {
				return this.keys.filter(x => !this.target[x] || x === this.focusedKey)
			}

			if (this.filterModel) {
				return this.keys.filter(x => x.toLowerCase().includes(this.filterModel.toLowerCase()))
			}

			return this.keys
		},
	},

	methods: {
		new() {
			this.dialogModel = true
		},
		close() {
			this.dialogModel = false
			this.$refs.form.reset()
		},
		compare() {
			if (!this.$refs.form.validate()) {
				return
			}
			this.dialogModel = false
			this.isGetting = true
			this.crudDrawerModel = true

			const promises = Promise.all([
				this.$axios.$get(`/v1/lookups/translations/${this.form.sourceLanguage.abbreviation}`),

				this.$axios.$get(`/v1/lookups/translations/${this.form.targetLanguage.abbreviation}`),
			])

			promises.then((resp) => {
				this.source = dot.dot(resp[0])
				this.target = dot.dot(resp[1])
				this.isGetting = false
			})
		},

		save() {
			this.isSaving = true
			this.$axios
				.$patch(`/v1/admin/portal/translations/${this.form.targetLanguage.abbreviation}`, this.target)
				.then((resp) => {
					this.crudDrawerModel = false
					this.$toast.success("Data has been successfully saved")
				})
				.catch((error) => {
					this.genericErrorHandler(error)
				})
				.finally(() => {
					this.isSaving = false
				})
		},
	},
}
</script>
