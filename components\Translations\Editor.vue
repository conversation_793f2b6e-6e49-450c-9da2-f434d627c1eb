<template>
	<div>
		<v-tabs v-model="tabsModel">
			<v-tab v-for="lang in item.languages" :key="'tab-title-' + lang.abbreviation" :disabled="!fullNewKey">
				<v-badge :value="true" :color="lang.value ? 'success' : 'warning'" left inline bordered dot>
					{{ lang.abbreviation }}
				</v-badge>
			</v-tab>
		</v-tabs>
		<v-tabs-items ref="tabsItems" v-model="tabsModel">
			<v-tab-item v-for="(lang, i) in item.languages" :key="'tab' + i" eager>
				<section v-if="item.type">
					<component
						:is="`translations-editor-${item.type}`"
						ref="editorComponent"
						v-model="lang.value"
						:lang="lang"
						:disabled="!fullNewKey"
						:index="i"
						@focused="inputIndex"
					/>
				</section>
			</v-tab-item>
		</v-tabs-items>
	</div>
</template>

<script>
import translations from "~/mixins/translations"
export default {
	mixins: [translations],
	props: {
		item: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			tabsModel: 0,
			index: null,
		}
	},
	computed: {
		fullNewKey() {
			if (!this.item.key) { return null }
			return `${this.item.group}.${this.item.key}`
		},
	},
	methods: {
		insertPlaceHolder(value) {
			const currentEditorComponent = this.$refs.editorComponent[this.tabsModel]
      currentEditorComponent.insertPlaceHolder(value)
		},
		inputIndex(index) {
			this.index = index
		},
		async setValueByLocale(locale, value) {
			const i = this.item.languages.findIndex(item => item.abbreviation === locale)
			if (i > -1) {
				if (!this.item.languages[i].value) {
					/* eslint-disable-next-line */
					this.item.languages[i].value = value;
				} else {
					await this.$confirm(
						`<div class="mb-2">Are you sure you want to overwrite the <span class="text-uppercase">${this.item.languages[i].abbreviation}</span> content for key "${this.fullNewKey}"?</div>
					<label>Before: </label>
					<div class="pa-2 grey lighten-2 rounded text-body-2">${this.item.languages[i].value}</div>
					<label>After: </label>
					<div class="pa-2 grey lighten-2 rounded text-body-2">${value}</div>`,
						{
							title: "Overwrite value",
						},
					).then((isAccepted) => {
						if (isAccepted) {
							/* eslint-disable-next-line */
							this.item.languages[i].value = value;
						}
					})
				}
			}
		},
	},
}
</script>
