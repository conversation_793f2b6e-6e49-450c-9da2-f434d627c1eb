<template>
<vc-text-field readonly :disabled="!fullNewKey" dense background-color="grey lighten-3" outlined append-icon="mdi-content-copy" :value="fullNewKey" :hint="$t('common.content-will-be-copied-automatically-on-save') " persistent-hint @click:append="copyKey({ key: itemKey, group: itemGroup })">
    <!-- :rules="[validate]" -->
    <template #append-outer>
        <div class="d-flex align-baseline">
            <v-switch v-show="editMode" v-model="localValue" class="mt-0" hide-details />
            <div v-show="editMode">
{{ $t("common.allow-key-edit") }}
</div>
        </div>
    </template>
</vc-text-field>
</template>

<script>
import translations from "~/mixins/translations"
export default {
    mixins: [translations],
    props: {
        itemGroup: {
            type: String,
            default: null,
        },
        itemKey: {
            type: String,
            default: null,
        },
        editMode: {
            type: Boolean,
            default: false,
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            originalKey: null,
        }
    },
    computed: {
        fullNewKey() {
            return this.itemKey ? `${this.itemGroup}.${this.itemKey}` : null
        },
        localValue: {
            get() {
                return this.value
            },
            set(value) {
                this.$emit("input", value)
            },
        },
    },

    methods: {
        validate(v) {
            return (this.editMode ? this.originalKey === v || !this.$te(v) : !this.$te(v)) || this.$t("validation.key-exists", {
                key: v,
            })
        },
        setOriginalKey() {
            this.originalKey = this.fullNewKey
        },
    },
}
</script>
