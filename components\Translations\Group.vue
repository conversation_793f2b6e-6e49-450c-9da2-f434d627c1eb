<template>
	<vc-combobox v-model="localValue" :label="$t('common.group')" :items="groups"
		:rules="[$rules.required('common.group')]" v-on="$listeners" />
</template>

<script>
import translations from "~/mixins/translations"
export default {
	mixins: [translations],
	props: {
		value: {
			type: String,
			default: null,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
	methods: {
		setGroup(group) {
			this.$emit("input", group)
		},
	},
}
</script>
