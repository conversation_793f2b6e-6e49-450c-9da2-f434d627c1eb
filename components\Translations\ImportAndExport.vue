<template>
	<crud
		ref="crud"
		width="400"
		:default="defaultItem"
		item-name="Translation"
		api="/v1/admin/portal/translations"
		@updated="refresh"
		@created="refresh"
		@deleted="refresh"
	>
		<under-construction />
	</crud>
</template>

<script>
export default {
	props: {
		value: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(v) {
				this.$emit("input", v)
			},
		},
	},
	methods: {
		show() {
			console.log("show")
			this.$refs.crud.new()
		},
	},
}
</script>
