<template>
	<vc-text-field
		v-model="localValue"
		:label="$t('common.key')"
		:disabled="!group"
		:hint="isNewKeyExist ? 'Key Exist' : ''"
		:append-icon="isNewKeyExist ? 'mdi-alert' : undefined"
		:color="isNewKeyExist ? 'warning' : undefined"
		:rules="[
			$rules.required('Key'),
			$rules.regex('', '^[a-z]', $t('validation.regex.alpha')),
			$rules.regex('', '^[a-z0-9\-]+$', $t('validation.regex.alpha-numeric-dash')),
		]"
	/>
</template>

<script>
import translations from "~/mixins/translations"
export default {
	mixins: [translations],
	props: {
		value: {
			type: String,
			default: null,
		},
		group: {
			type: String,
			default: null,
		},
		subGroup: {
			type: String,
			default: null,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
	methods: {
		setKey(key) {
			this.$emit("input", key)
		},
	},
}
</script>
