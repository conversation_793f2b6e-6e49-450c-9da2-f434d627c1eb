<template>
	<div id="placeholders" class="d-flex align-center">
		<v-chip-group v-if="placeholders.length" column active-class="primary--text" class="flex-grow-1">
			<v-chip
				v-for="placeholder in parsedPlaceholders"
				:id="`placeholder-${placeholder.value}`"
				:key="placeholder.value"
				:input-value="false"
				close
				close-icon="mdi-dots-vertical"
				class="relative"
				@click:close="placeholderMenu(placeholder)"
				@click="insertPlaceHolder(placeholder.value)"
			>
				{{ placeholder.text }}
			</v-chip>
		</v-chip-group>

		<v-alert v-else type="info" dense text class="flex-grow-1 mb-0">
{{ $t("common.there-is-no-placeholders-yet") }} !
</v-alert>
		<v-menu
			v-model="placeholderMenuModel"
			:position-x="placeholderMenuPositionX"
			:position-y="placeholderMenuPositionY"
			auto
			bottom
			allow-overflow
		>
			<v-card>
				<v-list-item dense @click="insertPlaceHolder(currentMenuPlaceholder.value)">
					<v-list-item-content>
						<v-list-item-title>Insert</v-list-item-title>
					</v-list-item-content>
				</v-list-item>
				<v-list-item dense @click="deletePlaceholder(currentMenuPlaceholder.value)">
					<v-list-item-content>
						<v-list-item-title>Delete</v-list-item-title>
					</v-list-item-content>
				</v-list-item>
			</v-card>
		</v-menu>
		<v-btn icon @click="placeHolderDialogModel = true">
<v-icon>mdi-plus</v-icon>
</v-btn>
		<v-dialog v-model="placeHolderDialogModel" max-width="300" @update:return-value="placeHolderDialogHandler">
			<v-card>
				<v-card-title> {{ $t("common.add-new-placeholder") }}   </v-card-title>
				<v-card-text>
					<v-form ref="addPlaceHolderForm">
						<vc-text-field
							v-model="newPlaceholder.label"
							dense
							:label="$t('common.label')"
							:rules="[$rules.required('common.placeholder')]"
						/>
						<vc-text-field
							v-model="newPlaceholder.value"
							dense
							:label="$t('common.value')"
							:rules="[$rules.required('common.placeholder')]"
						/>
					</v-form>
				</v-card-text>
				<v-card-actions>
					<v-spacer />
					<v-btn text @click="placeHolderDialogModel = false">
{{ $t("common.cancel") }}
</v-btn>
					<v-btn text color="primary" @click="addPlaceHolder">
{{ $t("common.add") }}
</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>

<script>
export default {
	props: {
		placeholders: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			placeHolderDialogModel: false,
			placeholderMenuModel: false,
			placeholderMenuPositionX: 0,
			placeholderMenuPositionY: 0,
			currentMenuPlaceholder: {},
			newPlaceholder: {
				value: null,
				text: null,
			},
		}
	},
	computed: {
		parsedPlaceholders: {
			get() {
				return this.placeholders.map((p) => {
					if (typeof p.menuModel === "undefined") {
						this.$set(p, "menuModel", false)
					}
					return p
				})
			},
		},
	},
	methods: {
		addPlaceHolder() {
			this.$refs.addPlaceHolderForm.validate()
			if (this.$refs.addPlaceHolderForm.validate()) {
				this.$emit("add", {
					value: this.newPlaceholder.value,
					text: this.newPlaceholder.label,
				})
				this.newPlaceholder = {
					value: null,
					text: null,
				}
				this.placeHolderDialogModel = false
			}
		},
		insertPlaceHolder(placeholder) {
			this.$emit("insert", placeholder)
		},
		placeHolderDialogHandler(val) {
			if (!val) {
				this.$refs.addPlaceHolderForm.reset()
			}
		},
		placeholderMenu(placeholder) {
			this.currentMenuPlaceholder = placeholder

			// find el coordinates by id
			const el = document.getElementById(`placeholder-${placeholder.value}`)
			const rect = el.getBoundingClientRect()
			this.placeholderMenuPositionX = rect.x + rect.width / 2
			this.placeholderMenuPositionY = rect.y + 14

			this.placeholderMenuModel = true
		},
		deletePlaceholder(placeholder) {
			this.$emit("delete", placeholder)
		},
	},
}
</script>
