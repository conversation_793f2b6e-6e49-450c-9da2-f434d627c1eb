<template>
	<v-autocomplete
		v-model="selectedItem"
		:search-input.sync="search"
		label="Search"
		single-line
		:items="searchItems"
		:loading="$fetchState.pending"
		chips
		dense
		clearable
		return-object
		prepend-inner-icon="mdi-magnify"
		@change="emitResults"
	>
		<template #selection="{ item }">
			<v-chip>{{ item.value }}</v-chip>
		</template>
		<template #item="{ item, attrs, on, selected }">
			<v-list-item v-bind="attrs" :input-value="selected" v-on="on">
				<v-list-item-content>
					<v-list-item-title>{{ item.text }}</v-list-item-title>
					<v-list-item-subtitle>{{ item.value }}</v-list-item-subtitle>
				</v-list-item-content>
			</v-list-item>
		</template>
	</v-autocomplete>
</template>

<script>
export default {
	data() {
		return {
			selectedItem: null,
			search: null,
			oldMessages: [],
		}
	},
	async fetch() {
		await this.$axios.$getOnce("/local/translations/old", { baseURL: "/" }).then((res) => {
			this.oldMessages = res
		})
	},
	computed: {
		items() {
			const arr = []
			const locale = this.$i18n.locale
			const messages = this.oldMessages[locale]
			for (const key in messages) {
				arr.push({ text: messages[key], value: key, locale })
			}
			return arr
		},
		searchItems() {
			if (!this.search) { return this.items }
			return this.items.filter(item => item.text.toLowerCase().includes(this.search.toLowerCase()))
		},
	},
	methods: {
		emitResults(obj) {
			if (!obj) { return }

			const result = {}
			this.$store.state.languages.forEach((item) => {
				result[item.abbreviation] = { key: obj.value, value: this.oldMessages?.[item.abbreviation]?.[obj.value] || null }
			})

			this.$emit("change", result)
		},
	},
}
</script>
