<template>
	<v-input v-bind="$attrs" :value="internalValue">
		<v-chip-group v-model="internalValue" multiple column color="success">
			<v-chip v-for="chip in items" :key="chip.value" filter :value="chip">
{{ chip.text }}
</v-chip>
		</v-chip-group>
	</v-input>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => [],
		},
	},

	data() {
		return {
			items: [],
		}
	},
	async fetch() {
		await this.$axios.$getOnce("/v1/lookups/translation-tags").then((res) => {
			this.items = res
		})
	},
	computed: {
		internalValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
}
</script>
