<template>
	<div v-if="lang && lang.countable">
		<div v-for="(countable, index) in lang.countable.length" :key="index">
			<vc-text-field
				ref="containedRef"
				:value="localValue[index]"
				:label="countables[index].text"
				:dir="lang.direction"
				:disabled="disabled"
				@focus="inputIndex(index)"
				@input="setValue(index, $event)"
			/>
		</div>
	</div>
	<div v-else class="mt-4">
		<v-chip color="warning" small outlined class="text-uppercase me-1 mb-1" label>
			{{ $t("countable.no-countable-found-for-this-language") }}
		</v-chip>
	</div>
</template>

<script>
import { countableEnum } from "~/config/Enum"
import translationType from "~/mixins/translationType"

export default {
	mixins: [translationType],
	data() {
		return {
			countables: countableEnum,
      currentIndex: null,
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value ? this.stripTags(this.value).split("|") : []
			},
			set(value) {
				this.$emit("input", this.stripTags(value.join("|")))
			},
		},
	},
	methods: {
		setValue(index, value) {
			const arr = this.$cloneDeep(this.localValue)
			arr[index] = value
			this.localValue = arr
		},
    insertPlaceHolder(value) {
			const inputComponent = this.$refs.containedRef[this.currentIndex]
      this.addLabel(inputComponent, value)
		},
	},
}
</script>
