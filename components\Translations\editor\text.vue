<template>
	<vc-textarea
		ref="containedRef"
		v-model="localValue"
		:dir="lang.direction"
		:disabled="disabled"
		@focus="inputIndex(0)"
	/>
</template>

<script>
import translationType from "~/mixins/translationType"
export default {
	mixins: [translationType],
  computed: {
		localValue: {
			get() {
				return this.stripTags(this.value)
			},
			set(value) {
				this.$emit("input", this.stripTags(value))
			},
		},
	},
}
</script>
