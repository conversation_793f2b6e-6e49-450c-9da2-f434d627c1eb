<template>
	<v-input
		ref="thisComp"
		:rules="rules"
		:label="label"
		class="upload-file"
		:hint="allowedFilesText"
		persistent-hint
		:value="localValue"
		v-on="$listeners"
	>
		<div class="upload-images mb-4">
			<input
				ref="fileInput"
				type="file"
				:accept="types ? allowedFilesMime.join(', ') : undefined"
				class="d-none"
				multiple
				tabindex="-1"
				@change="onFileChanged"
			>

			<transition-group name="list" tag="div" class="row">
				<template v-for="file in filesAsArray">
					<v-col :key="'image-' + file.name" class="flex-grow-0">
						<v-hover>
							<template #default="{ hover }">
								<v-responsive :aspect-ratio="aspectRatio" :width="width">
									<v-card outlined width="100%" height="100%">
										<v-img v-if="file.preview" contain :src="file.preview" :aspect-ratio="aspectRatio" :width="width">
											<div
												class="w-100 fill-height d-flex align-center justify-center"
												:class="{ 'is-uploading': file.uploading }"
											>
												<v-scale-transition appear mode="out-in">
													<v-progress-circular
														v-if="file.uploading"
														:value="file.uploading"
														:size="70"
														color="rgb(255 255 255 / 70%)"
														width="5"
													>
														<div class="text-h6 font-weight-bold">
{{ file.uploading }}%
</div>
													</v-progress-circular>
												</v-scale-transition>
											</div>
										</v-img>
										<!-- <v-sheet v-else color="transparent" :width="100" :height="(100 * 3) / 4" class="pa-2">
											<div class="fi" :class="{ ['fi-' + file.type]: true }">
												<div class="fi-content">{{ file.ext }}</div>
											</div>
										</v-sheet> -->
										<!-- <div v-tooltip="file.src" class="text-center text-caption line-clamp-1">{{ file.src }}</div> -->

										<v-fade-transition>
											<v-overlay v-if="hover" absolute opacity="0.8">
												<div class="d-flex text-center justify-space-around">
													<div class="text-caption">
														<v-btn
															v-tooltip="$t('common.delete')"
															:aria-label="$t('common.delete')"
															icon
															small
															class="px-2"
															color="white"
															@click="del(file.src)"
														>
															<v-icon>mdi-delete</v-icon>
														</v-btn>
													</div>
												</div>
											</v-overlay>
										</v-fade-transition>
									</v-card>
								</v-responsive>
							</template>
						</v-hover>
					</v-col>
				</template>
				<v-col v-if="remainingAllowed" key="add-file">
					<v-responsive :aspect-ratio="aspectRatio">
						<v-card light :color="color" flat class="d-flex justify-center align-center" :width="width" height="100%">
							<v-btn width="100%" height="100%" plain @click="upload">
								<div class="d-flex flex-column justify-center align-center">
									<v-icon x-large>
mdi-plus
</v-icon>
									<div class="my-1">
{{ $t("common.upload") }}
</div>
								</div>
							</v-btn>
						</v-card>
					</v-responsive>
				</v-col>
			</transition-group>
		</div>
	</v-input>
</template>

<script>
import mime from "mime-type/with-db"
import field from "../mixins/field"
require("css-file-icons/build/css-file-icons.css")
export default {
	mixins: [field],
	props: {
		value: {
			type: [String, Array],
			default: null,
		},
		max: {
			type: Number,
			default: 1,
		},
		types: {
			type: [Array, Boolean],
			default: false,
		},
		rules: {
			type: Array,
			default: () => [],
		},
		label: {
			type: String,
			default: null,
		},
		width: {
			type: [String, Number],
			default: 200,
		},
		aspectRatio: {
			type: [Number],
			default: 1,
		},
		color: {
			type: String,
			default: "rgb(240 240 240)",
		},
	},
	data() {
		return {}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(v) {
				this.$emit("input", v)
			},
		},
		filesAsArray() {
			if (this.isMultiple) {
				return this.localValue
			} else if (this.localValue?.src) {
				return [this.localValue]
			} else {
				return []
			}
		},
		remainingAllowed() {
			return this.max - this.filesAsArray.length
		},

		allowedFilesText() {
			if (!this.types) {
				return null // return all files if no types are specified
			}
			// return string after the "/"
			// only {files} types are allowed
			return this.$t("hint.allowed-file-types", { files: this.types.join(", ") })
		},
		allowedFilesMime() {
			if (!this.types) {
				return []
			}
			return this.types.map(item => mime.contentType(item))
		},
		isMultiple() {
			return this.max > 1
		},
	},

	methods: {
		upload() {
			this.$refs.fileInput.click()
		},
		onFileChanged(e) {
			for (let i = 0; e.target.files.length > i; i++) {
				if (e.target.files[i] instanceof File) {
					const ext = e.target.files[i].name.split(".").pop()
					if (this.remainingAllowed) {
						if (!this.types || this.types.includes(ext)) {
							const file = e.target.files[i]
							console.log("🚀 ~ file: Upload.vue:188 ~ onFileChanged ~ file:", file)

							const refFile = this.add(file)

							refFile.uploading = true
							const formData = new FormData()
							formData.append("file", file)
							this.$axios
								.$post("/v1/upload-media", formData, {
									onUploadProgress: (progressEvent) => {
										const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)

										refFile.uploading = percentCompleted
									},
								})
								.then((resp) => {
									refFile.uploading = false
									this.setFileItem(refFile.src, resp)
								})
								.catch((e) => {
									this.del(refFile.src)
									refFile.uploading = false
									this.genericErrorHandler(e)
								})
						}
					}
				}
			}
			this.loading = false

			this.$refs.fileInput.value = ""
		},

		del(file) {
			this.$confirm(this.$t("messages.confirm-delete-text"), { title: this.$t("messages.confirm-delete-title") }).then(
				(isAccepted) => {
					if (isAccepted) {
						if (this.isMultiple) {
							this.localValue = this.localValue.filter(item => item.src !== file)
						} else {
							this.localValue = null
						}
						console.log("del", file, this.files)
						this.$delete(this.files, file)
					}
				},
			)
		},
		setFileItem(oldName, newObject) {
			newObject.isNew = true
			if (this.isMultiple) {
				this.localValue = this.localValue.map((item) => {
					if (item.src === oldName) {
						return newObject
					}
					return item
				})
			} else {
				this.localValue = newObject
			}
		},
		async add(fileObject) {
			const file = {
				src: fileObject.name,
				preview: await this.toBase64(fileObject),
				fileSize: fileObject.size,
				mimeType: fileObject.type,
			}
			if (this.isMultiple) {
				if (!Array.isArray(this.localValue)) {
					this.localValue = []
				}
				this.localValue.push(file)
				// return the new file object from its reference
				return this.localValue[this.localValue.length - 1]
			} else {
				this.localValue = file
				return this.localValue
			}
		},

		toBase64(file) {
			return new Promise((resolve, reject) => {
				const reader = new FileReader()
				reader.readAsDataURL(file)
				reader.onload = () => resolve(reader.result)
				reader.onerror = error => reject(error)
			})
		},
	},
}
</script>
<style lang="scss">
@import "~/assets/mixins.scss";
.upload-images {
	border: 1px solid transparent;
	padding: 8px;
}
.v-input--has-state.error--text .upload-images {
	border: 1px solid #dd2c00;
}
.upload-file {
	padding-top: 12px;
	.v-label {
		position: absolute !important;
		transform: translateY(-24px);
		top: 8px;
		font-size: 14px;
		@include dir("transform-origin", top left, top right);
		@include dir("left", 0 !important, auto !important);
		@include dir("right", auto !important, 0!important);
	}
	.is-uploading {
		background-color: rgba(0, 0, 0, 0.5);
	}
}
.fi.large {
	width: 69px;
	height: 75px;
	padding: 33.5px 0.3em 0;
	.fi-content {
		padding-top: 42px;
		font-size: 18px;
	}
}
</style>
