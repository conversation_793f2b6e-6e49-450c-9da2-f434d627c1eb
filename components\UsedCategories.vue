<template>
	<vc-autocomplete
		v-model="localValue"
		multiple
		:api="api"
		label="Category"
		v-bind="$attrs"
		v-on="$listeners"
	/>
</template>

<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => [],
		},
		multiple: {
			type: Boolean,
			default: true,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		api() {
			return "/v1/lookups/used-categories"
		},
	},
}
</script>
