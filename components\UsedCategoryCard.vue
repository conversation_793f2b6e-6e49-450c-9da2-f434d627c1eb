<template>
<div class="d-flex align-center mb-2">
		<nuxt-link
			:to="
				localePath({
					name: 'used-category',
					params: {
						usedCategory: `${data.slug}`,
					},
				})
			"
			class="used-category-card"
		>
			<!--<div v-if="category.media.cover" class="content-wrapper">
				<img class="img" :src="category.media.cover.url" />
			</div>-->

			<v-sheet class="bg" rounded>
				<div class="text">
{{ data.name }}
</div>
			</v-sheet>
		</nuxt-link>
	</div>
</template>

<script>
export default {
    props: {
        data: {
            type: Object,
            default: () => {},
        },
    },
}
</script>

<style lang="scss">
.used-category-card {
    position: relative;
    height: 100px;
    display: flex;
    flex-direction: column;
    min-width: 180px;
    margin-inline-end: 8px;
    text-decoration: none !important;

    .content-wrapper {
        display: flex;

        align-items: center;
        position: absolute;
        top: 0;
        bottom: 4px;
        left: 4px;
        right: 4px;

        .img {
            max-height: 100%;
            max-width: 100px;
            //margin-top: 4px;
            bottom: 5px;
            position: absolute;
            //object-fit: cover;
        }
    }

    .bg {
        height: 70%;
        margin-top: auto;
        background-color: var(--v-accent13-base);
        width: 100%;
        padding-inline-start: 104px;
        display: flex;
        align-items: center;

        .text {
            flex-grow: 1;
            font-size: large;
            font-weight: 400;
        }
    }
}
</style>
