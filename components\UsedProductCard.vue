<template>
    <v-card
class="used-product-card d-flex flex-column mb-2 fill-height"

    :to="localePath({ name: 'used-product',	params: {
						usedProduct:`${item.slug}`
					},})"

    flat hover :elevation="isHovered ? 5 : 0" :width="width" @mouseenter="carouselStartHandler" @mouseleave="carouselStopHandler">
        <div>
            <div>
                <v-sheet color="accent13" class="image-wrapper">
                    <v-carousel v-model="carouselModel" :height="200" :show-arrows="isHovered" :cycle="isHovered" :hide-delimiters="!isHovered" hide-delimiter-background>
                        <template #next="{ on }">
                            <v-btn icon v-on="on" @click.prevent>
                                <v-icon>mdi-chevron-right</v-icon>
                            </v-btn>
                        </template>
                        <template #prev="{ on }">
                            <v-btn icon v-on="on" @click.prevent>
                                <v-icon>mdi-chevron-left</v-icon>
                            </v-btn>
                        </template>
                        <v-carousel-item v-for="(image, i) in media.covers" :key="i" :eager="isHovered">
                            <img :src="image.url" height="200">
                        </v-carousel-item>
                    </v-carousel>
</v-sheet>
                <v-spacer />
                <div class="c-row justify-end align-end">
                    <!--<p-cashback v-if="data.cashback">

                         <small>{{ data.currency }}</small> {{ data.cashback }}

                         </p-cashback>-->
                </div>
            </div>
            <v-progress-linear :value="100" height="2" />
            </v-sheet>
        </div>

        <v-card-text>
            <div class="d-flex align-center pt-1">
                <!--	<div v-if="item.category" class="text--secondary text-subtitle-2">

{{ item.category.name }}

</div>

<v-spacer />
                        -->
            </div>
            <div class="title pb-1">
                {{ item.name }}
            </div>
            <div v-if="item.price" class="price-wrapper">
                <span class="price--text">
                    <span class="font-weight-bold text-body-1 value">{{ item.price.price }}</span></span>
                <small>{{ item.price.currency.name }}</small>
            </div>
            <div v-if="item.user" class="d-flex justify-end">
                <v-avatar>
                    <v-icon dark>
                        mdi-account-circle
                    </v-icon>
                </v-avatar>
                <span class="font-weight-bold text-body-1 value">{{ item.user.fullName }}</span></span>
            </div>
        </v-card-text>
        </div>
    </v-card>
</template>

<script>
export default {
	props: {
		item: {
			type: [Object, Number],
			default: () => ({}),
		},
		loading: {
			type: Boolean,
			default: false,
		},
		width: {
			type: Number,
			default: 300,
		},
		categoryId: {
			type: Number,
			default: 0,
		},
	},

	data() {
		return {
			carouselModel: 0,
			isHovered: false,
			media: {
				covers: [
					{
						url: "/images/demo1.png",
					},
					{
						url: "/images/demo2.png",
					},
					{
						url: "/images/demo1.png",
					},
				],
			},
			isWishlisting: false,
			isComparing: false,
		}
	},

	methods: {
		carouselStartHandler() {
			this.isHovered = true
			setTimeout(() => {
				if (this.isHovered) { this.carouselModel = 1 }
			}, 200)
		},
		carouselStopHandler() {
			this.isHovered = false
			setTimeout(() => {
				if (!this.isHovered) { this.carouselModel = 0 }
			}, 200)
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/variables.scss";

.used-product-card {
	.image-wrapper {
		position: relative;
		overflow: hidden;

		img {
			//	filter: $product-drop-shadow;
			padding: 16px 0;
			margin-inline: auto;
			display: block;
		}

		.overlay {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			flex-direction: column;

			.c-row {
				padding: 8px;
				display: flex;
				justify-content: space-between;
				flex: content;
			}
		}
	}

	.price-wrapper {
		.value {
			font-size: 20px !important;
		}
	}

	&.has-discount {
		.image-wrapper {
			img {
				padding-top: 36px;
			}
		}
	}

	.v-carousel__controls__item.v-btn {
		color: #000 !important;

		.v-icon {
			width: 18px;
			height: 18px;
			margin: 0 4px !important;
			//opacity: 0.9;
		}
	}
}
</style>
