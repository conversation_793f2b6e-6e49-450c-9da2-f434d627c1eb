<template>
	<v-menu v-model="model" :close-on-content-click="false" open-delay="300" open-on-hover>
		<template #activator="{ on }">
			<v-chip pill outlined class="curser-pointer" v-on="on">
				<avatar :src="data.profile_pic" class="elevation-1" />

				{{ data.first_name }} {{ data.last_name }}
			</v-chip>
		</template>
		<v-card class="d-flex" max-width="450">
			<v-img aspect-ratio="1" width="170" class="border-tr-0 border-br-0" :src="profilePic" />

			<div class="flex-grow-1">
				<v-card-title>
					<div>{{ data.first_name }} {{ data.last_name }}</div>
				</v-card-title>
				<v-card-text>
					<!-- <div class="text-caption"><b>Wallet Amount:</b> 685$</div>
					<div class="text-caption"><b>Entity:</b> Australia</div>
					<div class="text-caption"><b>Last Login:</b> 03-02-2022</div>
					<div class="text-caption"><b>Member Since:</b> 22-01-2019</div> -->
					<div class="text-caption">
						<b>{{ $t("common.email") }}</b> {{ data.email }}
					</div>
					<div class="text-caption">
						<b>{{ $t("common.phone") }}</b> <a :href="'tel:' + data.full_phone">{{ data.full_phone | phone }}</a>
					</div>
					<div class="text-caption">
						<b>{{ $t("common.address") }}</b> {{ data.address }}
					</div>
				</v-card-text>
				<v-card-actions>
					<v-spacer />
					<v-btn color="primary" exact :to="localePath('/')" text small>
More Details
</v-btn>
				</v-card-actions>
			</div>
		</v-card>
	</v-menu>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			model: false,
		}
	},
	computed: {
		profilePic() {
			return this.data.profile_pic || "/images/default-avatar.jpg"
		},
	},
}
</script>
