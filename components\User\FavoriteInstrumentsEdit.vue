<template>
	<v-dialog v-model="dialogModel" max-width="800">
		<v-card>
			<v-card-title> Edit Instruments</v-card-title>
			<v-card-text>
				<v-row dense>
					<v-col>
						<vc-text-field
							v-model="searchModel"
							label="Search"
							placeholder="Type instrument symbol. Ex: EURUSD"
							hide-details
							dense
							clearable
							outlined
							prepend-inner-icon="mdi-magnify"
							autofocus
						/>
					</v-col>
				</v-row>
				<v-row dense>
					<v-col cols="12" md="4">
						<v-tabs ref="instrumentsTabs" v-model="instrumentModel" vertical class="mt-12">
							<v-tab v-for="instrument in instruments" :key="instrument.value" class="justify-start">
								{{ instrument.text }}
							</v-tab>
						</v-tabs>
					</v-col>
					<v-col cols="12" md="8">
						<v-tabs v-model="selectionModel" fixed-tabs class="mb-2">
							<v-tab>
								All <small class="mx-1">({{ countSymbolsInCurrentTab }})</small>
							</v-tab>
							<v-tab>
								Selected <small class="mx-1">({{ countSelectedSymbolsInCurrentTab }})</small>
							</v-tab>
						</v-tabs>

						<v-sheet :max-height="48 * Object.keys(instruments).length" class="overflow-y-auto mb-2">
							<v-chip-group v-model="favoredInstruments" :max="maximumFavoriteLength" column multiple color="success">
								<v-scale-transition group leave-absolute tag="div">
									<v-chip
										v-for="symbol in symbols"
										v-show="selectedInstrument && selectedInstrument.items.includes(symbol)"
										:key="symbol"
										:value="symbol"
										filter
									>
										{{ symbol }}
									</v-chip>
								</v-scale-transition>
							</v-chip-group>
						</v-sheet>
						<v-alert v-if="!countSymbolsInCurrentTab" text type="warning" icon="mdi-alert">
							<template v-if="searchModel">
No instruments matching your search "{{ searchModel }}"
</template>
							<template v-else>
								No selected instruments in this category.
								<v-btn small color="warning" class="ma-2" outlined @click="selectionModel = 0">
See all symbols
</v-btn>
							</template>
						</v-alert>
						<v-alert v-if="countSelectedSymbolsInCurrentTab === maximumFavoriteLength" text type="warning" icon="mdi-alert">
							You have reached the maximum number of instruments in your favorites.
						</v-alert>
					</v-col>
				</v-row>
			</v-card-text>
			<v-card-actions>
				<v-spacer />
				<v-btn text @click="dialogModel = false">
Cancel
</v-btn>
				<v-btn text color="primary" @click="test">
Save
</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script>
export default {
	data() {
		return {
			dialogModel: false,
			searchModel: null,
			selectionModel: 0,
			instrumentModel: 0,
			maximumFavoriteLength: 10,
			favoredInstruments: ["Ethereum", "BNB"],
			// instruments each one have 20 items
			instruments: [
				{ text: "All Categories", value: "all" },
				{
					text: "Cryptocurrency",
					value: "cryptocurrency",
					items: ["Bitcoin", "BNB", "Ethereum", "USDT", "USDC", "XRP", "TRX", "WBTC", "ETC", "XMR"],
				},
				{ text: "Commodities", value: "commodities", items: ["Corn", "Wheat", "Soybeans", "Oil", "Olives", "Food"] },
				{
					text: "Indices",
					value: "indices",
					items: [
						"S&P 500",
						"NASDAQ",
						"DJIA",
						"IGHK",
						"AQWU",
						"KMCD",
						"UTER",
						"ZMKI",
						"KQOO",
						"S&P 500",
						"NASDAQ",
						"DJIA",
						"IGHK",
						"AQWU",
						"KMCD",
						"UTER",
						"ZMKI",
						"KQOO",
					],
				},
				{ text: "ETF's", value: "etfs", items: ["SPY", "QQQ", "DIA", "UHJ", "ZAU", "UYC", "POU", "UYR"] },
				{ text: "Forex", value: "forex", items: ["EURUSD", "GBPUSD", "USDJPY", "JORUSD", "USDGPP", "UTSLKU"] },
			],
		}
	},
	computed: {
		selectedInstrument() {
			let i = {}
			if (this.instrumentModel) {
				i = this.$cloneDeep(this.instruments[this.instrumentModel]) || { items: [] }
			} else {
				i = { ...this.instruments[0], items: this.symbols }
			}

			if (this.selectionModel) {
				i.items = i.items.filter(item => this.favoredInstruments.includes(item))
			}

			if (this.searchModel) {
				i.items = i.items.filter(item => item.toLowerCase().includes(this.searchModel.toLowerCase()))
			}
			return i
		},

		symbols() {
			const symbols = []
			this.instruments.forEach((instrument) => {
				instrument?.items?.forEach((item) => {
					symbols.push(item)
				})
			})
			return symbols
		},
		symbolsInCurrentTab() {
			return this.symbols.filter(symbol => this.selectedInstrument.items.includes(symbol))
		},
		countSymbolsInCurrentTab() {
			return this.symbolsInCurrentTab.length
		},
		selectedSymbolsInCurrentTab() {
			return this.symbolsInCurrentTab.filter(symbol => this.favoredInstruments.includes(symbol))
		},
		countSelectedSymbolsInCurrentTab() {
			return this.selectedSymbolsInCurrentTab.length
		},
	},
	mounted() {
		this.$nuxt.$on("edit-favorite-instruments", () => {
			this.dialogModel = true
		})
	},
	methods: {
		test() {
			console.log(this.$refs.instrumentsTabs)
		},
	},
}
</script>
