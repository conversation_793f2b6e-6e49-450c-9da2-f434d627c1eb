<template>
	<div>
		<v-btn id="user-menu" text large :loading="isLoggingOut">
			<avatar size="42" eager :src="$auth.user.media?.avatar?.preview" class="me-3" />
			<div>
				<div class="text-subtitle-1">
					Welcome {{ $auth.user.firstName }}
				</div>
				<div class="text--secondary" dir="ltr">
					{{ $auth.user.phone | phone }}
				</div>
			</div>
		</v-btn>
		<v-menu activator="#user-menu" close-delay="200" open-on-hover offset-y>
			<v-card>
				<v-list>
					<v-list-item-group>
						<v-list-item v-show="checkHaveAdminDashboard()" :to="localePath({ name: 'dashboard' })">
							<v-list-item-action>
								<v-icon>mdi mdi-account-outline</v-icon>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>{{ $t("common.admin-dashboard") }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
						<v-list-item :to="localePath({ name: 'my-account' })">
							<v-list-item-action>
								<v-icon>mdi mdi-account-outline</v-icon>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>{{ $t("common.my-account") }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
						<v-list-item :to="localePath({ name: 'my-orders' })">
							<v-list-item-action>
								<v-icon>mdi mdi-cart-outline</v-icon>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>{{ $t("common.my-orders") }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>

						<v-list-item :to="localePath({ name: 'my-ads' })">
							<v-list-item-action>
								<v-icon>mdi-format-list-bulleted-square</v-icon>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>{{ $t("common.my-ads") }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>

						<v-list-item :to="localePath({ name: 'my-wishlist' })">
							<v-list-item-action>
								<v-icon>mdi mdi-heart-outline</v-icon>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>{{ $t("common.my-wishlist") }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
						<v-list-item :to="localePath({ name: 'my-addresses' })">
							<v-list-item-action>
								<v-icon>mdi mdi-map-marker-outline</v-icon>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>{{ $t("common.my-addresses") }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
						<v-list-item @click="logout">
							<v-list-item-action>
								<v-icon>mdi mdi-logout</v-icon>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>{{ $t("common.logout") }}</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
					</v-list-item-group>
				</v-list>
			</v-card>
		</v-menu>
	</div>
</template>

<script>
export default {
	data() {
		return {
			isLoggingOut: false,
			setVisitorId: false,
			isInitiating: false,
		}
	},

	methods: {
		checkHaveAdminDashboard() {
			return (
				Array.isArray(this.$auth.user.roles) &&
				(this.$auth.user.roles.includes("admin") ||
					this.$auth.user.roles.includes("editor") ||
					this.$auth.user.roles.includes("accounting"))
			)
		},
		logout() {
			this.isLoggingOut = true
			this.$auth
				.logout()
				.then(() => {
					// check if this route has the middleware auth
					this.$router.push(this.localePath({ name: "index" }))
				})
				.finally(() => {
					this.isLoggingOut = false
				})
		},
	},
}
</script>
