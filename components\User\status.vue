<template>
	<v-sheet tag="span" class="user-status colored-bg text-caption px-4 py-1 text-center" rounded :class="color + '--text'">
		<v-icon :color="color" small left v-text="icon" />{{ text }}
	</v-sheet>
</template>

<script>
export default {
	props: {
		status: {
			type: String,
			required: true,
			validation: value => ["approved", "pending"].includes(value),
		},
	},
	data() {
		return {
			statuses: {
				approved: {
					color: "success",
					text: "Approved",
					icon: "mdi-account-check",
				},
				pending: {
					color: "warning",
					text: "Pending",
					icon: "mdi-account-clock",
				},
			},
		}
	},
	computed: {
		text() {
			return this.statuses[this.status].text
		},
		color() {
			return this.statuses[this.status].color
		},
		icon() {
			return this.statuses[this.status].icon
		},
	},
}
</script>
