<template>
	<v-card outlined @click="$emit('click', item)">
		<v-list-item two-line link :class="{ 'border-default': item.isDefault }">
			<v-list-item-icon>
				<v-img width="100" :aspect-ratio="4 / 3" :src="computedSrc" />
			</v-list-item-icon>
			<v-list-item-content>
				<v-list-item-title @click.stop>
					{{ item.name | trans }}
				</v-list-item-title>
				<v-list-item-subtitle>{{ item.description | trans }}</v-list-item-subtitle>
				<div class="d-flex align-center">
					<slot name="actions" />
				</div>
				<div v-for="(stock, index) in item.stocks" :key="index" class="row stock-card d-flex mt-2" @click.stop>
					<div class="col-1">
						index #{{ index + 1 }}
					</div>
					<div class="col-3">
						<!-- <vc-text-field v-model.number="stock.quantity" type="number" min="1" label="Quantity"
							hide-spin-buttons :rules="[$rules.required('quantity')]" /> -->
							<vc-text-field v-model.number="stock.quantity" type="number" min="1" label="Quantity"
               hide-spin-buttons :rules="[value => value !== null && value !== undefined && Number.isInteger(value) || 'Quantity must be a whole number']" />
					</div>
					<div class="col-3">
						<money-field v-model="stock.price" :label="priceLabel" :rules="[$rules.required('Price')]" />
					</div>
					<div class="col-3">
						<money-field v-model="stock.cost" disable-currency label="Cost" />
					</div>
					<div class="col-2 text-center pt-4">
						<vc-autocomplete v-model="stock.supplierId" return-object label="Supplier" api="/v1/lookups/suppliers"
							:rules="[$rules.required('Supplier')]" @change="supplierHandler($event, stock)" />
					</div>
				</div>
			</v-list-item-content>
		</v-list-item>
	</v-card>
</template>

<script>

export default {
	props: {
		item: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			inStock: true, // Default stock status
		}
	},
	computed: {
		computedSrc() {
			return (
				this.item.media.cover?.[0]?.src ||
				this.item.media.gallery?.[0]?.src ||
				"/images/product-placeholder.webp"
			)
		},
	},
	methods: {
		isStockAvailable(stock) {
			return stock.quantity > 0 && stock.isPublished === true
		},
		supplierHandler(data, stock) {
			console.log("🚀 ~ file: Stocks.vue:137 ~ supplierHandler ~ data, item:", data, stock)

			stock.supplierId = data.value
			stock.supplier = { ...data.meta, name: data.text }
		},

	},
}
</script>
<style>
.border-default {
	border: 4px solid purple
}
</style>
