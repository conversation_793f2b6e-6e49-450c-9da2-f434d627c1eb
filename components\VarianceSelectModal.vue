<template>
	<div>
		<slot name="activator" v-bind="{ on }" />
		<v-dialog v-model="dialogModel" max-width="800">
			<v-card>
				<v-card-title> Select Variance </v-card-title>
				<v-card-text>
					<v-toolbar flat>
						<v-text-field v-model="search" append-icon="mdi-magnify" label="Search" single-line
							:loading="$fetchState.pending" hide-details @keydown.enter="$fetch" @input="debouncedFetch" />
						<v-spacer />
					</v-toolbar>
					<v-divider />
					<v-row>
						<transition-group name="list" class="row" tag="div">
							<v-col v-for="variance in variances" :key="variance.varianceId" cols="3" md="3">
								<admin-variance-card :item="variance" @click.stop="selectVariance(variance)" />
							</v-col>
						</transition-group>
					</v-row>
				</v-card-text>
			</v-card>
		</v-dialog>
	</div>
</template>

<script>
import debounce from "debounce"

export default {
	props: {
		inStock: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			dialogModel: false,
			variances: [],
			cancelToken: null,
			pagination: {},
			search: "",
		}
	},
	async fetch() {
		if (this.cancelToken && typeof this.cancelToken.cancel === "function") {
			this.cancelToken.cancel()
		}
		this.cancelToken = this.$axios.CancelToken.source()

		await this.$axios
			.$get(`/v1/variances`, {
				params: {
					orderBy: this.orderBy,
					q: this.search,
				},
				cancelToken: this.cancelToken.token,
			})
			.then((resp) => {
				this.variances = resp.items
				this.pagination = resp.pagination
			})
			.catch(() => {
				this.variances = []
				this.hasError = true
			})
	},
	computed: {
		on() {
			return {
				click: this.open,
			}
		},
	},
	methods: {
		open() {
			this.dialogModel = true
		},
		close() {
			this.dialogModel = false
		},
		selectVariance(varianceParam) {
			// update v-model
			const variance = {
				varianceId: varianceParam.varianceId,
				name: varianceParam.name,
				slug: varianceParam.slug,
				brandId: varianceParam.brandId,
				SKU: varianceParam.SKU,
				description: varianceParam.description,
				metaTitle: varianceParam.metaTitle,
				metaDescription: varianceParam.metaDescription,
				media: varianceParam.media,
			}
			this.$emit("input", variance)
			this.close()
		},
		debouncedFetch() {
			return debounce(this.$fetch, 500)()
		},
	},
}
</script>
