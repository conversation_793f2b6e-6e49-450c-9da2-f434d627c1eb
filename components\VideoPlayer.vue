<template>
	<div class="text-center">
		<v-bottom-sheet v-model="localVideoModel" inset style="max-height: unset !important" content-class="sheet-video-player">
			<v-card tile class="d-flex flex-column">
				<video ref="video" width="100%" @timeupdate="timeupdate">
					<source :src="data.url" type="video/mp4">
				</video>
				<v-progress-linear :min="0" :max="videoDuration" :value="progress" class="my-0" height="5" />

				<v-list>
					<v-list-item>
						<v-list-item-content>
							<v-list-item-title
								>
{{ data.title }}
								<span class="text-caption ms-8"> {{ formattedCurrentTime }}/{{ formattedDuration }} </span>
							</v-list-item-title>
							<v-list-item-subtitle>{{ data.subtitle }}</v-list-item-subtitle>
						</v-list-item-content>

						<v-spacer />

						<v-list-item-icon>
							<v-btn icon @click="windBackward">
								<v-icon>mdi-rewind</v-icon>
							</v-btn>
						</v-list-item-icon>

						<v-list-item-icon :class="{ 'mx-5': $vuetify.breakpoint.mdAndUp }" class="my-auto">
							<v-btn v-if="isPlaying" x-large icon @click="pause">
								<v-icon x-large>
mdi-pause
</v-icon>
							</v-btn>
							<v-btn v-else icon x-large @click="play">
								<v-icon x-large>
mdi-play
</v-icon>
							</v-btn>
						</v-list-item-icon>

						<v-list-item-icon class="ml-0" :class="{ 'mr-3': $vuetify.breakpoint.mdAndUp }">
							<v-btn icon @click="windForward">
								<v-icon>mdi-fast-forward</v-icon>
							</v-btn>
						</v-list-item-icon>
					</v-list-item>
</v-list
				>
			</v-card>
		</v-bottom-sheet>
	</div>
</template>

<script>
export default {
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
		value: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			isPlaying: false,
			isReady: false,
			progress: 0,
			intervalRwd: null,
			currentTime: 0,
			videoDuration: 0,
			checkingIntervals: null,
		}
	},

	computed: {
		localVideoModel: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		formattedCurrentTime() {
			return this.formatSeconds(this.currentTime)
		},
		formattedDuration() {
			return this.formatSeconds(this.videoDuration)
		},
	},
	watch: {
		progress(val) {
			if (val === 100) {
				this.stop()
			}
		},
		value: {
			immediate: true,
			handler(val) {
				if (!val && this.isPlaying) {
					this.stop()
				} else if (val === true) {
					this.checkingIntervals = setInterval(() => {
						if (this.$refs.video && typeof this.$refs.video.play === "function") {
							this.isReady = true
							clearInterval(this.checkingIntervals)
						}
					}, 50)
				}
			},
		},
		isReady() {
			if (this.isReady) {
				this.play()
			}
		},
	},

	methods: {
		timeupdate(time) {
			this.currentTime = time.srcElement.currentTime
			this.videoDuration = time.srcElement.duration
			this.progress = (time.srcElement.currentTime / time.srcElement.duration) * 100
		},

		play() {
			this.isPlaying = true
			this.$refs.video.play()
		},
		pause() {
			this.isPlaying = false
			this.$refs.video.pause()
		},

		stop() {
			this.$refs.video.pause()
			this.isPlaying = false
			this.progress = 0
			this.$refs.video.currentTime = 0
		},

		windBackward() {
			if (this.$refs.video.currentTime <= 3) {
				this.$refs.video.currentTime = 0
			} else {
				this.$refs.video.currentTime -= 3
			}
		},

		windForward() {
			if (this.$refs.video.currentTime + 3 >= this.$refs.video.duration) {
				this.stop()
			} else {
				this.$refs.video.currentTime += 3
			}
		},
		setCurrentTime(v) {
			if (this.isReady) { this.$refs.video.currentTime = v }
		},
		formatSeconds(sec) {
			const date = new Date(0)
			date.setSeconds(sec) // specify value for SECONDS here
			return date.toISOString().substr(11, 8)
		},
	},
}
</script>

<style lang="scss">
.sheet-video-player {
	max-height: unset !important;
	.v-slider__thumb-container,
	.v-slider__track-background,
	.v-progress-linear,
	.v-slider__track-fill {
		transition-duration: 0.3s;
		transition-timing-function: linear;
	}
}
</style>
