<template>
	<website-filters-list v-model="localValue" :name="name" label="Brands" :items="items" type="v-checkbox" multiple v-on="$listeners">
		<template #item-title="{ item }">
			{{ item.text }}
		</template>
	</website-filters-list>

	<!-- <div>
		<v-subheader>Brands</v-subheader>
		<v-sheet max-height="200">
			<v-list-item-group v-model="localValue" multiple>
				<template v-for="(item, i) in parsedItems">
					<v-divider v-if="!item" :key="`divider-${i}`"></v-divider>
					<v-list-item v-else :key="`item-${i}`" class="small" dense :value="item.value" active-class="primary--text">
						<template #default="{ active }">
							<v-list-item-action>
								<v-checkbox dense :input-value="active" color="primary"></v-checkbox>
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>
									{{ item.text }}
								</v-list-item-title>
							</v-list-item-content>
							<v-list-item-action>
								<small>({{ item.meta.facet }})</small>
							</v-list-item-action>
						</template>
					</v-list-item>
				</template>
			</v-list-item-group>
			<v-btn plain small block @click="limited = !limited">
				<v-icon left small>{{ limited ? "mdi-plus" : "mdi-minus" }}</v-icon>
				<template v-if="limited">See More</template>
				<template v-else>See Less </template>
			</v-btn>
		</v-sheet>
	</div> -->
</template>

<script>
export default {
	props: {
		value: {
			type: [Array, Boolean],
			default: false,
		},
		items: {
			type: [Array],
			default: null,
		},
		name: {
			type: String,
			default: "",
		},
	},
	// data() {
	// 	return {
	// 		limited: true,
	// 	};
	// },
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit("input", val)
			},
		},
		// parsedItems() {
		// 	// sort items higher to lower by item.meta.facet and if limited return only first 5 items
		// 	return this.items
		// 		.slice()
		// 		.sort((a, b) => b.meta.facet - a.meta.facet)
		// 		.slice(0, this.limited ? 5 : this.items.length);
		// },
	},
}
</script>
