<template>
	<website-filters-list
		v-model="localValue"
		:name="name"
		:items="items"
		:config="config"
		type="v-checkbox"
		multiple
		v-on="$listeners"
	/>
</template>

<script>
export default {
	props: {
		value: {
			type: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Number],
			default: false,
		},
		items: {
			type: [Array],
			default: null,
		},
		name: {
			type: String,
			default: null,
		},
		config: {
			type: Object,
			default: () => ({}),
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value || []
			},
			set(val) {
				this.$emit("input", val)
			},
		},
	},
}
</script>
