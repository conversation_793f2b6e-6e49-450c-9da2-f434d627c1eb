<template>
	<v-chip-group v-model="localValue" multiple column v-on="$listeners">
		<v-chip v-for="(item, i) in items" :key="i" color="accent13" :value="item.value" filter>
			{{ item.text }}
		</v-chip>
	</v-chip-group>
</template>
<script>
export default {
	props: {
		value: {
			type: [<PERSON><PERSON><PERSON>, <PERSON><PERSON>an],
			default: false,
		},
		items: {
			type: [Array],
			default: null,
		},
		name: {
			type: String,
			default: "",
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value || []
			},
			set(value) {
				this.$emit("input", value)
			},
		},
		getItems() {
			return this.items.map(item => item.value)
		},
	},

	methods: {
		remove(item) {
			this.chips.splice(this.chips.indexOf(item), 1)
		},
	},
}
</script>

<style scoped>
/* stylelint-disable-next-line */
.chips .v-chip-group .v-slide-group__content {
	padding: 0 !important;
}
</style>
