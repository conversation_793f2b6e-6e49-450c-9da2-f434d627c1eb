<template>
	<div>
		<v-subheader>{{ name }}</v-subheader>
		<div class="d-flex px-3">
			<v-btn
				v-for="item in items"
				:key="item.value"
				height="24"
				width="24"
				fab
				elevation="2"
				:color="item.meta.hexCode"
				:input-value="localValue?.includes(item.value)"
				class="website-filter-color-btn"
				active-class="selected-color"
				@click="clickHandler(item.value)"
			>
				<v-icon size="20" color="white">
mdi-check
</v-icon>
			</v-btn>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		selectedColorValues: {
			type: Array,
			default: () => [],
		},
		items: {
			type: Array,
			default: () => [],
		},
		// colors: {
		// 	type: Object,
		// 	required: true,
		// },
		name: {
			type: String,
			required: true,
		},
		value: {
			type: Array,
			default: () => [],
		},
	},

	data() {
		return {}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit("input", val)
			},
		},
	},
	methods: {
		clickHandler(value) {
			let localValue = []
			if (Array.isArray(this.localValue)) {
				localValue = [...this.localValue]
			}
			if (localValue.includes(value)) {
				localValue.splice(this.localValue.indexOf(value), 1)
			} else {
				localValue.push(value)
			}
			this.localValue = localValue
		},
	},
}
</script>

<style lang="scss">
.v-btn.website-filter-color-btn {
	.v-icon {
		display: none;
	}
	&.selected-color {
		.v-icon {
			display: block !important;
		}
	}
}
</style>
