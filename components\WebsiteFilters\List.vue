<template>
	<div>
		<v-subheader>
			<b>{{ name }}</b>
			<small v-if="Array.isArray(localValue)" class="ms-2 text--secondary">({{ localValue.length }}
				Selected)</small><v-spacer />
			<v-btn :disabled="localValue === null || !localValue.length" class="px-0 me-n2" plain small @click="clear">
				Clear
			</v-btn>
		</v-subheader>
		<v-sheet max-height="250" class="overflow-auto">
			<v-list-item-group v-model="localValue" :multiple="multiple" @change="updateFilter">
				<template v-for="(item, i) in parsedItems">
					<v-divider v-if="!item" :key="`divider-${i}`" />
					<v-list-item v-else :key="`item-${i}`" class="small" dense :value="item.value"
						active-class="primary--text">
						<template #default="{ active }">
							<v-list-item-action class="me-4">
								<!-- <v-checkbox dense :input-value="active" color="primary"></v-checkbox> -->
								<component :is="type" dense :input-value="active" color="primary" />
							</v-list-item-action>
							<v-list-item-content>
								<v-list-item-title>
									<slot name="item-title" v-bind="{ item, config }">
										<span v-if="config.prefix">{{ config.prefix }}</span>
										{{ item.text }}
										<span v-if="config.suffix">{{ config.suffix }}</span>
									</slot>
								</v-list-item-title>
							</v-list-item-content>
							<v-list-item-action>
								<small v-if="item.meta.facet !== null">({{ item.meta.facet || 0 }})</small>
							</v-list-item-action>
						</template>
					</v-list-item>
				</template>
			</v-list-item-group>
		</v-sheet>
		<v-btn v-if="hasMore" plain small block @click="limited = !limited">
			<v-icon left small>
				{{ limited ? "mdi-plus" : "mdi-minus" }}
			</v-icon>
			<template v-if="limited">
				See More
			</template>
			<template v-else>
				See Less
			</template>
		</v-btn>
	</div>
</template>

<script>
import { VCheckbox, VRadio } from "vuetify/lib"
export default {
	components: {
		VCheckbox,
		VRadio,
	},
	props: {
		value: {
			type: [Array, Boolean, Number],
			default: false,
		},
		items: {
			type: [Array],
			default: null,
		},
		multiple: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
			required: true,
		},
		name: {
			type: String,
			required: true,
		},
		limit: {
			type: Number,
			default: 5,
		},
		config: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			limited: true,
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit("input", val)
			},
		},
		parsedItems() {
			// sort items higher to lower by item.meta.facet and if limited return only first 5 items
			return this.items
				.slice()
				.sort((a, b) => b.meta.facet - a.meta.facet)
				.slice(0, this.limited ? this.limit : this.items.length)
		},
		hasMore() {
			return this.items.length > this.limit
		},
	},
	methods: {
		clear() {
			this.localValue = this.multiple ? [] : null
			this.updateFilter()
		},
		updateFilter() {
			this.$emit("filter", this.localValue)
		},
	},
}
</script>
