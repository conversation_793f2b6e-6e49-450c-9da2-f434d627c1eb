<template>
	<div>
		<v-subheader>
			<b>{{ name }}</b>
			<!-- <small v-if="Array.isArray(localValue)" class="ms-2 text--secondary">({{ localValue.length }} Selected)</small> -->
			<v-spacer />
			<v-btn :disabled="value === null || !localValue.length" class="px-0 me-n2" plain small @click="clear">
Clear
</v-btn>
		</v-subheader>

		<div class="px-3">
			<v-range-slider
				v-model="localValue"
				step="10"
				:max="config.max"
				:min="config.min"
				hide-details
				class="align-center"
				@change="updateFilter"
			>
				<!-- <template #thumb-label="props">
								{{ props.value | money($store.state.currency, 0) }}
							</template> -->
				<!-- <template v-slot:prepend>
								<v-text-field
									:value="Range[0]"
									class="mt-0 pt-0"
									hide-details
									single-line
									type="number"
									style="width: 60px"
									@change="updateRange"
								></v-text-field>
							</template>
							<template v-slot:append>
								<v-text-field
									:value="Range[1]"
									class="mt-0 pt-0"
									hide-details
									single-line
									type="number"
									style="width: 60px"
									@change="updateRange"
								></v-text-field>
							</template> -->
			</v-range-slider>
			<div class="d-flex justify-space-between">
				<small>{{ localValue[0] | money($store.state.currency) }}</small>
				<small>{{ localValue[1] | money($store.state.currency) }}</small>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		value: {
			type: [String],
			default: null,
		},

		config: {
			type: Object,
			default: () => ({}),
		},
		name: {
			type: String,
			default: "",
		},
	},

	computed: {
		localValue: {
			get() {
				if (!this.value) {
					return [this.config.min, this.config.max]
				}

				return this.value?.split(":")
			},
			set(value) {
				if (
					value &&
					Array.isArray(value) &&
					(Number(value[0]) !== Number(this.config.min) || Number(value[1]) !== Number(this.config.max))
				) {
					// console.log(1);
					this.$emit("input", value.join(":"))
				} else {
					// console.log(2);
					this.$emit("input", null)
				}
			},
		},
	},
	methods: {
		clear() {
			this.localValue = null
			this.updateFilter()
		},
		updateFilter() {
			this.$emit("filter", this.localValue)
		},
	},
}
</script>
