<template>
	<v-card>
		<v-card-title>{{ $t("common.comments") }}</v-card-title>
		<v-card-text>
			<widget-comment v-for="comment in comments" :key="comment.id" :data="comment" />
			<v-alert type="info" text dense>
{{ $t("common.no-data") }}
</v-alert>
			<v-divider />
			<v-form ref="form" lazy-validation>
				<vc-textarea
					v-model="comment"
					:counter="500"
					:rules="[$rules.required($t('common.your-comment')), $rules.maxLength($t('common.your-comment'), 500)]"
					:label="$t('common.your-comment')"
					required
				/>
			</v-form>
		</v-card-text>
		<v-card-actions>
			<v-spacer />
			<v-btn color="primary" text @click="saveComment">
{{ $t("common.your-comment") }}
</v-btn>
		</v-card-actions>
	</v-card>
</template>

<script>
export default {
	props: {
		api: {
			type: String,
			required: true,
		},
		comments: {
			type: Array,
			required: true,
		},
	},
	data() {
		return {
			comment: null,
		}
	},
	methods: {
		saveComment() {
			if (!this.$refs.form.validate()) { return }
			this.$axios.post(this.api, { comment: this.comment }).then((response) => {
				this.$emit("comment-saved", response.data)
				this.$toast.success(this.$t("common.comment-saved"))
				this.comment = ""
			})
		},
	},
}
</script>
