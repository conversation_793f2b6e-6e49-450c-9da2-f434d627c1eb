<template>
	<v-card>
		<v-card-title> Most Trading Instruments </v-card-title>
		<div id="chart">
			<apexchart type="donut" width="380" :options="chartOptions" :series="series" />
		</div>
	</v-card>
</template>

<script>
// import apexchart from "vue-apexcharts";

export default {
	components: {
		apexchart: process.browser ? async () => await import("vue-apexcharts") : {},
	},

	data() {
		return {
			series: [44, 55, 41, 17, 15],
			chartOptions: {
				chart: {
					width: 380,
					type: "donut",
				},
				colors: ["#009688", "#607d8b", "#B71F23", "#00838f", "#ff9800"],
				plotOptions: {
					pie: {
						startAngle: -90,
						endAngle: 270,
					},
				},
				dataLabels: {
					enabled: true,
				},
				fill: {
					// type: "gradient",
				},
				legend: {
					formatter(val, opts) {
						return val + " - " + opts.w.globals.series[opts.seriesIndex]
					},
				},
				labels: ["XAUUSD", "EURUSD", "US30", "USDJPY", "GBPUSD"],
				title: {
					text: "",
				},
				responsive: [
					{
						breakpoint: 480,
						options: {
							chart: {
								width: 200,
							},
							legend: {
								position: "bottom",
							},
						},
					},
				],
			},
		}
	},
}
</script>
