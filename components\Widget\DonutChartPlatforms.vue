<template>
	<v-card>
		<v-card-title> Most Trading Platforms </v-card-title>
		<div id="chart">
			<apexchart type="donut" width="380" :options="chartOptions" :series="series" />
		</div>
	</v-card>
</template>

<script>
// import apexchart from "vue-apexcharts";

export default {
	components: {
		apexchart: process.browser ? async () => await import("vue-apexcharts") : {},
	},

	data() {
		return {
			series: [604, 808],
			chartOptions: {
				chart: {
					width: 380,
					type: "donut",
				},
				plotOptions: {
					pie: {
            startAngle: -90,
            endAngle: 90,
            offsetY: 10,
          },
				},
				dataLabels: {
					enabled: true,
				},
				fill: {
					type: "gradient",
				},
				legend: {
					formatter(val, opts) {
						return val + " - " + opts.w.globals.series[opts.seriesIndex]
					},
				},
				labels: ["MT4", "MT5"],
				title: {
					text: "",
				},
				responsive: [
					{
						breakpoint: 480,
						options: {
							chart: {
								width: 200,
							},
							legend: {
								position: "bottom",
							},
						},
					},
				],
			},
		}
	},
}
</script>
