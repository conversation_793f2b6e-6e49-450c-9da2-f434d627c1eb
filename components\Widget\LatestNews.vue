<template>
	<v-card class="fill-height">
		<v-card-title> {{ $t("dashboard.latest-news") }} </v-card-title>
		<div class="pa-4">
			<template v-for="(item, i) in news">
				<v-card :key="item.title" to="/ar" flat class="d-flex">
					<v-sheet width="150px">
<v-img width="100%" height="100%" :src="item.src" />
</v-sheet>
					<v-sheet>
						<v-card-title>
							<span class="text-body-1 font-weight-bold">{{ item.title }}</span>
						</v-card-title>

						<v-card-subtitle class="muted-1 text-body-2">
							<span>{{ item.body }}</span>
						</v-card-subtitle>
					</v-sheet>
				</v-card>
				<v-divider v-if="i < news.length - 1" :key="item.title + '2'" class="my-2" />
			</template>
		</div>
		<v-card-actions>
			<v-spacer />
			<v-btn color="primary" text>
{{ $t("common.read-more") }}
</v-btn>
		</v-card-actions>
	</v-card>
</template>

<script>
export default {
	data() {
		return {
			news: [
				{
					src: "",
					title: "",
					body: "",
				},
				{
					src: "",
					title: "",
					body: "",
				},
				{
					src: "",
					title: "",
					body: "",
				},
				{
					src: "",
					title: "",
					body: "",
				},

				{
					src: "",
					title: "",
					body: "",
				},
			],
		}
	},
}
</script>
