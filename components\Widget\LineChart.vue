<template>
	<div>
		<div class="d-md-flex align-center">
			<slot name="title">
<div class="title">
Instruments Prices
</div>
</slot>
			<v-spacer />
			<slot name="append-action" />
		</div>
		<client-only
			>
<VueApexCharts width="100%" height="350" type="candlestick" :options="options" :series="series" />
</client-only>
	</div>
</template>

<script>
// import VueApexCharts from "vue-apexcharts";
export default {
	components: {
		VueApexCharts: process.browser ? async () => await import("vue-apexcharts") : {},
	},
	props: {
		series: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			currentInterval: "1M",
		}
	},
	computed: {
		options() {
			const yaxis = []
			return {
				markers: {
					colors: ["#F44336", "#E91E63", "#9C27B0"],
				},
				// grid: {
				// 	row: {
				// 		colors: ["#F44336", "#E91E63", "#9C27B0"],
				// 	},
				// 	column: {
				// 		colors: ["#F44336", "#E91E63", "#9C27B0"],
				// 	},
				// },

				chart: {
					toolbar: {
						show: false,
					},
				},

				title: {
					align: "left",
				},
				xaxis: {
					type: "datetime",
				},
				yaxis: {
					tooltip: {
						enabled: true,
					},
					min(min) {
						return min
					},
					max(max) {
						return max
					},
					decimalsInFloat: 4,
					tickAmount: 8,
				},
				annotations: {
					yaxis,
				},
				stroke: {
					width: 3,
					colors: ["#a41a1f", "#E91E63", "#9C27B0"],
					show: true,
					curve: "smooth",
					lineCap: "round",
				},
			}
		},
	},
}
</script>
