<template>
	<v-navigation-drawer v-model="drawerModel" width="350" app hide-overlay temporary :left="isRTL" :right="!isRTL">
		<template #prepend>
			<v-subheader class="text-h5 d-flex align-center">
				<v-icon left>
					mdi-heart
				</v-icon><span> {{ $t("common.my-wishlist") }} </span><v-spacer />
				<v-btn icon @click="close">
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</v-subheader>
		</template>
		<v-lazy>
			<v-list>
				<v-list-item v-for="(item, itemIndex) in wishlistItems" :key="`item-${itemIndex}`" link :to="localePath({
					name: 'product',
					params: {
						productSlug: item?.slug,
					},
				})
					">
					<thumbnail :width="80" :aspect-ratio="4 / 3"
						:src="item?.media?.gallery[0]?.src || item?.media?.cover[0]?.src || '/images/product-placeholder.webp'" />
					<v-list-item-content class="pl-2">
						<v-list-item-title>{{ item?.name }}</v-list-item-title>
						<v-list-item-subtitle class="d-flex">
							SKU #{{ item?.SKU || item?.variance?.SKU }}
							<v-spacer />
							{{ item?.stock?.price | money }}
						</v-list-item-subtitle>
					</v-list-item-content>

					<v-list-item-action>
						<v-btn icon @click.prevent="remove(item)">
							<v-icon>mdi-delete</v-icon>
						</v-btn>
					</v-list-item-action>
				</v-list-item>
			</v-list>
		</v-lazy>
		<no-wishlist-items v-if="!wishlistItems?.length" @close="close" />
	</v-navigation-drawer>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex"
import NoWishlistItems from "./NoWishlistItems.vue"
export default {
	components: { NoWishlistItems },
	data() {
		return {
			...mapState,
			drawerModel: false,
		}
	},
	computed: {
		...mapGetters({ wishlistItems: "wishlist/wishlistItems" }),
	},

	mounted() {
		this.$nuxt.$on("wishlist.show", this.open)
		this.$nuxt.$on("wishlist.hide", this.close)
		this.$nuxt.$on("wishlist.add", this.add)
		this.$nuxt.$on("wishlist.remove", this.remove)
		this.$nuxt.$on("wishlist.update", this.update)
	},
	methods: {
		...mapActions({ add: "wishlist/add", remove: "wishlist/remove", get: "wishlist/get" }),
		open() {
			this.drawerModel = true
		},
		close() {
			this.drawerModel = false
		},
	},
}
</script>
