<template>
	<div>
		<component :is="attribute.insertType"

    v-for="attribute in attributes"
    				:key="attribute.attributeId"
										v-model="localValue"
										:label="attribute.name"

    :items="localItems" />
	</div>
</template>

<script>
export default {
	props: {
		value: {
			type: [String, Number],
			default: null,
		},
		attributes: {
			type: [Array],
			default: null,
		},
		label: {
			type: [String, Number],
			default: null,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				return this.$emit("input", value)
			},
		},
	},

	created() {},
	methods: {},
}
</script>

<style lang="scss" scoped>
</style>
