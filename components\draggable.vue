<template>
	<vue-draggable
		v-bind="$attrs"
		v-model="localValue"
		:no-transition-on-drag="true"
		:force-fallback="true"
		:handle="handle"
		v-on="$listeners"
	>
		<transition-group name="list">
			<slot />
		</transition-group>
	</vue-draggable>
</template>

<script>
export default {
	name: "DraggableComponent",
	components: {
		[process.client ? "vue-draggable" : undefined]: process.client ? () => import("vuedraggable") : undefined,
	},
	props: {
		value: {
			type: Array,
			default: () => [],
		},
		handle: {
			type: String,
			default: ".handle",
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", value)
			},
		},
	},
}
</script>
<style>
.list-move {
	transition: transform 0.5s;
}

.list-leave-active {
	position: absolute;
}

.list-item {
	transition: all 1s;
	display: inline-block;
	margin-right: 10px;
}

.list-enter, .list-leave-to
  /* .list-complete-leave-active below version 2.1.8 */ {
	opacity: 0;
	transform: translateY(30px);
}

.sortable-ghost {
	border: 1px dashed #893477;
	background-color: #89347717;
}

.drag-preview {
	position: absolute;
	transform: translateX(-50%) translateY(-50%) rotate(7deg) translateY(55%);
	background-color: red;
}
</style>
