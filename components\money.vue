<template>
	<v-tooltip :right="!isRTL" :left="isRTL" open-delay="300">
		<template #activator="{ on }">
			<span class="fixed-numeric curser-help" v-on="on"> {{ localValue | money(currency) }} </span>
		</template>

		<!-- <ul class="my-2">
			<li v-if="currency !== 'EUR'" class="fixed-numeric">{{ EUR | money("EUR") }}</li>
			<li v-if="currency !== 'JPY'" class="fixed-numeric">{{ JPY | money("JPY") }}</li>
			<li v-if="currency !== 'JOD'" class="fixed-numeric">{{ JOD | money("JOD") }}</li>
			<li v-if="currency !== 'USD'" class="fixed-numeric">{{ toUSD | money("USD") }}</li>
		</ul> -->
		<v-chip label color="info" class="d-block text-caption mb-2">
			<v-icon left small>
mdi-information-outline
</v-icon> These values are approximate
</v-chip
		>
	</v-tooltip>
</template>

<script>
export default {
	props: {
		currency: {
			type: String,
			default: "USD",
		},
		value: {
			type: [String, Number],
			default: 0,
		},
	},
	computed: {
		localValue() {
			return this.$slots?.default?.[0]?.text || Number(this.value)
		},
		toUSD() {
			switch (this.currency) {
				case "USD":
					return 1
				case "EUR":
					return 1
				case "JPY":
					return 0.0073
				default:
					return 0
			}
		},
		USD() {
			return this.localValue * this.toUSD
		},
		EUR() {
			return this.localValue * this.toUSD * 1
		},
		JPY() {
			return (this.localValue * this.toUSD) / 0.0073
		},
		JOD() {
			return this.localValue * this.toUSD * 0.7
		},
	},
}
</script>
