export const orderStatus = [
	// below is just an example
	{
		key: "common.cancelled",
		value: -1,
		selectable: false,
		color: "grey lighten-2",
	},
	{
		key: "common.pending",
		value: 0,
		color: "warning",
	},
	{
		key: "common.delivered",
		value: 1,
		color: "success",
	},
]
export const translationTypesEnum = [
	{
		text: "common.text",
		value: "text",
	},
	{
		text: "common.html",
		value: "html",
	},
	{
		text: "common.countable",
		value: "countable",
	},
]
export const currenciesTypesEnum = [
	{
		key: "common.JOD",
		value: "JOD",
	},
	{
		key: "common.USD",
		value: "USD",
	},
	{
		key: "common.SAR",
		value: "SAR",
	},
]
export const filterTypes = [
	{
		key: "common.price",
		value: "price",
	},
	{
		key: "common.brand",
		value: "brand",
	},
	{
		key: "common.attribute",
		value: "attribute",
	},
	{
		key: "common.category",
		value: "category",
	},
]
export const componentTypes = [
	// {
	// 	key: "common.radio",
	// 	value: "radio",
	// },

	{
		key: "common.checkbox",
		value: "checkbox",
	},
	// {
	// 	key: "common.select",
	// 	value: "select",
	// },
	{
		key: "common.chips",
		value: "chips",
	},
	// {
	// 	key: "common.brand",
	// 	value: "brand",
	// },
	{
		key: "common.range",
		value: "range",
	},
	{
		key: "common.color",
		value: "color",
	},
	// {
	// 	key: "common.text",
	// 	value: "text",
	// },
	// {
	// 	key: "common.number",
	// 	value: "number",
	// },
]

export const symbolsTypesEnum = [
	{
		key: "common.JOD",
		value: "JOD",
	},
	{
		key: "common.$",
		value: "$",
	},
	{
		key: "common.SR",
		value: "SR",
	},
]

export const statusContactUs = [
	{
		key: "common.pending",
		value: "pending",
		color: "warning",
	},
	{
		key: "common.completed",
		value: "completed",
		color: "success",
	},
]

export const statusPaymentMethod = [
	{
		key: "common.active",
		value: "active",
		color: "success",
	},
	{
		key: "common.inactive",
		value: "inactive",
		color: "warning",
	},
]

export const isPublished = [
	{
		key: "Published",
		value: true,
		color: "success",
	},

	{
		key: "Not Published",
		value: false,
		color: "warning",
	},
]

export const genders = [
	{
		key: "common.male",
		value: "male",
	},
	{
		key: "common.female",
		value: "female",
	},
]

export const statusUser = [
	{
		key: "common.active",
		value: "active",
		color: "success",
	},
	{
		key: "common.suspended",
		value: "suspended",
		color: "warning",
	},
]

export const statusRating = [
	{
		key: "Published",
		value: "Published",
		color: "success",
	},

	{
		key: "UnPublished",
		value: "UnPublished",
		color: "warning",
	},
]

export const statusRequestsChangeName = [
	{
		key: "common.active",
		value: "Active",
		color: "success",
	},
	{
		key: "common.pending",
		value: "Pending",
		color: "warning",
	},
]

export const productsTypes = [
	{
		key: "common.simple",
		value: "simple",
	},
	{
		key: "common.alternative",
		value: "alternative",
	},
	{
		key: "common.bundle",
		value: "bundle",
	},
]

export const problemTypes = [
	{
		key: "common.problem-with-an-order",
		value: "Problem with an order",
	},
	{
		key: "common.password-email-or-login",
		value: "Password, email, or Login",
	},
	{
		key: "common.payment-issues",
		value: "Payment issues",
	},
	{
		key: "common.product-or-website-information",
		value: "Product/website information",
	},

	{
		key: "common.problems",
		value: "Other problems",
	},
]

export const attributesType = [
	{
		key: "common.text-field",
		value: "text",
	},
	// {
	// 	key: "common.select",
	// 	value: "select",
	// },
	{
		key: "common.number-field",
		value: "number",
	},
	// {
	// 	key: "common.radio",
	// 	value: "radio",
	// },
	{
		key: "common.checkbox",
		value: "checkbox",
	},
	// {
	// 	key: "common.chips",
	// 	value: "chips",
	// },
	// {
	// 	key: "common.brand",
	// 	value: "brand",
	// },
	// {
	// 	key: "common.range",
	// 	value: "range",
	// },
	{
		key: "common.color",
		value: "color",
	},
	{
		key: "common.textarea",
		value: "textarea",
	},
]

export const countableEnum = [
	{
		text: "common.none",
		value: "0",
	},
	{
		text: "common.singular",
		value: "1",
	},
	{
		text: "common.pair",
		value: "2",
	},
	{
		text: "common.multiple_of",
		value: "3",
	},
]

export const orderStatuses = [
	{
		text: "Received",
		value: "received",
		color: "blue",
	},
	{
		text: "Processing",
		value: "processing",
		color: "blue",
	},
	{
		text: "Editing",
		value: "editing",
		color: "warning",
	},
	{
		text: "Completed",
		value: "completed",
		color: "success",
	},
	{
		text: "Draft",
		value: "draft",
		color: "grey",
	},
	{
		text: "Canceled",
		value: "canceled",
		color: "error",
	},
	{
		text: "failed",
		value: "failed",
		color: "error",
	},

]

export const paymentStatus = [
	{
		text: "Piad",
		value: "paid",
		color: "success",
	},
	{
		text: "unPaid",
		value: "unPaid",
		color: "error",
	},
	{
		text: "partialPaid",
		value: "partialPaid",
		color: "warning",
	},
]

export const invoiceStatus = [
	{
		text: "Active",
		value: "active",
		color: "success",
	},
	{
		text: "Superseded",
		value: "superseded",
		color: "error",
	},
	{
		text: "Canceled",
		value: "canceled",
		color: "warning",
	},
]
