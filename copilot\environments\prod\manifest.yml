# The manifest for the "prod" environment.
# Read the full specification for the "Environment" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/environment/

# Your environment name will be used in naming your resources like VPC, cluster, etc.
name: prod
type: Environment

# Import your own VPC and subnets or configure how they should be created.
network:
  vpc:
    id: vpc-0ce54da2932cc0a0c # Your VPC ID
    subnets:
      public:
        - id: subnet-03572e8fa7d1e8647 # copilot-v2-prod-pub0
        - id: subnet-05c54706dc3fa54cf # copilot-v2-prod-pub1
      private:
        - id: subnet-060dd3bb1ec187363 # copilot-v2-prod-priv0
        - id: subnet-0596eb12436cec3e5 # copilot-v2-prod-priv1

# Configure the load balancers in your environment, once created.
# http:
#   public:
#   private:

# Configure observability for your environment resources.
observability:
  container_insights: false
