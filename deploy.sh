#!/bin/bash
newDir="building-dir"



if [ -f .env ]
then
  export $( grep -vE "^(#.*|\s*)$" .env )
fi



app=$NUXT_ENV_PROJECT_NAME

# Rename the build directory
sed -i "s/buildDir: \".nuxt\"/buildDir: \"$newDir\"/" nuxt.config.js

#\cp app/views/$app.error.html app/views/error.html


echo "Building $app for production"
npm run build


## get status ##
status=$?

# Revert the rename
git checkout nuxt.config.js


if [ $status -eq 0 ]
then
echo "The build was successful, Mounting new build dir... "
# Replace the existing directory with the new build
rm -rf .nuxt && mv $newDir .nuxt
echo "Reloading PM2..."
pm2 reload $app
else
echo "The build has failed"
fi