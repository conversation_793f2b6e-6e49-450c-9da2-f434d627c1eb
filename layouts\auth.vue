<template>
	<client>
		<base-view content-class="d-flex align-center justify-center py-md-5">
			<!-- <video preload loop play autoplay class="fixed top left right fill-width" src="/videos/login-bg.mp4"></video> -->
			<!-- <div class="fixed top left right fill-width"><v-img src="/images/login-bg.avif"></v-img></div> -->
			<!-- <v-sheet color="background" min-height="100vh" width="100vw" class="d-flex align-center justify-center"> -->
			<base-auth-view>
				<Nuxt />
			</base-auth-view>
			<!-- </v-sheet> -->
		</base-view>
	</client>
</template>

<script>
export default {
	head() {
		return {
			htmlAttrs: {
				lang: this.$i18n.locale,
			},
		}
	},
}
</script>
<style lang="scss">
@import "~/assets/mixins";

.login-card {

	// @include responsive("width", 400px, 400px, 100%);
	//background-color: rgba(255, 255, 255, 0.7) !important;
	//backdrop-filter: blur(25px);
	>* {
		width: 100%;
	}
}
</style>
