<template>
	<client-only>
		<base-view>
			<div v-if="isAllowedToProceed">
				<v-navigation-drawer v-model="drawer" :expand-on-hover="storage.miniDrawer"
					:mini-variant="storage.miniDrawer" clipped :fixed="isDesktop" app :right="isRTL" :permanent="isDesktop"
					width="300">
					<template #prepend>
						<div v-if="isMobile" class="d-flex align-center justify-center mt-4">
							<logo :to="{ name: 'dashboard' }" />
						</div>
						<v-text-field ref="searchInput" v-model="searchMenu" autofocus filled dense hide-details solo
							clearable flat solo-inverted :placeholder="$t('menu.use-ctrl-s-to-search')"
							prepend-inner-icon="mdi-magnify" style="max-width: 300px" class="mx-2 mt-5 flex-grow-0"
							@keydown.ctrl.c="searchMenu = null" @keydown.down="selectNextItem" @keydown.up="selectPreviousItem"
							@keydown.enter="goToSelectedItem" @keydown.esc="searchMenu = null" @input="selectFirstItem" />
					</template>
					<div class="d-flex flex-column fill-height">
						<v-list v-model="selectedItem" nav dense class="align-self-start fill-width">
							<v-subheader v-if="searchMenu">
								{{ $t("menu.search-result") }}
							</v-subheader>
							<div v-if="!filteredItem.length" type="grey" text icon="mdi-magnify-remove-outline"
								class="ma-2 text-caption muted-2">
								<v-icon small>
									mdi-magnify-remove-outline
								</v-icon>
								<span>{{ $t("menu.there-is-no-data-matching") }} "{{ searchMenu }}"</span>
							</div>
							<template v-for="(item, i) in filteredItem">
								<v-subheader v-if="item.type === 'subheader'" :key="item.title">
									{{ item.title }}
								</v-subheader>

								<v-list-group v-else-if="item.children" :key="'group' + item.title" :prepend-icon="item.action"
									no-action>
									<template #activator>
										<v-list-item-action>
											<v-icon>{{ item.icon }}</v-icon>
										</v-list-item-action>
										<v-list-item-content>
											<v-list-item-title v-text="item.title" />
											<v-list-item-subtitle v-text="item.subheader" />
										</v-list-item-content>
									</template>

									<v-list-item v-for="child in item.children" :key="child.title" dense :to="child.to">
										<v-list-item-content>
											<v-list-item-title v-text="child.title" />
										</v-list-item-content>
									</v-list-item>
								</v-list-group>
								<v-list-item v-else :key="i" :to="item.to" router :exact="i === 0" color="primary" exact-path
									:input-value="i === selectedItem" :class="{ 'colored-bg': i === selectedItem }">
									<v-list-item-action>
										<v-icon>{{ item.icon }}</v-icon>
									</v-list-item-action>
									<v-list-item-content>
										<v-list-item-title v-html="searchMenu ? item.highlightedTitle : item.title" />
										<v-list-item-subtitle v-if="item.subheader" v-text="item.subheader" />
									</v-list-item-content>
								</v-list-item>
							</template>
						</v-list>

						<v-spacer />
					</div>
				</v-navigation-drawer>
				<v-app-bar ref="header" v-mutate="onMutate" :clipped-left="!isRTL" :clipped-right="isRTL" fixed app
					elevate-on-scroll>
					<template v-if="!isMobile">
						<v-app-bar-nav-icon @click.stop="drawerHandler">
							<v-scale-transition leave-absolute>
								<v-icon :key="'miniDrawer' + storage.miniDrawer">
									<template v-if="storage.miniDrawer">
										mdi-menu
									</template>
									<template v-else>
										mdi-menu-open
									</template>
								</v-icon>
							</v-scale-transition>
						</v-app-bar-nav-icon>
						<div class="d-flex align-center" :style="{ minWidth: isDesktop ? 'calc(300px - 54px)' : 0 }">
							<logo :to="{ name: 'dashboard' }" />
						</div>

						<!-- <lazy-client-header /> -->

						<v-spacer />
						<!-- <lazy-translations-add v-if="$config.isDev" /> -->

						<!-- <v-btn icon @click="switchTheme"><v-icon>mdi-theme-light-dark</v-icon> </v-btn> -->
						<v-badge content="2" color="error pulse" :value="false" overlap bordered offset-x="24px"
							offset-y="24px">
							<v-menu offset-y>
								<template #activator="{ on }">
									<v-btn v-tooltip="'Soon'" icon disabled v-on="on">
										<v-icon>mdi-bell</v-icon>
									</v-btn>
								</template>
								<v-card>
									<v-list dense two-line>
										<div class="d-flex align-center justify-space-between px-2">
											<v-btn text small color="primary">
												{{ $t("header.mark-all-read") }}
											</v-btn>
											<v-chip small color="primary" dark>
												2 {{ $t("header.unread") }}
											</v-chip>
										</div>
										<v-divider class="my-2" />
										<v-list-item exact :input-value="true" color="primary" class="colored-bg clickable"
											@click="$router.push(localePath('/'))">
											<v-list-item-avatar>
												<v-badge dot color="primary">
													<v-icon>mdi-account</v-icon>
												</v-badge>
											</v-list-item-avatar>
											<v-list-item-content>
												<v-list-item-title>Account Opened</v-list-item-title>
												<v-list-item-subtitle>Your real account has been opened</v-list-item-subtitle>
											</v-list-item-content>
											<v-list-item-action>
												<v-list-item-action-text>15 Min Ago</v-list-item-action-text>
											</v-list-item-action>
										</v-list-item>

										<v-list-item color="primary" class="colored-bg clickable" :input-value="true"
											@click="$router.push(localePath('/'))">
											<v-list-item-avatar>
												<v-badge dot color="primary">
													<v-icon>mdi-account</v-icon>
												</v-badge>
											</v-list-item-avatar>
											<v-list-item-content>
												<v-list-item-title>Account Activated</v-list-item-title>
												<v-list-item-subtitle>Your account has been approved</v-list-item-subtitle>
											</v-list-item-content>
											<v-list-item-action>
												<v-list-item-action-text>15 Min Ago</v-list-item-action-text>
											</v-list-item-action>
										</v-list-item>
										<v-list-item exact @click="$router.push(localePath('/'))">
											<v-list-item-avatar>
												<v-badge dot color="primary" :value="false">
													<v-icon>mdi-account</v-icon>
												</v-badge>
											</v-list-item-avatar>
											<v-list-item-content>
												<v-list-item-title>Account Created</v-list-item-title>
												<v-list-item-subtitle>Your account has been created</v-list-item-subtitle>
											</v-list-item-content>
											<v-list-item-action>
												<v-list-item-action-text>35 Min Ago</v-list-item-action-text>
											</v-list-item-action>
										</v-list-item>
										<v-divider class="my-2" />
										<v-btn small text block>
											{{ $t("header.see all notifications") }}
										</v-btn>
									</v-list>
								</v-card>
							</v-menu>
						</v-badge>

						<v-btn icon class="text-uppercase font-weight-bold me-3" :href="switchLocalePath(otherLocale.code)">
							{{ otherLocale.code }}
						</v-btn>

						<v-menu open-on-hover offset-y>
							<template #activator="{ on }">
								<v-chip v-if="isDesktop" color="transparent" pill v-on="on">
									<template v-if="$auth.user">
										<avatar v width="200" height="200" :src="$auth.user.media?.avatar?.preview" />
										<div v-if="isDesktop">
											<div>{{ $t("title.welcome") }} {{ $auth.user.firstName }},</div>
											<div class="text-caption">
												Administrator
											</div>
										</div>
									</template>
								</v-chip>
								<v-btn v-else icon v-on="on">
									<v-avatar size="40">
										<v-img src="/images/user2.jpeg" />
									</v-avatar>
								</v-btn>
							</template>
							<v-list dense>
								<v-list-item :to="localePath({ name: 'admin-profile' })">
									<v-list-item-icon>
										<v-icon>mdi-account</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>
											<span>{{ $t("header.profile") }}</span>
										</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-list-item :to="localePath({ name: 'admin-change-password' })">
									<v-list-item-icon>
										<v-icon>mdi-key</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>
											<span>{{ $t("header.change-password") }}</span>
										</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-list-item exact :to="'/' + $i18n.locale">
									<v-list-item-icon>
										<v-icon>mdi-monitor-screenshot</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>
											<span>{{ $t("header.back-to-website") }}</span>
										</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider />
								<v-list-item @click="logout">
									<v-list-item-icon>
										<v-icon>mdi-logout</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>
											<span>{{ $t("header.logout") }}</span>
										</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
							</v-list>
						</v-menu>
					</template>
					<template v-else>
						<v-app-bar-nav-icon @click.stop="drawerHandler" />
						<v-spacer />
						<v-toolbar-title id="page-title" />
						<v-spacer />
					</template>

					<template v-if="!isDesktop">
						<v-menu>
							<template #activator="{ on }">
								<v-btn icon v-on="on">
									<v-icon>mdi-dots-vertical</v-icon>
								</v-btn>
							</template>
							<v-list dense>
								<v-list-item to="/">
									<v-list-item-icon>
										<v-icon>mdi-earth</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>
											<span>{{ $t("header.language") }}</span>
										</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-list-item to="/">
									<v-list-item-icon>
										<v-icon>mdi-calendar</v-icon>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>
											<span>{{ $t("header.calendar") }}</span>
										</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
							</v-list>
						</v-menu>
					</template>
				</v-app-bar>
				<v-main>
					<v-divider />
					<v-container fluid class="fill-height align-start">
						<v-row class="fill-height">
							<v-col class="fill-height">
								<Nuxt :nuxt-child-key="$route.name" />
							</v-col>
						</v-row>
					</v-container>
				</v-main>

				<!-- <Toasts /> -->
				<!-- <v-footer :absolute="!fixed" app>

<span>&copy; {{ new Date().getFullYear() }}</span>

</v-footer> -->

				<v-bottom-navigation v-if="isMobile" hide-on-scroll grow fixed color="primary" shift>
					<v-btn>
						<span>{{ $t("header.home") }}</span>

						<v-icon>mdi-home</v-icon>
					</v-btn>

					<!-- <v-btn>
                    <span>{{ $t("header.notifications") }}</span>

                    <v-icon>mdi-bell</v-icon>
                </v-btn> -->

					<v-btn>
						<span>{{ $t("header.my-account") }}</span>

						<v-icon>mdi-account</v-icon>
					</v-btn>
					<v-btn>
						<span>{{ $t("header.settings") }}</span>

						<v-icon>mdi-cog</v-icon>
					</v-btn>
				</v-bottom-navigation>

				<!-- <fcm /> -->
				<!-- <lazy-boarding v-if="!storage.bordered" /> -->
			</div>
			<div v-else key="not-allowed" class="fill-height fill-width d-flex align-center justify-center py-md-5">
				<base-auth-view>
					<logging-in />
				</base-auth-view>
			</div>

			<!-- </v-fade-transition> -->
		</base-view>
	</client-only>
</template>

<script>
import darkColors from "~/assets/dark.scss"
import responsive from "~/mixins/responsive"
import authMixins from "~/mixins/auth"
export default {
	name: "DefaultLayout",
	mixins: [responsive, authMixins],
	middleware: ["auth", "admin"],
	data() {
		return {
			drawer: true,
			fixed: false,
			searchMenu: null,
			bottomNavigationModel: 0,
			darkColors,
			right: true,
			rightDrawer: false,
			selectedItem: 1,
			isPermissionsLoaded: true,
			isPreferencesLoaded: true,
			setVisitorId: false,
			isInitiating: false,
		}
	},
	computed: {
		isAllowedToProceed() {
			return this.$store.getters.isInitialDataLoaded && this.isPermissionsLoaded && this.isPreferencesLoaded
		},
		filteredItem() {
			let items = []
			if (this.searchMenu) {
				this.items.forEach((item) => {
					if (item.children) {
						const children = item.children.filter((child) => {
							return child.title.toLowerCase().includes(this.searchMenu.toLowerCase())
						})
						if (children.length > 0) {
							children.forEach((child) => {
								child.subheader = item.title
								child.icon = item.icon
								child.highlightedTitle = this.highlightTitle(child.title)
								items.push(child)
							})
						}
					} else if (!item.children && item.title.toLowerCase().includes(this.searchMenu.toLowerCase())) {
						item.highlightedTitle = this.highlightTitle(item.title)
						items.push(item)
					}
				})

				// return this.items
				// 	.filter((item) => {
				// 		return (
				// 		item.title.toLowerCase().includes(this.searchMenu.toLowerCase()) ||
				// 		item.children?.filter((child) => {
				// 		return child.title.toLowerCase().includes(this.searchMenu.toLowerCase());
				// 		}).length > 0
				// 		);
				// 	})
				// 	.map((parent) => {
				// 		parent.active = true;
				// 		return parent;
				// 	});
			} else {
				items = this.items
			}
			// check permissions for items and its children
			return items
				.map((item) => {
					if (item.children) {
						item.children = item.children.filter((child) => {
							if (!child.permission) { return true }
							return this.$gates.hasPermission(child.permission)
						})
					}
					return item
				})
				.filter((item) => {
					if (item.children) {
						return item.children.length > 0
					} else if (item.permission) {
						return this.$gates.hasPermission(item.permission)
					} else {
						return true
					}
				})
		},
		items() {
			return [
				{
					icon: "mdi-view-dashboard",
					title: this.$t("menu.dashboard"),
					to: this.localePath({
						name: "dashboard",
					}),
				},
				{
					icon: "mdi-account-circle",
					title: this.$t("menu.orders"),
					children: [
						{
							title: this.$t("menu.orders"),
							to: this.localePath({
								name: "orders",
								params: {
									status: "received",
								},
							}),
						},
					],
				},
				// { title: "My Account", type: "subheader" },
				{
					icon: "mdi-card-account-details-star",
					title: this.$t("menu.inventory"),
					children: [
						{
							title: this.$t("menu.products"),
							to: this.localePath({
								name: "products",
							}),
						},
						{
							title: this.$t("menu.categories-and-filters"),
							to: this.localePath({
								name: "categories",
							}),
						},
						{
							title: this.$t("menu.product-group"),
							to: this.localePath({
								name: "groups",
							}),
						},
						{
							title: this.$t("menu.brands"),
							to: this.localePath({
								name: "admin-brands",
							}),
						},
						{
							title: this.$t("menu.attributes"),
							to: this.localePath({
								name: "attributes",
							}),
						},
						{
							title: this.$t("menu.product-labels"),
							to: this.localePath({
								name: "labels",
							}),
						},
					],
				},
				{
					icon: "mdi-account-circle",
					title: this.$t("menu.suppliers"),
					children: [
						{
							title: this.$t("menu.suppliers"),
							to: this.localePath({
								name: "suppliers",
							}),
						},
					],
				},

				{
					icon: "mdi-star",
					title: this.$t("menu.ratings"),
					children: [
						{
							title: this.$t("menu.ratings"),
							to: this.localePath({
								name: "ratings",
							}),
						},
					],
				},
				{
					icon: "mdi-wrench",
					title: this.$t("menu.auctions"),
					children: [
						{
							title: this.$t("menu.auctions"),
							to: this.localePath({
								name: "auctions",
							}),
						},
					],
				},
				// {
				// 	icon: "mdi-message-text",
				// 	title: this.$t("menu.action-used-souq"),
				// 	children: [
				// 		{
				// 			title: this.$t("menu.used-categories"),
				// 			to: this.localePath({
				// 				name: "used-categories",
				// 			}),
				// 		},

				// 		{
				// 			title: this.$t("menu.used-products"),
				// 			to: this.localePath({
				// 				name: "used-products",
				// 			}),
				// 		},
				// 	],
				// },
				{
					icon: "mdi-message-text",
					title: this.$t("menu.shippings"),
					to: this.localePath({
						name: "shippings-carriers",
					}),
				},

				{
					icon: "mdi-message-text",
					title: this.$t("menu.system-configurations"),
					children: [
						{
							title: this.$t("menu.currencies"),
							to: this.localePath({
								name: "currencies",
							}),
						},

						{
							title: this.$t("menu.payments-methods"),
							to: this.localePath({
								name: "paymentsMethods",
							}),
						},
						{
							title: this.$t("menu.cities"),
							to: this.localePath({
								name: "cities",
							}),
						},
						{
							title: this.$t("menu.countries"),
							to: this.localePath({
								name: "countries",
							}),
						},
						{
							title: this.$t("menu.banners"),
							to: this.localePath({
								name: "banners",
							}),
						},
						{
							title: this.$t("menu.mailsearch"),
							to: this.localePath({
								name: "mailsearch",
							}),
						},
					],
				},
				{
					icon: "mdi-pencil",
					title: this.$t("menu.ai"),
					children: [
						{
							title: this.$t("menu.ai"),
							to: this.localePath({
								name: "ai",
							}),
						},
					],
				},

				{
					icon: "mdi-account-circle",
					title: this.$t("menu.users"),
					children: [
						{
							title: this.$t("menu.customers"),
							to: this.localePath({
								name: "customers",
							}),
						},
						{
							title: this.$t("menu.request-change-name"),
							to: this.localePath({
								name: "requestChangeName",
							}),
						},
						{
							title: this.$t("menu.deleted-users"),
							to: this.localePath({
								name: "deletedUsers",
							}),
						},
					],
				},

				{
					icon: "mdi-message-text",
					title: this.$t("menu.pages"),
					children: [
						{
							title: this.$t("menu.pages"),
							to: this.localePath({
								name: "pages",
							}),
						},
					],
				},
				{
					icon: "mdi-message-text",
					title: this.$t("menu.contact-us"),
					children: [
						{
							title: this.$t("menu.contact-us"),
							to: this.localePath({
								name: "contact-us",
							}),
						},
					],
				},
				{
					icon: "mdi-message-text",
					title: this.$t("menu.translations"),
					children: [
						{
							title: this.$t("menu.translations"),
							to: this.localePath({
								name: "translations",
							}),
						},

						{
							title: this.$t("menu.translation-tags"),
							to: this.localePath({
								name: "translationTags",
							}),
						},
					],
				},
			]
		},
		otherLocale() {
			return this.$i18n.locales.find(locale => locale.code !== this.$i18n.locale)
		},
		bottomItems() {
			return [
				{
					icon: "mdi-headset",
					title: this.$t("menu.support"),
					to: this.localePath({
						name: "support-tickets-list",
					}),
				},
			]
		},
	},

	beforeCreate() {
		//! TODO
		// this.$store.dispatch("authExtend/loadPermissions").then(() => {
		// 	this.isPermissionsLoaded = true;
		// });
		// this.$store.dispatch("authExtend/loadPreferences").then(() => {
		// 	this.isPreferencesLoaded = true;
		// });
	},
	created() {
		this.$vuetify.rtl = this.$i18n.localeProperties.dir === "rtl"
	},
	mounted() {
		window.addEventListener("keydown", (e) => {
			if (e.key === "s" && e.ctrlKey) {
				e.preventDefault()
				this.$refs.searchInput.focus()
			}

			if (e.key === "F8") {
				e.preventDefault()
				/* eslint-disable-next-line */
				debugger;
			}
		})
	},
	methods: {
		increaseLimit() {
			if (this.limit < this.countries.length) {
				this.limit += 12
			}
		},
		onMutate() {
			let height = 0
			const header = this.$refs.header
			if (header) {
				height = `${header.$el.offsetHeight}px`
			}
			document.querySelector(":root").style.setProperty("--headerHeight", height)
		},
		drawerHandler() {
			if (this.isDesktop) {
				this.storage.miniDrawer = !this.storage.miniDrawer
			} else {
				this.storage.miniDrawer = false
				this.drawer = !this.drawer
			}
		},
		switchTheme() {
			this.$vuetify.theme.dark = !this.$vuetify.theme.dark

			// this.$cookies.set("theme", this.$vuetify.theme.dark ? "dark" : "light", { path: "/", maxAge: 60 * 60 * 24 * 365 });+
			this.storage.theme = this.$vuetify.theme.dark ? "dark" : "light"
		},
		highlightTitle(item) {
			return item.replace(new RegExp(this.searchMenu, "gi"), match => `<b class="primary--text yellow">${match}</b>`)
		},
		goToSelectedItem() {
			if (this.selectedItem > -1) {
				this.$router.push(this.filteredItem[this.selectedItem].to)
			}
		},
		selectNextItem() {
			if (this.selectedItem < this.filteredItem.length - 1) {
				this.selectedItem++
			}
		},
		selectPreviousItem() {
			if (this.selectedItem > 0) {
				this.selectedItem--
			}
		},
		selectFirstItem(input) {
			if (this.filteredItem.length > 0 && input) {
				this.selectedItem = 0
			} else {
				this.selectedItem = -1
			}
		},

		logout() {
			this.$auth
				.logout()
				.then(() => {
					// check if this route has the middleware auth
					this.$router.push(this.localePath({ name: "index" }))
				})
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/mixins";

.logo {
	@include responsive("height", 42px, 42px, 48px);
}

.unread-notification {
	background-color: #fcf6f6;
}
</style>
