<template>
	<div class="d-flex align-center justify-center fill-height fill-width text-center">
		<v-sheet color="transparent" class="primary--text">
			<h1 class="text-h1">
				{{ error.statusCode }}
			</h1>
			<h1>
				{{ msg }}
			</h1>
			<v-btn outlined exact class="mt-4" color="primary" :to="localePath('/')">
				Home page
			</v-btn>
		</v-sheet>
	</div>
</template>

<script>
export default {
	name: "EmptyLayout",
	layout: "base",
	props: {
		error: {
			type: Object,
			default: null,
		},
	},
	data() {
		return {
			pageNotFound: "Page Not Found",
			otherError: "An error occurred",
		}
	},
	head() {
		const title = this.error.statusCode === 404 ? this.pageNotFound : this.otherError
		return {
			title,
			htmlAttrs: {
				lang: this.$i18n.locale,
			},
		}
	},
	computed: {
		msg() {
			switch (this.error.statusCode) {
				case 404:
					return this.pageNotFound
				default:
					return this.otherError
			}
		},
	},
}
</script>

<style scoped>
h1 {
	font-size: 20px;
}
</style>
