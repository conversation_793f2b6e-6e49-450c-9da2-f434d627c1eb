<template>
	<v-app>
		<div v-if="$config.isDev" style="
				position: fixed;
				right: 0;
				top: 0;
				padding: 8px;
				background-color: rgb(0 0 0 / 70%);
				color: #fff;
				text-transform: uppercase;
				z-index: 10;
			">
			{{ $vuetify.breakpoint.name }}
		</div>

		<v-app-bar :height="height" extension-height="72" hide-on-scroll class="main-header" fixed app light color="unset"
			elevate-on-scroll>
			<secondary-bar />
			<!-- it will get hidden from css below -->

			<template #extension>
				<div class="flex-grow-1">
					<v-container class="pa-0">
						<div class="menu-bar d-flex align-center py-2" :style="{ 'max-height': 'var(--main-bar-height)' }">
							<div class="flex-grow-0 flex-shrink-0 ">
								<nuxt-link :to="localePath({ name: 'index' })" class="d-block py-2">
									<img height="56" src="/images/logo.png">
								</nuxt-link>
							</div>
							<v-btn id="categories-menu-activator" :class="{ 'v-btn--hover': isCategoriesMenuActive }"
								class="mx-4 hidden-sm-and-down" text large>
								<!-- <v-icon left>mdi-menu</v-icon> -->
								<div class="text-subtitle-1">
									<b>{{ $t("common.categories") }}</b>
								</div>
								<v-icon right>
									mdi-chevron-down
								</v-icon>
							</v-btn>

							<!-- <v-spacer /> -->

							<main-search class="flex-grow-1 hidden-sm-and-down" />
							<v-spacer class="hidden-lg-and-down" />

							<v-btn v-if="!$auth.loggedIn" text large class="hidden-sm-and-down"
								@click="$nuxt.$emit('auth.show', 'login')">
								<v-icon left large>
									mdi mdi-account-outline
								</v-icon>
								<div>
									<div>{{ $t("layout.sign-in-register") }}</div>
								</div>
							</v-btn>
							<user-menu v-else class="hidden-sm-and-down" />

							<v-btn icon large class="hidden-sm-and-down" @click="$nuxt.$emit('wishlist.show')">
								<v-badge color="primary" :content="wishlistItems?.length || 0" :value="!!wishlistItems?.length"
									icon offset-y="8" offset-x="8">
									<v-icon>mdi mdi-cards-heart</v-icon>
								</v-badge>
							</v-btn>

							<!-- <v-btn icon large>
								<v-badge color="primary" content="3" icon offset-y="8" offset-x="8">
									<v-icon>mdi mdi-bell-outline</v-icon>
								</v-badge>
							</v-btn> -->

							<v-btn icon large class="me-1 hidden-sm-and-down" @click="$nuxt.$emit('cart.show')">
								<v-badge color="accent4" :content="$store.state.cart.items.length" icon offset-y="8"
									offset-x="8" :value="!!$store.state.cart.items.length">
									<v-icon>mdi mdi-cart-variant</v-icon>
								</v-badge>
							</v-btn>
							<v-spacer class="hidden-md-and-up" />
							<v-btn icon class="hidden-md-and-up" @click="mobileSearchMenuModel = !mobileSearchMenuModel">
								<v-icon>mdi-magnify</v-icon>
							</v-btn>
							<v-btn icon class="hidden-md-and-up" @click="mobileMainMenuModel = !mobileMainMenuModel">
								<v-icon>mdi-menu</v-icon>
							</v-btn>
						</div>
					</v-container>
				</div>
			</template>
		</v-app-bar>

		<client-only>
			<mobile-main-menu v-model="mobileMainMenuModel" />
			<mobile-search-menu v-model="mobileSearchMenuModel" />
		</client-only>
		<products-comparison />
		<v-main style="padding-top: var(--main-and-secondary-bar-height)">
			<!-- <v-container> -->
			<Nuxt />
			<!-- </v-container> -->
		</v-main>
		<v-footer class="hidden-sm-and-down">
			<v-row>
				<v-col cols="15" sm="6" md="4" lg="4">
					<v-list dense>
						<v-list-item>
							<v-list-item-content>
								<v-list-item-title>
									<v-icon class="ml-2" color="grey">
										mdi-format-list-bulleted-square
									</v-icon>
									<span class="p-0"> {{ $t("common.terms-and-policy") }} </span>
								</v-list-item-title>

								<v-divider class="my-4" />
							</v-list-item-content>
						</v-list-item>
						<v-list-item v-for="(page, pageIndex) in pages" :key="`page-${pageIndex}`"
							:href="page.slug === 'blog' ? `${baseURI}/blog` : undefined" :to="page.slug !== 'blog' ? localePath({
								name: 'page',
								params: {
									slug: page.slug,
								},
							}) : undefined
								">
							<v-list-item-title>
								{{ page.name }}
							</v-list-item-title>
						</v-list-item>
					</v-list>
				</v-col>

				<!-- Remove payment methods section -->
				<!-- <v-col cols="12" sm="6" md="6" lg="3">
					<v-list dense>
						<v-list-item>
							<v-list-item-content>
								<v-list-item-title href="https://web.facebook.com/actionmobile">
									<v-icon color="grey">mdi-tag</v-icon>
									<span class="p-0"> {{ $t("common.payment-methods") }} </span>
								</v-list-item-title>

								<v-divider class="my-4"></v-divider>

								<v-list-item-subtitle>
									<v-list-item-icon class="mx-1">
										<v-img
											class="rounded bordered-element"
											:elevation="10"
											max-height="50"
											max-width="50"
											src="https://upload.wikimedia.org/wikipedia/commons/a/ac/Old_Visa_Logo.svg"
										></v-img>
									</v-list-item-icon>

									<span>{{ $t("common.visa") }} </span>
								</v-list-item-subtitle>

								<v-list-item-subtitle>
									<v-list-item-icon class="mx-1">
										<v-img
											class="rounded bordered-element"
											max-height="50"
											max-width="50"
											src="data:image/png;base64,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"
										></v-img>
									</v-list-item-icon>

									<span>{{ $t("common.madfooatcoma") }}</span>
								</v-list-item-subtitle>

								<v-list-item-subtitle>
									<v-list-item-icon class="mx-1">
										<v-img
											class="rounded bordered-element"
											max-height="50"
											max-width="50"
											src="https://e7.pngegg.com/pngimages/741/683/png-clipart-mobile-payment-money-cash-telephone-iphone-angle-white.png"
										></v-img>
									</v-list-item-icon>

									<span> {{ $t("common.payment-is-cash") }} </span>
								</v-list-item-subtitle>
							</v-list-item-content>
						</v-list-item>
					</v-list>
				</v-col> -->

				<v-col cols="15" sm="6" md="6" lg="4">
					<v-list dense>
						<v-list-item>
							<v-list-item-content>
								<v-list-item-title href="https://web.facebook.com/actionmobile">
									<v-icon class="ml-2" color="grey">
										mdi-clipboard-text
									</v-icon>
									<span class="p-0"> {{ $t("common.services") }} </span>
								</v-list-item-title>

								<v-divider class="my-4" />

								<v-list-item-subtitle v-for="(category, index) in categories" :key="category.id">
									<v-btn :id="index" text plain :to="decodeURIComponent(
										localePath({
											name: 'category',
											params: {
												slugs: `${category.slug}`,
											},
										})
									)
										">
										{{ category.name }}
									</v-btn>
								</v-list-item-subtitle>
							</v-list-item-content>
						</v-list-item>
					</v-list>
				</v-col>
				<v-col cols="15" sm="6" md="6" lg="4">
					<v-list dense>
						<v-list-item>
							<v-list-item-content>
								<v-list-item-title>
									<v-icon color="grey">
										mdi-gesture-tap-button
									</v-icon>
									<span class="pl-2 text-lg"> {{ $t("common.support") }} </span>
								</v-list-item-title>

								<v-divider class="my-4" />

								<v-sheet class="d-flex">
									<div>
										<v-icon color="blue" right>
											mdi-phone
										</v-icon>
									</div>

									<div>
										<div class="mb-2">
											{{ $t("common.customer-support") }}
										</div>
										<p class="text-body-2 muted-3">
											{{ $t("common.customer-support-get-your-texts-emails-answered-in-your-native-language")
											}}
										</p>
										<div class="text-body-2">
											<div>
												<a class="muted-3 black--text" href="tel:+962791009595" dir="ltr">+962-791009595</a>
											</div>
											<div>
												<a class="muted-3 black--text" target="blank"
													href="mailto:<EMAIL>"><EMAIL></a>
											</div>
										</div>
									</div>
								</v-sheet>

								<!-- <v-list-item-title class="my-4" href="https://web.facebook.com/actionmobile">
									{{ $t("common.customers-service") }}
								</v-list-item-title> -->
							</v-list-item-content>
						</v-list-item>

						<!-- Hidden for now -->
						<!-- <v-list-item>
							<v-list-item-content>
								<v-list-item-title href="https://web.facebook.com/actionmobile">
									{{ $t("common.download-our-app") }}</v-list-item-title
								>
								<v-list-item-subtitle class="d-flex">
									<v-img
										max-height="100"
										max-width="100"
										src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png"
									/>
									<v-img
										max-height="100"
										max-width="100"
										src="https://w7.pngwing.com/pngs/314/368/png-transparent-itunes-app-store-apple-logo-apple-text-rectangle-logo.png"
									/>
								</v-list-item-subtitle>
							</v-list-item-content>
						</v-list-item> -->
					</v-list>
				</v-col>
			</v-row>

			<div class="line" />

			<v-row>
				<v-col cols="15" sm="12" xm="12" md="4" lg="4" class="text-sm-center">
					<p>
						{{ $t("common.copyright-2023-action-website-all-rights-reserved") }}
					</p>
				</v-col>
				<v-col cols="12" sm="12" xm="12" md="4" lg="4" class="text-sm-center">
					<!-- Hidden for now - til target pages are created -->
					<!-- <v-btn v-for="(link, I) in links" :key="I" :href="link.to" target="_blank" text class="pa-2">
						<span class="pl-1">{{ link.text }}</span>
						<span class="circle"></span>
					</v-btn> -->
					<v-btn :to="localePath({
						name: 'page',
						params: {
							slug: links[2].slug,
						},
					})
						" text class="pa-2">
						<span class="pl-1">{{ links[2].name }}</span>
						<span class="circle" />
					</v-btn>
				</v-col>
				<v-col cols="12" sm="12" xm="12" md="4" lg="4" class="text-sm-center">
					<div class="d-flex justify-md-end">
						<span class="px-4 pa-2"> {{ $t("common.follow-us") }} </span>
						<v-btn v-for="(social, index) in socials" :key="index" icon color="grey lighten-2" :href="social.url"
							target="_blank">
							<v-icon size="22px">
								{{ social.icon }}
							</v-icon>
						</v-btn>
					</div>
				</v-col>
			</v-row>
		</v-footer>
		<v-bottom-navigation app class="hidden-md-and-up" grow elevation="1" :scroll-threshold="50">
			<v-btn :to="localePath({ name: 'index' })">
				<span>{{ $t('layout.home') }}</span>

				<v-icon>mdi-home</v-icon>
			</v-btn>

			<v-btn :input-value="mobileSearchMenuModel" @click="mobileSearchMenuModel = !mobileSearchMenuModel">
				<span>{{ $t('layout.search') }}</span>
				<v-icon>mdi-magnify</v-icon>
			</v-btn>

			<!-- <v-btn :to="localePath({ name: 'index' })">
				<span>Categories</span>
				<v-icon>mdi-home</v-icon>
			</v-btn> -->

			<v-btn @click="$nuxt.$emit('cart.show')">
				<span>{{ $t('layout.cart') }}</span>
				<v-badge color="accent4" :content="$store.state.cart.items.length" icon offset-y="8" offset-x="8"
					:value="!!$store.state.cart.items.length">
					<v-icon>mdi-cart</v-icon>
				</v-badge>
			</v-btn>

			<v-btn :to="localePath({ name: 'my-index' })">
				<span>{{ $t('layout.account') }}</span>

				<v-icon>mdi-account</v-icon>
			</v-btn>
		</v-bottom-navigation>
		<auth-modal />
		<client-only>
			<Toasts />
		</client-only>
		<client-only>
			<WishlistDrawer />
		</client-only>
		<client-only>
			<CartDrawer />
		</client-only>
		<v-menu v-model="isCategoriesMenuActive" activator="#categories-menu-activator" close-delay="200" offset-y
			nudge-bottom="14px" max-height="400" open-on-hover>
			<v-card class="categories-menu">
				<v-row no-gutters>
					<v-col cols="3">
						<v-list dense class="categories-list flex-shrink-0">
							<v-list-item-group>
								<v-list-item v-for="(L1, L1i) in categories" :key="'L1-' + L1i" class="level-1"
									:to="decodeURIComponent(localePath({ name: 'category', params: { slugs: L1?.slug } }))"
									@mouseenter="hoveredItem = L1">
									<v-list-item-action>
										<v-icon />
									</v-list-item-action>
									<v-list-item-content>
										<v-list-item-title class="text-capitalize" v-text="L1.name" />
									</v-list-item-content>
									<v-list-item-action>
										<v-icon> mdi-chevron-right </v-icon>
									</v-list-item-action>
								</v-list-item>
							</v-list-item-group>
						</v-list>
					</v-col>
					<v-col v-show="!!hoveredItem" cols="9">
						<v-card flat class="categories-children flex-grow-1 flex-shrink-0">
							<v-container fluid>
								<v-row>
									<v-col v-for="(L2, L2i) in hoveredItem?.children" :key="'L2-' + L2i" cols="4">
										<v-list-item dense class="level-2" :to="decodeURIComponent(
											localePath({
												name: 'category',
												params: {
													slugs: `${hoveredItem?.slug}/${L2?.slug}`,
												},
											})
										)
											">
											<v-list-item-content>
												<v-list-item-title class="text-capitalize">
													{{ L2.name }}
												</v-list-item-title>
											</v-list-item-content>
										</v-list-item>
										<v-divider class="my-2" />

										<div class="ps-2">
											<v-list-item v-for="(L3, L3i) in L2?.children" :key="'L3i' + L3i" dense class="level-3"
												:to="decodeURIComponent(
													localePath({
														name: 'category',
														params: {
															slugs: `${hoveredItem?.slug}/${L2?.slug}/${L3?.slug
																}`,
														},
													})
												)
													">
												<v-list-item-content>
													<v-list-item-title class="text-capitalize">
														{{ L3.name }}
													</v-list-item-title>
												</v-list-item-content>
											</v-list-item>
										</div>
									</v-col>
								</v-row>
							</v-container>
						</v-card>
					</v-col>
				</v-row>
			</v-card>
		</v-menu>
	</v-app>
</template>

<script>
import { mapGetters } from "vuex"
import config from "~/mixins/config"
export default {
	name: "WebsiteLayout",
	mixins: [config],
	data() {
		return {
			// systemBarHeight: 0,
			// mainBarHeight: 72,
			hoveredItem: null,
			isCategoriesMenuActive: false,
			mobileMainMenuModel: false,
			mobileSearchMenuModel: false,
			height: 42,
			pages: [
				{
					slug: "privacy-policy",
					name: this.$t("common.privacy-policy"),
				},
				{
					slug: "website-use-policy",
					name: this.$t("common.website-use-policy"),
				},
				{
					slug: "terms-of-wallet",
					name: this.$t("common.terms-of-wallet"),
				},
				{
					slug: "terms-of-service",
					name: this.$t("common.terms-of-service"),
				},
				{
					slug: "blog",
					name: this.$t("common.blog"),
				},
			],
			links: [
				{
					to: this.localePath({
						name: "branch",
					}),
					text: this.$t("common.branches"),
				},

				{
					to: this.localePath({
						name: "contact",
					}),
					text: this.$t("common.connect-us"),
				},
				{
					slug: "about-us",
					name: this.$t("common.about-us"),
				},
			],

			socials: [
				{
					url: "https://www.facebook.com/Actionwebsitejo",
					icon: "mdi-facebook",
				},
				{
					url: "https://x.com/actionwebsite?s=11",
					icon: "mdi-twitter",
				},
				{
					url: "https://www.instagram.com/actionwebsite/",
					icon: "mdi-instagram",
				},
				{
					url: "https://t.snapchat.com/a2P8N1QR",
					icon: "mdi-snapchat",
				},
			],
		}
	},
	head() {
		return {
			htmlAttrs: {
				lang: this.$i18n.locale,
			},
		}
	},
	// async fetch() {
	//     await this.$axios.$get(`/v1/pages`).then((resp) => {
	//         this.pages = resp;
	//     });
	// },
	// async fetch() {
	// 	await this.$axios.$get(`/v1/categroies`).then((resp) => {
	// 		this.categroies = resp;
	// 	});
	// },

	// async fetch() {
	// 	this.products = this.pagination.perPage;

	// 	const query = {
	// 		perPage: this.pagination.perPage,
	// 	};
	// 	const promises = [];

	// 	promises.push(
	// 		this.$axios
	// 			.$get(`v1/my/wishlist`, {
	// 				params: {
	// 					...this.$route.query,
	// 					...query,
	// 				},
	// 			})
	// 			.then((resp) => {
	// 				this.products = resp.items;
	// 				this.pagination = resp.pagination;
	// 			})
	// 	);

	// 	await Promise.all(promises).catch((error) => {
	// 		this.genericErrorHandler(error);
	// 	});
	// },
	computed: {
		availableLocales() {
			return this.$i18n.locales.filter(i => i.code !== this.$i18n.locale)
		},
		categories() {
			return this.$store.state.categories
		},
		baseURI() {
			return process.env.NUXT_ENV_BASE_URL || 'https://action.jo'
		},
		...mapGetters({ wishlistItems: "wishlist/wishlistItems" }),
	},
	created() {
		this.$vuetify.rtl = this.$i18n.localeProperties.dir === "rtl"
	},
	mounted() {
		this.$store.dispatch("wishlist/get")
		window.addEventListener(
			"keydown",
			function (e) {
				// console.log(e);
				if (e.key === "F8") {
					/* eslint-disable-next-line */
					debugger;
				}
			},
			false,
		)

		this.adjustHeader()

		window.addEventListener("resize", this.adjustHeader)
	},
	methods: {
		adjustHeader() {
			this.$nextTick(() => {
				if (this.isMobile) {
					this.height = 72
				}
			})
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/mixins.scss";

.box-border {
	border: 1px solid #d8d4d4;
	border-radius: 4px;
	padding: 10px;
	width: 70px;
	height: 100px;
}

.line {
	margin-top: 1rem;
	width: 100%;
	margin-bottom: 1rem;
	border-top: 1px solid #f2f5f6;
}

#item {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

#item:hover {
	color: #29a6df;
}

.no-space {
	margin: 0;
}

.no-space .v-list-item {
	margin-bottom: 0;
}

.center-element {
	justify-content: center;
}

@media (max-width: 600px) {
	.center-element {
		justify-content: center;
	}
}

.circle {
	width: 5px;
	height: 5px;
	margin-left: 2px;
	border-radius: 50%;
	background-color: #29a6df;
	/* Set the background color to your desired color */
}

.bordered-element {
	border: 1px solid #4e4e4e;
}

.elevated-image {
	width: 100%;
	height: auto;
}

.categories-menu {
	// max-height: calc(100vh - var(--main-bar-height) - 100px);
	// overflow: hidden;
	width: 60vw;

	.categories-list {
		max-height: 400px;
		background-color: #fff;
		overflow-y: scroll;

		.v-list-item {
			min-height: 32px !important;

			.v-list-item__action {
				margin: 0;
			}

			&.level-3 {
				.v-list-item__title {
					font-size: 12px;
				}
			}
		}

		.v-list-item.level-3 {
			.v-list-item__title {
				font-size: 12px;
			}
		}
	}

	.categories-children {
		max-height: 400px;
		background-color: #fff;
		overflow-y: scroll;

		.v-list-item {
			min-height: 32px !important;

			.v-list-item__action {
				margin: 0;
			}

			&.level-3 {
				.v-list-item__title {
					font-size: 14px;
				}
			}
		}
	}
}

@media screen and (max-width: 960px) {
	.main-header>.v-toolbar__content {
		display: none;
	}
}

.main-header {
	@include responsive("height", 72px !important, 114px, 114px);

	.v-toolbar__content {
		@include responsive("height", 0 !important, 42px, 42px);
	}

	&.v-app-bar--is-scrolled {
		// @include responsive("transform", translateY(-72px) !important, 114px, 114px);
	}
}
</style>
