export default {
    props: {
        value: {
            type: [String, Number, Boolean, Array, Object],
            default: null,
        },
        extra: {
            type: Object,
            default: () => ({}),
        },
        name: {
            type: String,
            default: null,
        },
        prefix: {
            type: String,
            default: null,
        },
        suffix: {
            type: String,
            default: null,
        },
        options: {
            type: Array,
            default: () => [],
        },
        multiple: {
            type: Boolean,
            default: false,
        },
			clearable: {
			type: Boolean,
			default: false,
		},

    },
    computed: {
        localValue: {
            get() {
                return this.value
            },
            set(value) {
                this.$emit('input', value)
            },
        },
    },
}
