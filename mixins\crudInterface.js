export default {
	data() {
		return {
			crud<PERSON>ey: 'crud',
			item: {},

		}
	},
	methods: {
		edit(payload) {
			this.$refs[this.crudKey].edit(payload)
		},
		new() {
			this.$refs[this.crudKey].new()
		},
		delete(payload) {
			this.$refs[this.crudKey].delete(payload)
		},
	},
	mounted() {
		const keyToWatch = `$refs.${this.crudKey}.item`
		this.$watch(keyToWatch, (newVal) => {
			// this.item = newVal
			const cloned = this.$cloneDeep(newVal)
			console.log('newVal', cloned)
			this.$set(this, 'item', cloned)
		}, {
			immediate: true,
			deep: true,
		})
	},

	// watch: {
	//     // Watch for changes on the $refs.crud.item, assuming `crud` is always present
	//     ['$refs.' + this.crudKey + '.item']: {
	//         handler(newVal) {
	//             // Update the local item whenever the ref's item changes
	//             this.item = newVal;
	//         },
	//         immediate: true, // Immediately sync the local item with the $ref's item
	//         deep: true, // In case the item is an object, watch deeply
	//     },
	// },

}
