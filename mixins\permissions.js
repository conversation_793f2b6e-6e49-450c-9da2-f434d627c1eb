export default {
	props: {
		value: {
			type: Array,
			default: () => {
				return []
			},
		},
		item: {
			type: Object,
			default: () => {},
		},
		allWildValue: {
			type: [String, Number, Array],
			default: null,
		},
		disableNotInherited: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value
			},
			set(value) {
				this.$emit("input", [...new Set(value)])
			},
		},
		isAllowed() {
			if (this.disableNotInherited) {
				return this.item.meta?.allowed
			}
			return true
		},
		deepChildrenValues() {
			// recursive function to get all children values
			const values = []
			const getChildren = (item) => {
				if (item?.children) {
					item.children.forEach((child) => {
						values.push(child.value)
						getChildren(child)
					})
				}
			}
			getChildren(this.item)

			return values
		},
		deepAllWildValue() {
			// recursive function to get all children values
			const values = []
			const getChildren = (item) => {
				if (item.meta?.all) {
					values.push(item.meta.all)
				}
				if (item?.children) {
					item.children.forEach((child) => {
						if (child.meta?.all) {
							values.push(child.meta.all)
						}
						getChildren(child)
					})
				}
			}
			getChildren(this.item)

			return values
		},
		directChildrenValues() {
			if (!this.item?.children) { return [] }
			return this.item.children.map(child => child.value).filter(v => v)
		},
		isAllChildrenSelected() {
			return this.deepChildrenValues.every(v => this.localValue.includes(v))
		},
		isSomeChildrenSelected() {
			return this.deepChildrenValues.some(v => this.localValue.includes(v))
		},
		isAllSelected() {
			if (Array.isArray(this.allWildValue)) {
				return this.allWildValue.some(v => this.localValue.includes(v))
			}
			return this.localValue.includes(this.allWildValue)
		},
		isItemSelected() {
			return this.localValue.includes(this.item.value)
		},
		itemMetaAll() {
			return this.item.value
		},
		isSelectedByRule() {
			return this.item.meta.via_role
		},
		hasChildrenSelectedByRule() {
			if (!this.item?.children || this.item.meta.via_role) { return false }
			// find if any of the children is selected by rule
			const findSelectedByRule = (item) => {
				if (item.meta.via_role) {
					return true
				}
				if (item?.children) {
					return item.children.some(findSelectedByRule)
				}
				return false
			}
			return this.item.children.some(findSelectedByRule)
		},
	},

	methods: {
		select(value, isSelected) {
			let local = this.localValue.filter(v => !this.deepChildrenValues.includes(v))
			if (isSelected) {
				local.push(value)
			} else {
				// remove value from local
				local = local.filter(v => v !== value)
			}
			this.localValue = local
			this.$emit("selected")
		},
		parentSelectHandler() {
			this.$nextTick(() => {
				if (this.isAllChildrenSelected) {
					const local = this.localValue.filter(v => !this.deepChildrenValues.includes(v))

					local.push(this.item.value)

					this.localValue = local
				}
			})
			// else if (this.isSomeChildrenSelected) {
			// 		console.log("2 unselect");
			// 		if (this.item.value) {
			// 			if (!this.localValue.includes(this.item.value)) {
			// 				this.localValue.push(this.item.value);
			// 			}
			// 		}
			// 	} else if (!this.isRoot) {
			// 		console.log("3 unselect");
			// 		// this.localValue = this.localValue.filter((v) => ![this.item.meta.all, this.item.value].includes(v));
			// 	}
			// 	this.$emit("selected");
			// 	//	});
		},
	},
}
