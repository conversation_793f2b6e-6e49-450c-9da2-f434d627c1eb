// const json = require("~/locales/en.json");
export default {
	data() {
		return {
			groups: ["common", "validation", "email", "errors", "messages", "title", "menu", "ratings", "auth", "rules", "login", "header", "listing", "product", "vc", "users", "order"],
		}
	},
	computed: {
		isNewKeyExist() {
			return this.$te(this.fullNewKey)
		},
	},
	watch: {
		group: {
			immediate: true,
			handler(group) {
				// this.setSubGroups(group);
			},
		},
	},
	methods: {
		// setSubGroups(group) {
		// 	console.log("setSubGroups", group);
		// 	if (!group || !this.$nuxt.context.i18n.messages.en[group]) {
		// 		this.$set(this, "subGroups", []);
		// 		return null;
		// 	}
		// 	console.log("setSubGroups", group, Object.keys(this.$nuxt.context.i18n.messages.en[group]));

		// 	const subGroups = Object.keys(this.$nuxt.context.i18n.messages.en[group]);

		// 	if (group === "portal" && !subGroups.includes(this.getRouteBaseName())) {
		// 		subGroups.unshift(this.getRouteBaseName());
		// 	}
		// 	this.$set(this, "subGroups", subGroups);
		// },
		copyKey(item) {
			const key = `${item.group}.${item.key}`
			navigator.clipboard.writeText(key)

			this.$toast.info(`Key "${key}" copied to clipboard`, { title: "test" })
			// this.$toast.error(`Key "${key}" copied to clipboard`);
		},
		deletePlaceHolder(value) {
			this.$confirm("Are you sure you want to delete this placeholder?").then((isAccepted) => {
				if (isAccepted) {
					const index = this.form.placeholders.findIndex(item => item.value === value)
					if (index > -1) {
						this.form.placeholders.splice(index, 1)
					}
				}
			})
		},
		async pasteHandler() {
			try {
				const permission = await navigator.permissions.query({ name: "clipboard-read" })
				if (permission.state === "denied") {
					this.$toast.error("Please Allow the clipboard read permission to use this feature.", {
						title: "Clipboard permission denied",
					})
				}

				const clipboardContents = await (await navigator.clipboard.readText())?.toLowerCase()

				if (clipboardContents && /^[a-z]+(\.[a-z0-9-]+)+/.test(clipboardContents)) {
					const keyParts = clipboardContents.split(".")
					const group = keyParts[0]
					const key = keyParts[keyParts.length - 1]
					this.$refs.group.setGroup(group)
					this.$refs.key.setKey(key)
				} else {
					this.$toast.warning('Please copy a valid "group.key" content.', {
						title: "Invalid key content",
					})
				}
			} catch (error) {
				console.error(error.message)
			}
		},
		toKebabCase(str) {
			return (
				str &&
				str
					.match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g)
					.map(x => x.toLowerCase())
					.join("-")
			)
		},
		oldFilesHandler(messages) {
			const fullKey = this.toKebabCase(messages[this.$i18n.locale].key)
			const fullNewKey = fullKey.split(".")
			let group, key
			if (fullNewKey.length === 2) {
				group = fullNewKey[0]

				key = fullNewKey[1]
			} else {
				// group = null; //no need to do anything just keep it as its

				key = fullNewKey[0]
			}
			if (this.$refs.group && !this.$refs.group.value) {
				this.$refs.group.setGroup(group)
			}
			if (this.$refs.key && !this.$refs.key.value) {
				this.$refs.key.setKey(key)
			}

			this.setValues(messages)
		},
		setValues(messages) {
			for (const locale in messages) {
				this.setValue(locale, messages[locale].value)
			}
		},
		setValue(localeCode, value) {
			this.$refs.editor.setValueByLocale(localeCode, value)
			// const locale = this.form.languages.find((item) => item.abbreviation === localeCode);
			// locale.value = value; // this.$te(this.fullNewKey, locale) ? this.$t(this.fullNewKey, locale) : locale.value;
		},
	},
}
