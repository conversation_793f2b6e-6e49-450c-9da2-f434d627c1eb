import chalk from "chalk"
const util = require("util")
const redis = require("redis")
const keyParser = function (key) {
	if (!key.startsWith("ComponentCache")) {
		return { key }
	}
	const splittedKey = key.split("__")
	return {
		key: splitted<PERSON>ey[0],
		timeout: Number(splittedKey[1]) || undefined,
		mode: splittedKey[2],
	}
}
const ModuleDebugName = "[Nuxt/ComponentsRedisCache]"

// console.log("NODE_APP_INSTANCE", process.env.NODE_APP_INSTANCE);
export default function (moduleOptions) {
	if (this.options.render.ssr === false) {
		// SSR Disabled
		return
	}

	// Create empty bundleRenderer object if not defined
	if (typeof this.options.render.bundleRenderer !== "object" || this.options.render.bundleRenderer === null) {
		this.options.render.bundleRenderer = {}
	}

	// Disable if cache explicitly provided in project
	if (this.options.render.bundleRenderer.cache) {
		return
	}

	moduleOptions = Object.assign(
		{
			debug: false,
			redis: {
				host: "127.0.0.1",
				port: 6379,
				// path: null,
				// url: null,
				// string_numbers: null,
				// return_buffers: false,
				// detect_buffers: false,
				// socket_keepalive: true,
				// socket_initial_delay: 0,
				// no_ready_check: false,
				// enable_offline_queue: true,
				// retry_unfulfilled_commands: false,
				// password: null,
				// auth_pass: null,
				// user: null,
				// db: 0,
				// family: "IPv4",
				// disable_resubscribing: false,
				// rename_commands: null,
				// tls: null,
				// prefix: process.env.NUXT_ENV_APP_NAME,
				// connect_timeout: 3600000,
			},
			maxExpiry: 60 * 60 * 24 * 1000,
			timeout: 50,
		},
		this.options.componentsRedisCache,
		moduleOptions,
	)

	let cache = {}

	this.nuxt.hook("listen", (server, { host, port }) => {
		const client = redis.createClient(moduleOptions.redis.port, moduleOptions.redis.host, moduleOptions.redis)
		client.on("error", (error) => {
			cache = {}
			console.error(error)
		})
		client.on("ready", () => {
			cache = client
		})
		client.on("connect", () => {
			cache = client
		})
		// // client.select(0, async () => {
		client.get = util.promisify(client.get)
		client.set = util.promisify(client.set)
		// redis.RedisClient.prototype.delWildcard = function (key, callback) {
		//     const redis = this;

		//     redis.keys(key, function (err, rows) {
		//         if (err) {
		//             return console.log(err);
		//         }
		//         for (let i = 0, j = rows.length; i < j; ++i) {
		//             redis.del(rows[i]);
		//         }
		//         if (callback) return callback();
		//     });
		// };
		// client.delWildcard("ComponentCache::*");
		client.flushdb()
		cache = client

		this.nuxt.options.cli.badgeMessages.push(
			`Redis Caching: ${chalk.underline.yellow(moduleOptions.redis.host + ":" + moduleOptions.redis.port)}`,
		)
		// this.nuxt.options.publicRuntimeConfig.cache = cache;
		// moduleOptions.debug && console.log(ModuleDebugName, "Creating Redis instance");
		// for (const key in this.nuxt.server) {
		//     console.log("this", key);
		// }
		// // console.log(this.nuxt);
		// this.addPlugin({
		//     src: path.resolve(__dirname, "plugin.js"),
		//     fileName: "plugin.server.js", // [optional] only include in server bundle
		//     options: moduleOptions,
		// });
	})

	this.nuxt.hook("close", (nuxt) => {
		if (cache.quit) {
			cache.quit(function () {
				moduleOptions.debug && console.log(ModuleDebugName, "Disconnecting Redis")
			})
		}
	})

	// const moduleParsedOptions = {
	//     // namespace: 'users',
	//     max: moduleOptions.max || 10000,
	//     stale: moduleOptions.stale || false,
	//     timeout: moduleOptions.timeout || 50,
	//     failsafe: "resolve",
	// };
	global.before = Date.now()
	this.options.render.bundleRenderer.cache = {
		get: (key, cb) => {
			global.before = Date.now()
			if (process.env.NODE_ENV === "development") {
				// return cb();
			}

			// var loaded = false;
			if (!cache.connected) {
				moduleOptions.debug && console.warn(ModuleDebugName, "Redis is not connected. Parsing SSR.")
				return cb()
			}
			const comp = keyParser(key)
			const before = Date.now()
			try {
				cache
					.get(comp.key)
					.then((data) => {
						moduleOptions.debug && console.log(ModuleDebugName, comp.key, "in", Date.now() - before, "ms")
						if (data) {
							// moduleOptions.debug && console.log("Found: ", data.substr(0, 100));
							// loaded = true;
							/* eslint-disable-next-line */
							cb({ components: [], html: data });
						} else {
							moduleOptions.debug &&
								console.log(ModuleDebugName, "no data found", "in", Date.now() - before, "ms")
							cb()
						}
					})
					.catch((error) => {
						console.error(error)
						cb()
					})
			} catch (error) {
				console.error(ModuleDebugName, "Couldn't get cache from redis".error)
				cb()
			}

			// setTimeout(() => {
			//     if (!loaded) {
			//         moduleOptions.debug &&
			//             console.log(ModuleDebugName, comp.key, "Redis timeout in", moduleOptions.timeout);
			//         return cb();
			//     }
			// }, moduleOptions.timeout);
		},
		set: (key, val) => {
			try {
				const comp = keyParser(key)
				const timeout =
					comp.timeout && moduleOptions.maxExpiry > comp.timeout ? comp.timeout : moduleOptions.maxExpiry
				console.log(ModuleDebugName, comp.key, "HTML generated in", Date.now() - global.before, "ms")
				cache.set(comp.key, val.html, "EX", timeout).catch((error) => {
					console.error(ModuleDebugName, error)
				})
			} catch (error) {
				console.error(ModuleDebugName, "Couldn't set cache to redis.")
			}
		},
	}
	// }
}
