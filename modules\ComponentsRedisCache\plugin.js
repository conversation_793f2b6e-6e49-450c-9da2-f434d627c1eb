// const util = require("util");
// const redis = require("redis");
// const client = redis.createClient({ port: 6379 });
// // client.select(0, async () => {
// client.get = util.promisify(client.get);
// client.set = util.promisify(client.set);
const redis = require("./redis.js")
export default (ctx, inject) => {
	// for (const key in x) {
	//     console.log(key);
	// }
	inject("redis", redis)
	// console.log(x.params);
}
