export default ({ $redis, $axios, $console }, inject) => {
	const isDev = process.env.NODE_ENV === "development"
	const cacheOrApi = function (url, timeout) {
		if (process.server && $redis.isConnected()) {
			return new Promise((resolve, reject) => {
				// console.log("Getting from cache", url)
				$redis
					.get(url)
					.then((data) => {
						// console.log("🚀 ~ .then ~ data:", data)
						if (data) {
							if (isDev) {
								console.log("Cache found", url)
							}
							resolve(data)
						} else if (url) {
							if (isDev) {
								console.log("Cache not found, Getting from api", url)
							}
							$axios
								.$get(url)
								.then((resp) => {
									// console.log("🚀 ~ .then ~ resp:", resp)
									if (resp) {
										if ($redis.isConnected()) {
											if (timeout) {
												$redis.set(url, resp, "EX", timeout)
											} else {
												$redis.set(url, resp)
											}
											if (isDev) {
												console.log("Cache set", url)
											}
										}

										resolve(resp)
									} else {
										reject(new Error("No responseData for URL:" + url))
									}
								})
								.catch((e) => {
									reject(e)
								})
						} else {
							resolve([null, "cache"])
						}
					})
					.catch((e) => {
						// console.log("🚀 ~ return newPromise ~ e:", e)
						reject(e)
					})
					.finally(() => {
						// console.log("🚀 ~ return newPromise ~ finally")
					})
			})
		} else if (url) {
			if (isDev) {
				console.log("Getting from api", url)
			}
			return $axios.$get(url)
			// 	.then((resp) => {
			// 	if (resp) {
			// 		if ($redis.isConnected()) {
			// 			if (timeout) {
			// 				$redis.set(url, resp, "EX", timeout)
			// 			} else {
			// 				$redis.set(url, resp)
			// 			}
			// 		}
			// 		return resp
			// 	}
			// })
		}
	}
	inject("cacheOrApi", cacheOrApi)
}
