import path from 'path'
export default function ExampleModule(moduleOptions) {
	console.log(moduleOptions.token) // '123'
	console.log(this.options.exampleMsg) // 'hello'

	const options = Object.assign({}, this.options.redis, moduleOptions)
	// this.nuxt.hook('ready', async (nuxt) => {
	// 	console.log('Nuxt is ready')
	// })

	this.addPlugin({
		src: path.resolve(__dirname, 'cacheOrApi.js'),
		fileName: 'cacheOrApi.js',
		options,
	})

	this.addPlugin({
		src: path.resolve(__dirname, 'redis.server.js'),
		fileName: 'redis.server.js',
		options,
	})
}
