const util = require("util");
const redis = require("redis");

const options = <%= JSON.stringify(options, null, 2) %>;

// Validate Redis URL
if (!options.host || !options.port) {
	throw new Error('Missing required Redis environment variables.');
}
const redisUrl = `redis://${options.host}:${options.port}`;

let client;
let isConnected = false;

const connectRedis = () => {
	return new Promise((resolve, reject) => {
		try {
			client = redis.createClient({
				url: redisUrl,
				database: options.database,
			});

			client.get = util.promisify(client.get);
			client.set = util.promisify(client.set);
			client.del = util.promisify(client.del);

			client.on('connect', () => {
				console.log('Redis Connected');
				isConnected = true;
				resolve();
			});

			client.on('error', (error) => {
				console.error("Redis connection error:", error);
				reject(error);
			});

			// client.connect().catch(console.error);
		} catch (error) {
			console.error("Error creating Redis client:", error);
			reject(error);
		}
	});
};

export default async ({ app }, inject) => {
	try {
		await connectRedis();
	} catch (error) {
		console.error("Failed to connect to Redis:", error);
	}

	inject("redis", {
		isConnected() {
			return isConnected;
		},
		async get(key) {
			const data = await client.get(key);
			return JSON.parse(data);
		},
		async set(key, data, ex, timeout) {
			if (ex && timeout) {
				return client.set(key, JSON.stringify(data), ex, timeout);
			} else {
				return client.set(key, JSON.stringify(data));
			}
		},
		async del(key) {
			return client.del(key);
		},
	});
};
