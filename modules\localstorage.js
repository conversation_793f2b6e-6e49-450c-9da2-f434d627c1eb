export const getKey = (key) => {
	if (localStorage) {
		const storageValue = JSON.parse(localStorage.getItem("vrs_"))
		if (typeof storageValue === "object" && storageValue !== null) {
			return storageValue[key]
		}
	}
}

export const setKey = (key, value) => {
	if (localStorage) {
		const storageValue = JSON.parse(localStorage.getItem("vrs_"))
		if (typeof storageValue === "object" && storageValue !== null) {
			storageValue[key] = value
			localStorage.setItem("vrs_", JSON.stringify(storageValue))
		}
	}
}

export default {
	getKey,
	setKey,
}
