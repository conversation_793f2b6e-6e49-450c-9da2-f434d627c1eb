import colors from "vuetify/es5/util/colors"
import locales from "./config/Locales.js"
const isDev = process.env.NODE_ENV === "development"
const defaultLocale = "en"
const config = {
	buildDir: ".nuxt",
	// Disable server-side rendering: https://go.nuxtjs.dev/ssr-mode
	ssr: true, //! Don't change
	target: "server",
	loading: false,
	publicRuntimeConfig: {
		portalType: process.env.NUXT_ENV_PORTAL_TYPE,
		defaultLocale,
		isDev,

		defaultUser: process.env.NUXT_ENV_DEFAULT_USER,
		defaultPassword: process.env.NUXT_ENV_DEFAULT_PASSWORD,
		google: {
			clientId: process.env.NUXT_ENV_GOOGLE_CLIENT_ID,
			clientSecret: process.env.NUXT_ENV_GOOGLE_CLIENT_SECRET,
		},
		facebook: {
			appId: process.env.NUXT_ENV_FACEBOOK_APP_ID,
		},
	},

	privateRuntimeConfig: {},

	// Global page headers: https://go.nuxtjs.dev/config-head
	head: {
		titleTemplate: "%s - Action Mobile",
		title: "Action Mobile",
		// htmlAttrs: {
		// 	lang: "en",
		// },
		meta: [
			{ charset: "utf-8" },
			{ name: "viewport", content: "width=device-width, initial-scale=1" },
			{ hid: "description", name: "description", content: "" },
			{ name: "format-detection", content: "telephone=no" },
			{ name: 'google-site-verification', content: 'sy0qTAzAz7sRktGoDrq7EvAxYknViAK1jaHpOgWR1kc' },
			{ name: 'facebook-domain-verification', content: '6nf09tyaviblgpt2gbtunp5ks5ra1q' },
			{ name: 'facebook-domain-verification', content: 'eb4g0lit8r2s5q1ctl0np47eeds7ij' },
			{ name: 'facebook-domain-verification', content: '5pupubnfmprsr67ouvz3h9sw602bwi' },
			{ 'http-equiv': 'Content-Security-Policy', content: 'upgrade-insecure-requests' },
			{ name: 'facebook-domain-verification', content: '9cr3f6sl18ft4xwvzq83vb0six57z0' },
		],
		link: [
			{ rel: "preconnect", href: "https://fonts.gstatic.com", crossorigin: true },
			{ rel: "preconnect", href: "https://fonts.googleapis.com" },
			{
				rel: "stylesheet", href: `/fonts/general.css`,
			},
			{ rel: "stylesheet", href: "https://cdn.jsdelivr.net/npm/vuetify@2.x/dist/vuetify.min.css" },
			{ rel: "icon", type: "image/x-icon", href: "/fav-icon.ico" },
		],
	},

	layoutTransition: {
		name: "fade-transition",
		mode: "out-in",
	},

	// Global CSS: https://go.nuxtjs.dev/config-css
	css: ["~/assets/general.scss", "~/assets/AboveFold.scss"],

	serverMiddleware: [{ path: "/_ipx", handler: "~/server-middleware/ipx.js" }],
	// serverMiddleware: {
	// 	'/_ipx': '~/server/middleware/ipx.js',
	// },
	// Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
	plugins: [
		"~/plugins/Common.js",
		"~/plugins/TypesConverters.js",
		"~/plugins/i18n.js",
		"~/plugins/axios",
		// "~/plugins/vue-gates.client.js",
		"~/plugins/VuetifyStackedToast.client.js",
		"~/plugins/tooltip.client.js",
		"~/plugins/General.js",
		"~/plugins/General.client.js",
		"~/plugins/Rules.client.js",
		"~/plugins/i18nRouting.js",
		"~/plugins/VuetifyConfirm.js",
		// "~/plugins/firebase.client.js",
		"~/plugins/autoAnimate.client.js",
		"~/plugins/sentry.js",
		'~/plugins/applicationName.js',
	],

	// Auto import components: https://go.nuxtjs.dev/config-components
	components: true,

	// router: {
	// 	extendRoutes(routes, resolve) {
	// 		console.log("extendRoutes", routes);
	// 		return routes.map((route) => {
	// 			return route;
	// 		});
	// 	},
	// },

	// Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
	buildModules: [
		// https://go.nuxtjs.dev/eslint
		"@nuxtjs/eslint-module",
		// https://go.nuxtjs.dev/stylelint
		// "@nuxtjs/stylelint-module",
		// https://go.nuxtjs.dev/vuetify
		"@nuxtjs/vuetify",
		"@nuxtjs/router-extras",
		// [
		// 	"nuxt-purgecss",
		// 	{
		// 		enabled: false, // ({ isDev, isClient }) => !isDev && isClient,
		// 		paths: [
		// 			"node_modules/@nuxtjs/vuetify/**/*.ts",
		// 			"node_modules/@nuxt/vue-app/template/**/*.html",
		// 			"node_modules/@nuxt/vue-app/template/**/*.vue",
		// 			"node_modules/vue-marquee-text-component/**/*.vue",
		// 			"components/**/*.vue",
		// 			"components/*.vue",
		// 			"layouts/*.vue",
		// 			"pages/**/*.vue",
		// 			"plugins/**/*.js",
		// 		],
		// 		styleExtensions: [".css", ".scss", ".sass"],
		// 		whitelist: ["body", "html", "nuxt-progress", "v-application", "v-application--wrap"],
		// 		whitelistPatterns: () => [/^v-((?!application).)*$/, /^\.theme--light*/, /^\.theme--dark*/, /.*-transition/, /col-*/],
		// 		whitelistPatternsChildren: [/^v-((?!application).)*$/, /^theme--light*/, /^theme--dark*/, /col-*/],
		// 		extractors: [
		// 			{
		// 				extractor: content => content.match(/[A-z0-9-:\\/]+/g) || [],
		// 				extensions: ["html", "vue", "js"],
		// 			},
		// 		],
		// 	},
		// ],
		"@nuxtjs/router",

	],

	// Modules: https://go.nuxtjs.dev/config-modules
	modules: [
		// https://go.nuxtjs.dev/axios
		"@nuxtjs/i18n",
		"@nuxtjs/axios",
		"@nuxtjs/auth-next",
		// "@nuxtjs/proxy",
		"nuxt-route-meta",
		"cookie-universal-nuxt",
		"@nuxtjs/dayjs",
		process.env.VUETIFY_CUSTOMIZED_FIELDS || "vuetify-customized-fields",
		"@nuxt/image",
		"@nuxtjs/google-analytics",
		"@nuxtjs/gtm",
		'@nuxtjs/google-gtag',
		'@nuxtjs/sentry',
		'nuxt-facebook-pixel-module',
	],
	'google-gtag': {
		id: process.env.NUXT_ENV_GTAG, // Replace with your Google Analytics Measurement ID
		config: {
			send_page_view: false, // Disable automatic page view tracking
		},
		debug: true,
		plugins: [{ src: '~/plugins/gtag.js', mode: 'client' }],
	},
	facebook: {
		/* module options */
		track: 'PageView',
		pixelId: process.env.NUXT_ENV_FACEBOOK_PIXEL_ID || 'XXXXXXXXXXXXXXX',
		autoPageView: true,
		disabled: isDev,
		debug: true,
	},
	gtm: {
		id: process.env.NUXT_ENV_GTM_ID, // Replace with your GTM ID
		enabled: !isDev, // Disable in development mode
	},
	// googleAnalytics: {
	// 	id: process.env.NUXT_ENV_GOOGLE_ANALYTICS_ID, // Replace with your Google Analytics ID
	// },

	// Axios module configuration: https://go.nuxtjs.dev/config-axios
	axios: {
		// baseURL: process.env.NUXT_ENV_API_BASE_URL,
		baseURL: process.env.NUXT_ENV_PROXY_TARGET_BASE_URL,
		proxy: false,
		debug: false,
		proxyHeaders: true,
		credentials: true,
		headers: {
			common: {
				Accept: "application/json",
			},
		},
		proxyHeadersIgnore: [
			"accept",
			"host",
			"x-forwarded-host",
			"x-forwarded-port",
			"x-forwarded-proto",
			"cf-ray",
			"cf-connecting-ip",
			"content-length",
			"content-md5",
			"content-type",
		],
	},
	auth: {
		plugins: ["~/plugins/auth.js"],
		cookie: {
			options: {
				path: "/",
				maxAge: 60 * 60 * 24 * 365, // 1 year
				secure: !isDev,
			},
		},
		strategies: {
			local: {
				// scheme: "refresh",

				token: {
					property: "accessToken",
					global: true,
					required: true,
					type: "Bearer",
					maxAge: 60 * 60 * 2,
				},
				// refreshToken: {
				// 	property: "token.refreshToken",
				// 	data: "accessToken",
				// 	maxAge: 60 * 60 * 24 * 30, // 1 month
				// },
				user: {
					property: false,
					autoFetch: true,
				},
				endpoints: {
					login: { url: "/v1/auth/login", method: "post" },
					logout: { url: "/v1/auth/logout", method: "delete" },
					user: { url: "/v1/my/profile", method: "get" },
					refresh: { url: "/v1/auth/refresh", method: "post" },
				},
			},
		},
		redirect: {
			// i18n path will be corrected with plugins/auth.js
			login: "login",
			logout: false,
			// callback: "/login",
			home: "/",
		},
		fullPathRedirect: true,
		resetOnError: true,
		rewriteRedirects: true,
	},
	dayjs: {
		locales: ["en", "ar"],
		// locales: dayjsLocales, to import only available and only lazily
		defaultLocale: "ar",
		// defaultTimeZone: 'Asia/Tokyo',
		plugins: [
			"utc", // import 'dayjs/plugin/utc'
			"timezone", // import 'dayjs/plugin/timezone'
			"duration", // import 'dayjs/plugin/duration'
			"localizedFormat", // import 'dayjs/plugin/LocalizedFormat'
			"relativeTime", // import 'dayjs/plugin/relativeTime'
		], // Your Day.js plugin
	},
	i18n: {
		// TODO: fix lazy loading on nuxt3 (test in telescope)
		lazy: {
			skipNuxtState: false,
		},
		vueI18nLoader: false,
		strategy: "prefix",
		langDir: "locales/",
		vueI18n: { fallbackLocale: "ar", silentTranslationWarn: true },
		// skipSettingLocaleOnNavigate: true,
		locales,
		defaultLocale: "ar",
	},
	image: {
		// provider: isDev ? "ipx" : "cloudflare",
		provider: "ipx",
		// provider: isDev ? "ipx" : "bunnyCDN",
		ipx: {
			baseURL: process.env.NUXT_ENV_PUBLIC_PATH ? process.env.NUXT_ENV_PUBLIC_PATH + "/_ipx" : undefined,
			// baseURL: "https://action-v2-frontend.b-cdn.net/_ipx",
		},
		providers: {
			customProvider: {
				name: "bunnyCDN", // optional value to overrider provider name
				provider: "~/modules/BunnyCDN.js", // Path to custom provider
				options: {
					// ... provider options
					baseURL: process.env.NUXT_ENV_PUBLIC_PATH || "/", // https://front-ingot.b-cdn.net
				},
			},
			customProvider2: {
				name: "s3", // optional value to overrider provider name
				provider: "~/modules/BunnyCDN.js", // Path to custom provider
				options: {
					// ... provider options
					baseURL: process.env.NUXT_ENV_S3_CDN_URL || "/", // https://front-ingot.b-cdn.net
				},
			},

		},
		baseURL: "https://media.action.jo",
		screens: {
			xs: 600,
			sm: 960,
			md: 1264,
			lg: 1904,
			xl: Infinity,
		},
	},
	proxy: {
		"/v1/": {
			target: process.env.NUXT_ENV_PROXY_TARGET_BASE_URL,
			changeOrigin: true,
			secure: false,

			on: {
				onProxyRes: (proxyRes, req, res) => {
					console.log("proxyRes", proxyRes)
					console.log("req", req)
					console.log("res", res)
					res.removeHeader("content-length")
				},
				onProxyReq(proxyReq, req, res, options) {
					console.log("proxyRes", proxyReq)
					console.log("req", req)
					console.log("res", res)
					if (req.body) {
						const bodyData = JSON.stringify(req.body)
						// incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
						proxyReq.setHeader("Content-Type", "application/json")
						proxyReq.setHeader("Content-Length", Buffer.byteLength(bodyData))
						// stream the content
						proxyReq.write(bodyData)
					}
				},
			},
		},
	},
	routerModule: {
		path: "routes",
		fileName: "index.js",
		// keepDefaultRouter: false,
	},
	stylelint: {
		/* module options */
		lintOnStart: false,
	},
	sentry: {
		// https://github.com/nuxt-community/sentry-module/blob/v7.5.0/docs/content/en/configuration/options.md
		dsn: process.env.SENTRY_DSN, // Enter your project's DSN here
		// Additional Module Options go here
		// https://sentry.nuxtjs.org/sentry/options
		disabled: isDev || !process.env.SENTRY_ENVIRONMENT || !process.env.SENTRY_DSN,
		config: {
			// Add native Sentry config here
			// https://docs.sentry.io/platforms/javascript/guides/vue/configuration/options/
			environment: process.env.SENTRY_ENVIRONMENT,
			// denyUrls: [
			// 	'https://cdn.livechatinc.com/tracking.js',
			// ],
			// ignoreErrors: [
			// 	/ReferenceError: Can't find variable: gtag$/, // already solved by redirecting to portal
			// 	/undefined is not an object \(evaluating 'e._isDestroyed'\)$/,
			// 	/Unexpected token '<'$/, // https://github.com/getsentry/sentry-javascript/issues/6945#issuecomment-1407548098
			// 	/ResizeObserver loop limit exceeded$/, // https://stackoverflow.com/questions/49384120/resizeobserver-loop-limit-exceeded
			// 	/undefined is not an object \(evaluating 'a.M'\)$/,
			// 	/Non-Error promise rejection captured$/, // https://github.com/getsentry/sentry-javascript/issues/3440
			// 	/Maximum call stack size exceeded$/,
			// 	/Cannot read properties of undefined (reading 'path')$/, // error doesn't show to user. happens just before the redirect
			// 	/Cannot read properties of undefined (reading 'matched')$/, // error doesn't show to user. happens just before the redirect
			// 	/a is undefined/, // error doesn't show to user. happens just before the redirect
			// 	/TypeError: a is undefined/,
			// 	/SecurityError: Failed to execute 'replaceState' on 'History'/,
			// 	/NavigationDuplicated: Avoided redundant navigation to current location/,
			// 	/Page Error Handler:Error: Network Error/, // this occurs when the user is offline, its already handled by the error handler
			// ],

		},
		publishRelease: {
			// Use .env for variables as they are not working from here
			errorHandler(err, invokeErr, compilation) {
				compilation.warnings.push(`Sentry Release Error: ${err.message}`)
			},
			release: {
				// Attach commits to the release (requires that the build triggered within a git repository).
				setCommits: {
					auto: true,
				},
			},
			setCommits: {
				auto: true,
			},

		},

		blacklistUrls: [
			/gtag\.js/,
		],

	},
	// Vuetify module configuration: https://go.nuxtjs.dev/config-vuetify
	vuetify: {
		customVariables: ["~/assets/variables.scss"],
		treeShake: true,
		options: {
			customProperties: true,
			variations: false,
		},
		theme: {
			dark: false,
			default: "light",
			defaultAssets: {
				font: false,
			},
			options: { customProperties: true },
			themes: {
				// dark: {
				// 	// primary: "#ff3b41",
				// 	primary: "#B71F23",
				// 	accent: colors.grey.darken3,
				// 	secondary: "#1a9f72",
				// 	info: colors.blue.lighten1,
				// 	warning: colors.amber.base,
				// 	error: colors.deepOrange.accent4,
				// 	success: colors.green.accent3,
				// 	sidebar: "#363636",
				// 	gold: "#f3c405",
				// 	"inactive-tab": colors.shades.black,
				// 	"active-tab": "#272727",
				// 	background: "#121212",
				// 	deposit: colors.teal.base,
				// },
				light: {
					primary: "#893477",
					// primary: "#94171b",
					accent: colors.grey.darken3,
					info: colors.blue.lighten1,
					secondary: "#1d996b",
					sidebar: "#f7f7fb",
					"baby-blue": "#8da1ae",
					price: "#2e9c00",
					new: "#E65100",
					cashback: "#309700",
					danger: "#f44336",
					// input: "#eaeaef",
					gold: "#FFB100",
					"inactive-tab": colors.grey.lighten3,
					"active-tab": colors.shades.white,
					background: "#fafafa",
					deposit: colors.teal.base,
					accent1: "#09596D",
					accent3: "#008589",
					accent4: "#2EB18D",
					accent5: "#8FD97E",
					accent6: "#F9F871",
					accent7: "#686481",
					accent8: "#C199BB",
					accent9: "#E19E20",
					accent10: "#D7F3FF",
					accent11: "#6B3B4E",
					accent12: "#AD1457",
					accent13: "#f6f7fb",
					wishlist: "#e80d0d",
					positive: "#2e9c00",
					negative: "#d50000",
				},
			},
		},
	},
	vuetifyCustomFields: {
		style: {
			dense: true,
			filled: false,
			rounded: false,
			flat: false,
			outlined: true,
		},
	},
	webpackOptimisations: {
		features: {
			// enable risky optimisations in dev only
			// hardSourcePlugin: isDev,
			// parallelPlugin: isDev,
		},
	},

	// Build Configuration: https://go.nuxtjs.dev/config-build
	build: {
		publicPath: process.env.NUXT_ENV_PUBLIC_PATH ? process.env.NUXT_ENV_PUBLIC_PATH + "/_nuxt" : "/_nuxt",
		analyze: process.env.NUXT_ENV_ANALYZE,
		extractCSS: !isDev,
		loaders: {
			vue: {
				prettify: false,
				compilerOptions: {
					whitespace: process.env.NODE_ENV === "production" ? "condense" : "",
				},
			},

			scss: {
				//	additionalData: '@import "/node_modules/vuetify/src/styles/settings", "~/assets/mixins";',
			},
		},
		postcss: null,
		extend(config, { isDev }) {
			if (isDev) {
				config.devtool = 'eval-source-map' // or 'cheap-module-eval-source-map' or any other dev-friendly sourcemap style
			}
		},
	},
	vue: {
		config: {
			productionTip: false,
			devtools: true,
		},
	},
}

export default config
