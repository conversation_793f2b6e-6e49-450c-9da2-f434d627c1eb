{"name": "action", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext \".js,.vue\" --ignore-path .gitignore .", "lint:style": "stylelint \"**/*.{css,scss,sass,html,vue}\" --ignore-path .gitignore", "lint:prettier": "prettier --check .", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lintfix": "npm run lint:js -- --fix", "delete:cache": "rd /s  \"node_modules/.cache\"", "prod-debug": "node --inspect node_modules/nuxt/bin/nuxt start"}, "dependencies": {"@amcharts/amcharts5": "^5.2.21", "@amcharts/amcharts5-geodata": "^5.0.3", "@chenfengyuan/vue-countdown": "^1.1.5", "@formkit/auto-animate": "^0.7.0", "@nuxtjs/auth-next": "5.0.0-1648802546.c9880dc", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/dayjs": "^1.4.1", "@nuxtjs/google-analytics": "^2.4.0", "@nuxtjs/google-gtag": "^1.0.4", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/i18n": "^7.3.1", "@nuxtjs/localtunnel": "^1.1.3", "@nuxtjs/proxy": "^2.1.0", "@nuxtjs/sentry": "^7.5.0", "@sentry/webpack-plugin": "1.2", "action": "file:", "apexcharts": "^3.35.4", "body-parser": "^1.20.0", "clone-deep": "^4.0.1", "cookie-universal-nuxt": "^2.2.1", "core-js": "^3.19.3", "country-flag-icons": "^1.5.7", "css-file-icons": "^0.1.0", "debounce": "^1.2.1", "deep-equal": "^2.2.2", "dot-object": "^2.1.4", "dotenv": "^16.0.3", "express": "^4.18.1", "firebase": "^9.13.0", "fs": "^0.0.1-security", "google-libphonenumber": "^3.2.32", "i18n-iso-countries": "^7.5.0", "ipx": "^1.1.0", "js-convert-case": "^4.2.0", "js-file-download": "^0.4.12", "json-conditions": "^1.3.4", "libphonenumber-js": "^1.10.12", "lodash": "^4.17.21", "mime": "^3.0.0", "mime-db": "^1.52.0", "mime-type": "^4.0.0", "nuxt": "2.15.8", "nuxt-facebook-pixel-module": "^1.6.0", "nuxt-purgecss": "^1.0.0", "nuxt-route-meta": "^2.3.4", "redis": "3.1", "sortablejs": "^1.4.2", "v-snackbars": "^3.2.8", "vue": "^2.6.14", "vue-apexcharts": "^1.6.2", "vue-gates": "^2.1.2", "vue-json-pretty": "^1.9.3", "vue-marquee-text-component": "^1.2.0", "vue-orgchart": "^1.1.7", "vue-position-sticky": "^0.2.1", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "vue-tooltip-directive": "^1.0.6-1", "vue-zoom-on-hover": "^1.0.6", "vue2-dropzone": "^3.6.0", "vue2-teleport": "^1.0.1", "vuedraggable": "^2.24.3", "vuetify": "^2.6.9", "vuetify-customized-fields": "^1.0.52", "vuetify-datetime-picker": "^2.1.1", "vuetify-toast-snackbar-ng": "^0.7.5", "vuex-shared-mutations": "^1.0.2", "webpack": "^4.46.0"}, "devDependencies": {"@babel/eslint-parser": "^7.16.3", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@faker-js/faker": "^7.4.0", "@nuxt/image": "^0.7.1", "@nuxtjs/eslint-config": "^8.0.0", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/router": "^1.7.0", "@nuxtjs/router-extras": "^1.1.1", "@nuxtjs/vuetify": "^1.12.3", "eslint": "^8.4.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-nuxt": "^3.1.0", "eslint-plugin-vue": "^8.2.0", "postcss-html": "^1.3.0", "prettier": "^2.5.1", "stylelint": "^15.7.0", "stylelint-config-recommended-vue": "1.4.0", "stylelint-config-standard": "^33.0.0"}}