<template>
	<page :title="$t('title.ai')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-ai") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/ai">
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.aiId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.aiId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.ai')"
				api="/v1/admin/ai"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<vc-text-field v-model="item.key" :label="$t('common.key')" :rules="[$rules.required($t('common.key'))]" />
				<vc-textarea
					v-model="item.prompt"
					:label="$t('common.prompt')"
					counter
					rows="6"
					:rules="[/*$rules.maxLength('prompt', 400),*/ $rules.required($t('common.prompt'))]"
				/>
				<vc-combobox
					v-model="item.config.model"
					:items="aiModels"
					:label="$t('common.model')"
					:rules="[$rules.required($t('common.model'))]"
				/>
				<v-alert type="info" border="left" text dense>
					You can learn more about the models
					<a style="color: inherit" href="https://platform.openai.com/docs/models/overview" target="_blank">here</a>
				</v-alert>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "aiId",
				},
				{
					text: this.$t("common.key"),
					sortable: true,
					value: "key",
				},
				{
					text: this.$t("common.prompt"),
					sortable: true,
					value: "prompt",
				},

				{
					text: this.$t("common.model"),
					sortable: true,
					value: "config.model",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				key: null,
				prompt: null,
				config: {},
			},
			aiModels: ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview", "text-embedding-3-small", "dall-e-3", "tts-1", "whisper-1"],
		}
	},

	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
	},
}
</script>
