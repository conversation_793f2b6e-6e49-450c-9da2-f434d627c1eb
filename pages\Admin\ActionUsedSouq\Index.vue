<template>
	<page :tabs="tabs" :title="$t('title.action-used-souq')">
		<template #actions>
			<div id="action" />
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	name: "ActionUsedSouqIndex",
	data() {
		return {
			tabs: [
				{
					text: this.$t("title.used-products"),
					icon: "mdi-message-text",
					to: this.localePath({
						name: "used-products",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("title.used-categories"),
					icon: "mdi-message-text",
					to: this.localePath({
						name: "used-categories",
						params: {
							type: "",
						},
					}),
				},
			],
		}
	},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new()
		},
	},
}
</script>
