<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-used-category") }}</span>
			</v-btn>
		</teleport>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/used-categories">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->

				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.media="{ item }">
					<thumbnail :width="80" :aspect-ratio="4 / 3" :src="item.media.cover?.preview" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.usedCategoryId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.usedCategoryId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.used-category')"
				:get-item-map="mapCategories"
				api="/v1/admin/used-categories?trans=off"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<vc-select v-model="item.parentId" :label="$t('common.parent-used-category')" api="/v1/lookups/used-categories" />
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field
						:label="$t('common.children-name')"
						:rules="[$rules.required($t('common.name'))]"
						:value="value"
						:dir="dir"
						@input="update"
						v-on="on"
					/>
				</translatable>

				<vc-autocomplete
					v-model="item.usedCategoriesAttributes"
					multiple
					:label="$t('common.attribute')"
					:rules="[$rules.required($t('common.categories-attributes'))]"
					api="/v1/lookups/attributes"
				/>
				<vc-autocomplete
					v-model="item.usedCategoriesBrands"
					multiple
					:label="$t('common.brand')"
					:rules="[$rules.required($t('common.categories-brands'))]"
					api="/v1/lookups/brands"
				/>

				<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaTitle">
					<vc-text-field
						:value="value"
						:label="$t('common.meta-title')"
						counter
						:dir="dir"
						:rules="[$rules.maxLength('Meta Title', 70)]"
						v-on="on"
						@input="update"
					/>
				</Translatable>
				<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaDescription">
					<vc-textarea
						:value="value"
						:label="$t('common.meta-description')"
						counter
						rows="6"
						:rules="[$rules.maxLength('Meta Description', 175)]"
						:dir="dir"
						v-on="on"
						@input="update"
					/>
				</Translatable>

				<upload v-model="item.media.cover" :max="1" width="340" />
			</crud>
		</div>
	</div>
</template>

<script>
export default {
	name: "UsedCategoriesIndex",
	data() {
		return {
			columns: [
				{
					text: "#",
					sortable: true,
					value: "usedCategoryId",
				},
				{
					text: this.$t("common.media"),
					sortable: true,
					value: "media",
				},
				{
					text: this.$t("common.parent-used-category"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
				metaTitle: {},
				metaDescription: {},
				parentId: null,
				media: {
					cover: {},
				},
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
		mapCategories(item) {
			console.log(
				"usedCategoriesAttributes",
				item.usedCategoriesAttributes.map(x => x.attributeId),
			)
			return {
				...item,
				usedCategoriesAttributes: item.usedCategoriesAttributes.map(x => x.attributeId),
				usedCategoriesBrands: item.usedCategoriesBrands.map(x => x.brandId),
			}
		},
	},
}
</script>
