<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-used-product") }}</span>
			</v-btn>
		</teleport>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/used-products">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->
				<template #filter="{ models, options }">
					<vc-select v-model="models.brandId" :items="options.brandId" label="Brand" />
				</template>

				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.publishedAt="{ item }">
					{{ item.publishedAt | dateTime }}
				</template>
				<template #item.isPublished="{ item }">
					<status :items="isPublishedItems" :value="item.isPublished" />
				</template>

				<template #item.price="{ item }">
					{{ (item.price?.price || item.price?.currency) | money }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.usedProductId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.usedProductId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
						<v-btn
							v-if="item.isPublished === false"
							v-tooltip="$t('common.active')"
							class="mx-4"
							color="success"
							small
							outlined
							:loading="itemLoading === item.usedProductId"
							@click="setActive(item.usedProductId)"
						>
							{{ $t("common.set-active") }}
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="1200"
				:default="defaultItem"
				:get-item-map="mapCategories"
				:item-name="$t('common.used-product')"
				api="/v1/admin/used-products?trans=off"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<v-row>
					<v-col cols="5">
						<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
							<vc-text-field
								:label="$t('common.name')"
								:rules="[$rules.required($t('common.name'))]"
								:value="value"
								:dir="dir"
								@input="update"
								v-on="on"
							/>
						</translatable>
						<used-categories v-model="item.usedCategories" />
						<vc-select
							:key="item.usedCategories.join(',')"
							v-model="item.brandId"
							:label="$t('common.brand')"
							:rules="[$rules.required($t('common.brands'))]"
							:api="'/v1/lookups/brands?used-categories=' + item.usedCategories.join(',')"
							:disabled="!item.usedCategories.length"
						/>
						<vc-autocomplete
							v-model="item.cityId"
							:label="$t('common.city')"
							:rules="[$rules.required($t('common.cities'))]"
							api="/v1/lookups/cities"
						/>
						<vc-autocomplete
							v-model="item.userId"
							:label="$t('common.user')"
							:rules="[$rules.required($t('common.users'))]"
							api="/v1/lookups/users"
						/>
						<used-product-attributes v-model="item.attributes" :categories="item.usedCategories" />
						<money-field v-model="item.price" :label="$t('common.price')" />
					</v-col>
					<v-col cols="7">
						<Translatable v-slot="{ on, dir, update, value }" v-model="item.description">
							<vc-wysiwyg
								:label="$t('common.description')"
								:value="value"
								:rows="20"
								:dir="dir"
								@input="update"
								v-on="on"
							/>
						</Translatable>
						<publish-field
							v-model="item.isPublished"
							:published-at.sync="item.publishedAt"
							:expires-at.sync="item.unPublishedAt"
						/>
						<upload v-model="item.media.gallery" :max="5" width="340" />
					</v-col>
				</v-row>
			</crud>
		</div>
	</div>
</template>

<script>
import { isPublished } from "~/config/Enum"

export default {
	name: "UsedProductsIndex",
	data() {
		return {
			value: {},
			itemLoading: null,
			columns: [
				{
					text: "#",
					sortable: true,
					value: "usedProductId",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.published"),
					sortable: true,
					value: "isPublished",
				},
				{
					text: this.$t("common.price"),
					sortable: true,
					value: "price",
				},
				{
					text: this.$t("common.published-at"),
					sortable: false,
					value: "publishedAt",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],

			defaultItem: {
				name: {},
				usedCategories: [],
				attributes: [],
				description: {},
				userId: null,
				media: {
					gallery: [],
				},
				price: {
					value: "",
					basePrice: "",
					currencyId: "",
				},
				brandId: null,
				isPublished: false,
				cityId: "",
				publishedAt: null,
				unPublishedAt: null,
			},
		}
	},
	computed: {
		isPublishedItems() {
			return isPublished.map(published => ({
				text: this.$t(published.key),
				value: published.value,
			}))
		},
	},

	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
		mapCategories(item) {
			const attributes = item.attributes.map((x) => {
				return {
					attributeId: x.attributeId,
					attributeOptionId: x.attributesValuesUsed.map(att => att.attributeOptionId),
				}
			})
			return {
				...item,
				usedCategories: item.usedCategories.map(x => x.usedCategoryId),
				attributes,
			}
		},

		setActive(usedProductId) {
			this.itemLoading = usedProductId
			this.$axios
				.$put("/v1/admin/used-products/" + usedProductId, {
					isPublished: "1",
				})
				.then(() => {
					this.$refs.dataTable.refresh()
				})
				.finally(() => {
					this.itemLoading = null
				})
		},
	},
}
</script>
