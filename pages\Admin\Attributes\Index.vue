<template>
	<page :title="$t('title.attributes')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon>
				<span class="text-capitalize">{{ $t("common.new-attribute") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/attributes">
				<template #filter="{ models }">
					<vc-select v-model="models.type" :label="$t('common.type')" :items="attributesTypeItems" clearable />
				</template>

				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.isRequired="{ item }">
					<check-mark :value="item.isRequired" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.attributeId)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.attributeId)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
		</div>

		<crud ref="crud" v-slot="{ item }" width="500" :default="defaultItem" :item-name="$t('common.attribute')"
			api="/v1/admin/attributes?trans=off" @updated="refreshAndClearCache" @created="refreshAndClearCache"
			@deleted="refreshAndClearCache">
			<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
				<vc-text-field :label="$t('common.name')" :rules="[$rules.required($t('common.name'))]" :value="value"
					:dir="dir" @input="update" v-on="on" />
			</translatable>

			<vc-text-field v-model="item.key" :rules="[$rules.required($t('common.key'))]" :label="$t('common.key')" />

			<translatable v-slot="{ value, update, on, dir }" v-model="item.prefix">
				<vc-text-field :label="$t('common.prefix')" :value="value" :dir="dir" @input="update" v-on="on" />
			</translatable>

			<translatable v-slot="{ value, update, on, dir }" v-model="item.suffix">
				<vc-text-field :label="$t('common.suffix')" :value="value" :dir="dir" @input="update" v-on="on" />
			</translatable>

			<vc-checkbox v-model="item.isRequired" :label="$t('common.is-required')" />

			<vc-select v-model="item.type" :rules="[$rules.required($t('common.type'))]" :items="attributesTypeItems"
				:label="$t('common.type-option')" />

			<v-list dense>
				<v-subheader class="d-flex align-center">
					{{ $t("common.options-list") }}
					<v-spacer />
					<div class="d-flex my-4 justify-end">
						<v-btn class="text-lowercase" color="primary" :disabled="!item.type"
							@click="insertOption(item.options)">
							<v-icon left>
								mdi-plus
							</v-icon>
							<span class="text-capitalize">{{ $t("common.add-new-option") }}</span>
						</v-btn>
					</div>
				</v-subheader>

				<v-list-item v-for="(option, index) in item.options" :key="index">
					<v-list-item-content>
						<component :is="`attributes-insert-${item.type}-option`" v-model="item.options[index]"
							:label="$t('common.option')" @input="setOptionSlug(index)" />
					</v-list-item-content>

					<v-list-item-action>
						<v-btn v-tooltip="$t('common.delete')" small icon @click="removeOption(index)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
					</v-list-item-action>
				</v-list-item>
			</v-list>
		</crud>
	</page>
</template>

<script>
import { attributesType } from "~/config/Enum"

export default {
	data() {
		return {
			isDeleting: false,
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "attributeId",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.prefix"),
					sortable: true,
					value: "prefix",
				},

				{
					text: this.$t("common.suffix"),
					sortable: true,
					value: "suffix",
				},
				{
					text: this.$t("common.key"),
					sortable: true,
					value: "key",
				},
				{
					text: this.$t("common.is-required"),
					sortable: true,
					value: "isRequired",
				},
				{
					text: this.$t("common.type"),
					sortable: true,
					value: "type",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},
				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},
				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],

			defaultItem: {
				name: {},
				prefix: {},
				suffix: {},
				type: null,
				isRequired: false,
				key: null,
				options: [],
			},
		}
	},

	computed: {
		attributesTypeItems() {
			return attributesType.map(option => ({
				text: this.$t(option.key),
				value: option.value,
			}))
		},
	},
	watch: {},
	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/attributes")
		},
		insertOption(options) {
			options.push({
				name: {},
				slug: '',
				number: "",
				hexCode: "",
			})
		},
		removeOption(index) {
			this.$confirm(this.$t("messages.confirm-delete-text"), {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					this.isDeleting = true
					this.$refs.crud.item.options.splice(index, 1)
				}
			})
		},
		setOptionSlug(index) {
			console.log("🚀 ~ file: Index.vue:221 ~ setOptionSlug ~ index:", index)

			const option = this.$refs.crud.item.options[index]
			const slug = this.toSlug(
				option.prefix +
				"-" +
				this.$options.filters.attrValue(option, option.type) +
				"-" +
				option.suffix +
				"-" +
				this.$refs.crud.item.name.en,
			)
			this.$set(option, "slug", slug)
		},
	},
}
</script>
