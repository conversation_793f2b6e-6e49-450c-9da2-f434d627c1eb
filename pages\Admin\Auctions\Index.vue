<template>
	<page :title="$t('title.auctions')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-auction") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/auctions">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->

				<template #item.endTime="{ item }">
					{{ item.endTime | dateTime }}
				</template>

				<template #item.startTime="{ item }">
					{{ item.startTime | dateTime }}
				</template>

				<template #item.offDisplayTime="{ item }">
					{{ item.offDisplayTime | dateTime }}
				</template>

				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.auctionId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.auctionId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.auction')"
				api="/v1/admin/auctions?trans=off"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<vc-text-field
					v-model="item.bidStart"
					:label="$t('common.bid-start')"
					:rules="[$rules.required($t('common.bid-start'))]"
				/>
				<vc-date-time
					v-model="item.startTime"
					:text-field-props="{ clearable: true, outlined: true, dense: true }"
					append-icon="mdi-calendar"
					:label="$t('common.start-time')"
					:rules="[$rules.required($t('common.start-time'))]"
				/>
				<vc-date-time
					v-model="item.endTime"
					:text-field-props="{ clearable: true, outlined: true, dense: true }"
					append-icon="mdi-calendar"
					:label="$t('common.end-time')"
					:rules="[$rules.required($t('common.end-time'))]"
				/>
				<vc-date-time
					v-model="item.offDisplayTime"
					:text-field-props="{ clearable: true, outlined: true, dense: true }"
					append-icon="mdi-calendar"
					:label="$t('common.off-display-time')"
					:rules="[$rules.required($t('common.off-display-time'))]"
				/>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					sortable: true,
					value: "auctionId",
				},

				{
					text: this.$t("common.bid-start"),
					sortable: true,
					value: "bidStart",
				},

				{
					text: this.$t("common.start-time"),
					sortable: true,
					value: "startTime",
				},
				{
					text: this.$t("common.end-time"),
					sortable: true,
					value: "endTime",
				},
				{
					text: this.$t("common.off-display-time"),
					sortable: true,
					value: "offDisplayTime",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				bidStart: null,
				startTime: null,
				endTime: null,
				offDisplayTime: null,
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
	},
}
</script>
