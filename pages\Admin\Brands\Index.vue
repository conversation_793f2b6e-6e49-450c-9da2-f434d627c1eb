<template>
	<page :title="$t('title.brands')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon>
				<span class="text-capitalize">{{ $t("common.new-brand") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/brands">
				<!-- <template #prepend-action="{ models, filter }">

                </template> -->
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>
				<template #item.publishedAt="{ item }">
					{{ item.publishedAt | dateTime }}
				</template>
				<template #item.isPublished="{ item }">
					<status :items="isPublishedItems" :value="item.isPublished" />
				</template>

				<template #item.media="{ item }">
					<thumbnail :width="80" :aspect-ratio="4 / 3" :src="item.media.logo?.preview" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.brandId)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.brandId)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud ref="crud" v-slot="{ item }" width="500" :default="defaultItem" :item-name="$t('common.brand')"
				api="/v1/admin/brands?trans=off" @updated="refreshAndClearCache" @created="refreshAndClearCache"
				@deleted="refreshAndClearCache">
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field :label="$t('common.brand')" :rules="[$rules.required($t('common.name'))]" :value="value"
						:dir="dir" @input="update" v-on="on" />
				</translatable>
				<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaTitle">
					<vc-text-field :value="value" :label="$t('common.meta-title')" counter :dir="dir" v-on="on" @input="update" />
				</Translatable>
				<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaDescription">
					<vc-textarea :value="value" :label="$t('common.meta-description')" counter rows="6" :dir="dir" v-on="on" @input="update" />
				</Translatable>
				<lookups-labels v-model="item.labels" :multiple="true" />
				<v-switch v-model="item.inHomePage" :label="`Appear on in home page? ${item.inHomePage ? 'Yes' : 'No'}`" />
				<!-- <publish-field v-model="item.isPublished" :published-at.sync="item.publishedAt" :expires-at.sync="item.unPublishedAt" /> -->
				<upload v-model="item.media.logo" label="Logo (1:1)" :rules="[$rules.required($t('common.media'))]" :max="1"
					width="340" />
				<upload v-model="item.media.logoName" label="Logo With name (25:9)"
					:rules="[$rules.required($t('common.media'))]" :max="1" :aspect-ratio="25 / 9" width="340" />
			</crud>
		</div>
	</page>
</template>

<script>
import { isPublished } from "~/config/Enum"
export default {
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "brandId",
				},
				{
					text: this.$t("common.media"),
					sortable: true,
					value: "media",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.published"),
					sortable: true,
					value: "isPublished",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
				publishedAt: null,
				unPublishedAt: null,
				isPublished: false,
				inHomePage: false,
				media: {
					logo: {},
				},
				metaTitle: {
					ar: '{name}',
					en: '{name}',
				},
				metaDescription: {
					ar: 'اكتشف جميع منتجات {name} الأصلية في الأردن من خلال أكشن موبايل ✔️ أفضل الأسعار، جودة مضمونة، وخدمات مميزة ✔️ تسوق الآن واحصل على عروض حصرية!',
					en: 'Discover all {name} products in Jordan at Action Mobile ✔️ Explore a wide range of authentic {Brand-Name} devices with competitive prices and excellent service ✔️ Shop now!',
				},
				labels: [],
			},
		}
	},

	computed: {
		isPublishedItems() {
			return isPublished.map(published => ({
				text: this.$t(published.key),
				value: published.value,
			}))
		},
	},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			//	Added the `/` to the route
			this.$store.dispatch("cache/clearKey", "/v1/lookups/brands")
		},
	},
}
</script>
