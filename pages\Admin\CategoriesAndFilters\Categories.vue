<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon>
				<span class="text-capitalize">{{ $t("common.new-category") }}</span>
			</v-btn>
		</teleport>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/categories">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->
				<template #filter="{ models }">
					<vc-select v-model="models.categoriesBrands" api="/v1/lookups/brands" label="Brand" />
				</template>
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.media="{ item }">
					<thumbnail :width="80" :aspect-ratio="4 / 3" :src="item.media.cover?.preview" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.categoryId)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.categoryId)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>

			<crud ref="crud" v-slot="{ item }" :get-item-map="mapCategoriesAttributesAndBrands" width="500"
				:default="defaultItem" :item-name="$t('common.category')" api="/v1/admin/categories?trans=off"
				@updated="refreshAndClearCache" @created="refreshAndClearCache" @deleted="refreshAndClearCache">
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field :label="$t('common.category')" :rules="[$rules.required($t('common.category'))]"
						:value="value" :dir="dir" @input="update" v-on="on" />
				</translatable>
				<!-- <vc-select v-model="item.parentId" :label="$t('common.parent-category')" api="/v1/lookups/categories"></vc-select> -->
				<categories-modal v-model.number="item.parentId" />
				<lookups-attributes v-model="item.categoriesAttributes" />
				<lookups-brands v-model="item.categoriesBrands" />
				<vc-select v-model="item.filtersGroupsId" :label="$t('common.filters-groups')"
					api="/v1/lookups/filter-group" />
				<upload v-model="item.media.cover" label="Category Image" :rules="[$rules.required($t('common.media'))]"
					:max="1" width="100" />
				<vc-text-field v-model.number="item.sort" type="number" min="1" label="sort" />
				<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaTitle">
					<vc-text-field :value="value" :label="$t('common.meta-title')" counter :dir="dir" v-on="on" @input="update" />
				</Translatable>
				<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaDescription">
					<vc-textarea :value="value" :label="$t('common.meta-description')" counter rows="6"  :dir="dir" v-on="on" @input="update" />
				</Translatable>

				<v-slider v-model="item.priority" label="Priority" min="0" max="10" tick-labels thumb-label="always">
					<template #thumb-label="props">
						<span class="text-subtitle-2">
							{{ props.value }}
						</span>
					</template>
				</v-slider>
				<v-switch v-model="item.isShowBrandIngListing"
					:label="`are you show brand in this category? ${item.isShowBrandIngListing ? 'Yes' : 'No'}`" />
			</crud>
		</div>
	</div>
</template>

<script>
export default {
	name: "CategoriesIndex",
	data() {
		return {
			columns: [
				{
					text: "#",
					sortable: true,
					value: "categoryId",
				},
				{
					text: this.$t("common.media"),
					sortable: true,
					value: "media",
				},

				{
					text: this.$t("common.category"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.parent-category"),
					sortable: true,
					value: "parent.name",
				},

				{
					text: this.$t("common.sort"),
					sortable: true,
					value: "sort",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
				parentId: null,
				attributeId: [],
				filtersGroupsId: null,
				metaTitle: {},
				metaDescription: {},
				sort: 0,
				media: {
					cover: {},
				},
				priority: 0,
				isShowBrandIngListing: false,
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		mapCategoriesAttributesAndBrands(item) {
			return {
				...item,
				categoriesAttributes: item.attributes.map(x => x.attributeId),
				categoriesBrands: item.brands.map(x => x.brandId),
			}
		},
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/categories")
		},
	},
}
</script>
