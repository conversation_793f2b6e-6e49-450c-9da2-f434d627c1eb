<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-filter") }}</span>
			</v-btn>
		</teleport>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/group-filters">
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.filtersGroupsId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.filtersGroupsId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="700"
				:default="defaultItem"
				:item-name="$t('common.filter')"
				api="/v1/admin/group-filters?trans=off"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field
						:label="$t('common.name')"
						:rules="[$rules.required($t('common.name'))]"
						:value="value"
						:dir="dir"
						@input="update"
						v-on="on"
					/>
				</translatable>

				<v-list>
					<v-subheader class="d-flex align-center">
						{{ $t("common.filters") }}
						<v-spacer />
						<v-btn color="primary" text @click="$refs.filter.new()">
							<v-icon left>
mdi-plus
</v-icon>
							<span class="text-capitalize"> {{ $t("common.new-child-filter") }}</span>
						</v-btn>
					</v-subheader>

					<draggable v-model="item.filters">
						<v-list-item v-for="(filter, i) in item.filters" :key="i" link>
							<v-list-item-content>
								<v-list-item-title>{{ filter.filterType }} </v-list-item-title>
								<v-list-item-subtitle>{{ filter.componentType }} </v-list-item-subtitle>
							</v-list-item-content>
							<v-list-item-action class="flex-row">
								<v-btn icon small @click="removeFilter(i)">
									<v-icon small>
mdi-delete
</v-icon>
								</v-btn>
								<v-btn icon small @click="editFilter(filter, i)">
									<v-icon small>
mdi-pencil
</v-icon>
								</v-btn>
								<v-btn icon small class="handle">
									<v-icon small>
mdi-drag
</v-icon>
								</v-btn>
							</v-list-item-action>
						</v-list-item>
					</draggable>
				</v-list>
			</crud>
			<div>
				<crud
					ref="filter"
					v-slot="{ item }"
					width="500"
					:default="defaultFilter"
					:item-name="$t('common.filter')"
					:post-api="false"
					:put-api="false"
					@created="addToGroup"
					@updated="saveEditFilter"
				>
					<vc-select v-model="item.filterType" :label="$t('common.filter-type')" :items="filterTypesItems" />

					<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
						<vc-text-field
							:label="$t('common.name')"
							:rules="[$rules.required($t('common.name'))]"
							:value="value"
							:dir="dir"
							@input="update"
							v-on="on"
						/>
					</translatable>

					<component
						:is="`filter-type-${item.filterType}`"
						v-if="item.filterType"
						:key="item.filterType"
						:item="item"
						@update="updateChildObject"
					/>

					<!-- <div v-if="item.filterType === 'brand'">
						<vc-select
							v-model="item.componentType"
							:label="$t('common.component-type')"
							:items="componentTypesItems"
						></vc-select>
					</div>
					<div v-if="item.filterType === 'price'">
						<vc-text-field v-model="item.config.max" type="number" :label="$t('common.max')"></vc-text-field>
						<vc-text-field v-model="item.config.min" type="number" :label="$t('common.min')"></vc-text-field>
						<vc-select
							v-model="item.componentType"
							:label="$t('common.component-type')"
							:items="componentTypesItems"
						></vc-select>
					</div>

					<div v-if="item.filterType === 'attribute'">
						<vc-select v-model="item.attributeId" :label="$t('common.attribute')" api="/v1/lookups/attributes"></vc-select>
						<vc-select
							v-model="item.componentType"
							:label="$t('common.component-type')"
							:items="componentTypesItems"
						></vc-select>
					</div> -->
				</crud>
			</div>
		</div>
	</div>
</template>

<script>
import { filterTypes, componentTypes } from "~/config/Enum"
export default {
	name: "FiltersIndex",
	data() {
		return {
			isDeleting: false,
			columns: [
				{
					text: "#",
					sortable: true,
					value: "filtersGroupsId",
				},

				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
				filters: [],
			},
			defaultFilter: {
				name: {},
				filterType: "",
				filtersGroupsId: "",
				componentType: null,
				config: {
					min: null,
					max: null,
				},
			},
			editedFilterIndex: null,
		}
	},

	computed: {
		filterTypesItems() {
			return filterTypes.map(filterType => ({
				text: this.$t(filterType.key),
				value: filterType.value,
			}))
		},

		componentTypesItems() {
			return componentTypes.map(componentType => ({
				text: this.$t(componentType.key),
				value: componentType.value,
			}))
		},
	},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/group-filters")
		},

		get(item) {
			return {
				...item,
				componentType: item.componentType,
			}
		},
		updateChildObject(newItem) {
			// this.$refs.filter.item = {
			// 	...this.$refs.filter.item,
			// 	...newItem,
			// };
			this.$set(this.$refs.filter, "item", {
				...this.$refs.filter.item,
				...newItem,
			})
		},
		addToGroup(item) {
			this.$refs.filter.close()
			this.$refs.crud.item.filters.push(item)
			// console.log("🚀 ~ file: Index.vue:233 ~ addToGroup ~ this.$refs.crud:", this.$refs.crud);
		},
		editFilter(item, i) {
			this.editedFilterIndex = i
			this.$refs.filter.edit(item)
		},
		saveEditFilter(item) {
			const index = this.editedFilterIndex
			const clonedItem = this.$cloneDeep(item)
			this.$set(this.$refs.crud.item.filters, index, clonedItem)

			this.$refs.filter.close()
		},
		removeFilter(index) {
			this.$confirm(this.$t("messages.confirm-delete-text"), {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					this.isDeleting = true
					this.$refs.crud.item.filters.splice(index, 1)
				}
			})
		},
	},
}
</script>
