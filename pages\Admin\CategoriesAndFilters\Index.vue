<template>
	<page :tabs="tabs" :title="$t('title.categories')">
		<template #actions>
			<div id="action" />
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	name: "CategoriesAndFiltersIndex",
	data() {
		return {
			tabs: [
				{
					text: this.$t("title.categories"),
					icon: "mdi-message-text",
					to: this.localePath({
						name: "categories",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("title.filters"),
					icon: "mdi-message-text",
					to: this.localePath({
						name: "filters",
						params: {
							type: "",
						},
					}),
				},
			],
		}
	},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new()
		},
	},
}
</script>
