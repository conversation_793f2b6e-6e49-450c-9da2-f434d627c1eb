<template>
	<page :title="$t('common.change-password')">
		<v-form ref="password" @submit.prevent="updatePassword">
			<div class="d-flex justify-space-between mt-4 px-4 pb-4 pt-4">
				<v-row>
					<v-col cols="12">
						<vc-password v-model="password.oldPassword" name="password" no-strength
							:rules="[$rules.required($t('common.old-password'))]" :label="$t('common.old-password')" />
					</v-col>
					<v-col cols="12">
						<vc-password v-model="password.password" name="password" no-strength
							:rules="[$rules.required($t('common.password')), $rules.minLength($t('common.password'), 8)]"
							:label="$t('common.password')" />
					</v-col>
					<v-col cols="12">
						<vc-password v-model="password.passwordConfirmation" name="passwordConfirmation" no-strength :rules="[
							$rules.required($t('common.password-confirmation')),
							$rules.minLength($t('common.password-confirmation'), 8),
						]" :label="$t('common.password-confirmation')" />
					</v-col>
				</v-row>
			</div>
			<div>
				<v-row>
					<v-col cols="12">
						<v-btn color="primary" text type="submit" class="float-right ml-5" :loading="loading"
							@click="updatePassword">
{{
								$t("common.save")
							}}
</v-btn>
					</v-col>
				</v-row>
			</div>
		</v-form>

		<nuxt-child ref="child" />
	</page>
</template>
<script>
export default {
	name: "ChangePassword",
	data() {
		return {
			status: null,
			loading: false,
			password: {
				oldPassword: "",
				password: "",
				passwordConfirmation: "",
			},

			isEdit: false,
		}
	},
	methods: {
		updatePassword() {
			if (!this.$refs.password.validate()) {
				this.scrollToError()
				return
			}
			this.loading = true
			this.$axios
				.$put(`v1/my/update-password`, this.password)
				.then(() => {
					this.password = {
						oldPassword: "",
						password: "",
						passwordConfirmation: "",
					}
					this.$toast.success(`${this.$t("common.password-has-been-updated-successfully")}`)
					this.cancel()
					this.isEditingPassword = false
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.password)
				})
				.finally(() => {
					setTimeout(() => {
						this.loading = false
					}, 4000)
				})
		},
	},
}
</script>
