<template>
	<page :title="$t('title.contact-us')" :desc="$t('.desc')">
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/contact-us">
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.status="{ item }">
					<status :items="statusContactUsItems" :value="item.status" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.contactUsId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.contactUsId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				:default="defaultItem"
				width="500"
				:item-name="$t('common.contact-us')"
				api="/v1/admin/contact-us?trans=off"
				@created="refresh"
				@updated="refresh"
				@deleted="refresh"
			>
				<vc-text-field v-model="item.name" :disabled="isDisabled" :label="$t('common.name')" />
				<vc-text-field v-model="item.email" :disabled="isDisabled" :label="$t('common.email')" type="email" />
				<vc-phone v-model="item.phone" outlined :disabled="isDisabled" :label="$t('common.phone')" />
				<vc-text-field v-model="item.problemType" :disabled="isDisabled" :label="$t('common.problem-type')" />
				<vc-textarea v-model="item.message" :disabled="isDisabled" :label="$t('common.message')" counter rows="10" />
				<vc-radio
					v-model="item.status"
					:label="$t('common.status')"
					:items="statusContactUsItems"
					:rules="[$rules.required($t('common.status'))]"
				/>
			</crud>
		</div>
	</page>
</template>

<script>
import { statusContactUs } from "~/config/Enum"
export default {
	name: "CountriesIndex",
	data() {
		return {
			name: "ContactUsIndex",
			value: {},
			isDisabled: true,
			columns: [
				{
					text: "#",
					sortable: true,
					value: "contactUsId",
				},

				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.phone"),
					sortable: true,
					value: "phone.number",
				},
				{
					text: this.$t("common.status"),
					sortable: true,
					value: "status",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				status: null,
				name: null,
				email: null,
				phone: null,
				problemType: null,
				message: null,
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
		statusContactUsItems() {
			return statusContactUs.map(contact => ({
				text: this.$t(contact.key),
				value: contact.value,
			}))
		},
	},
}
</script>
