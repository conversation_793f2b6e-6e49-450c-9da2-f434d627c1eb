<template>
	<page :title="$t('title.product-labels')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon>
				<span class="text-capitalize">{{ $t("common.new-label") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/labels">
				<template #item.name="{ item }">
					{{ item.name | trans }}
				</template>
				<template #item.color="{ item }">
					{{ item.color }}
				</template>
				<template #item.slug="{ item }">
					{{ item.slug }}
				</template>
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>
				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.labelId)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.labelId)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
		</div>

		<crud ref="crud" v-slot="{ item }" width="500" :default="defaultItem" :item-name="$t('common.label')"
			api="/v1/admin/labels?trans=off" @updated="refreshAndClearCache" @created="refreshAndClearCache"
			@deleted="refreshAndClearCache">
			<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
				<vc-text-field :label="$t('common.name')" :rules="[$rules.required($t('common.name'))]" :value="value"
					:dir="dir" @input="update" v-on="on" />
			</translatable>
			<vc-text-field v-model="item.color" :rules="[$rules.required($t('common.color'))]"
				:label="$t('common.color')" />
		</crud>
	</page>
</template>

<script>

export default {
	data() {
		return {
			isDeleting: false,
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "labelId",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.color"),
					sortable: true,
					value: "color",
				},
				{
					text: this.$t("common.slug"),
					sortable: true,
					value: "slug",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},
				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},
				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],

			defaultItem: {
				name: {},
				color: null,
			},
		}
	},

	watch: {},
	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/labels")
		},
	},
}
</script>
