<template>
	<page :tabs="tabs" :title="$t('title.orders')">
		<template #actions>
			<div id="action" />
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	name: "OrdersIndex",
	data() {
		return {
			tabs: [
				{
					text: this.$t("order.received"),
					value: "received",
					to: this.localePath({
						name: "orders",
						params: {
							status: "received",
						},
					}),
				},
				{
					text: this.$t("common.for-refund"),
					value: "forRefund",
					to: this.localePath({
						name: "orders",
						params: {
							status: "forRefund",
						},
					}),
				},
				{
					text: this.$t("order.editing"),
					value: "editing",
					to: this.localePath({
						name: "orders",
						params: {
							status: "editing",
						},
					}),
				},
				{
					text: this.$t("common.processing"),
					value: "processing",
					to: this.localePath({
						name: "orders",
						params: {
							status: "processing",
						},
					}),
				},
				{
					text: this.$t("common.refunded"),
					value: "refunded",
					to: this.localePath({
						name: "orders",
						params: {
							status: "refunded",
						},
					}),
				},
				{
					text: this.$t("common.completed"),
					value: "completed",
					to: this.localePath({
						name: "orders",
						params: {
							status: "completed",
						},
					}),
				},
				{
					text: this.$t("common.canceled"),
					value: "canceled",
					to: this.localePath({
						name: "orders",
						params: {
							status: "canceled",
						},
					}),
				},
				{
					text: this.$t("common.draft"),
					value: "draft",
					to: this.localePath({
						name: "orders",
						params: {
							status: "draft",
						},
					}),
				},
				{
					text: this.$t("common.all"),
					to: this.localePath({
						name: "orders",
					}),
				},
			],
		}
	},
	computed: {},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new()
		},
	},
}
</script>
