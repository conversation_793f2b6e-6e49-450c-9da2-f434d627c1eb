<template>
	<div>
		<!-- <template #actions>
        <v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
            <v-icon left>mdi-plus</v-icon>
            <span class="text-capitalize">{{ $t("common.new-order") }}</span>
        </v-btn>
    </template> -->
		<div>
			<data-table ref="dataTable" :single-expand="true" item-key="orderId" :columns="columns"
				:api="`/v1/admin/orders${$route.params.status ? '?status=' + $route.params.status : ''}`">
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>
				<template #item.amount="{ item }">
					<span class="price--text">{{ item.total | money }}</span>
				</template>
				<template #item.payment="{ item }">
					<v-img v-if="item.paymentMethod?.media?.logo?.src" :src="item.paymentMethod.media.logo?.src"
						width="64" />
				</template>
				<template #item.paymentStatus="{ item }">
					<invoice-payment-status :value="item.paymentStatus" />
				</template>
				<template #item.address="{ item }">
					<div>{{ item.address?.recipientName }}</div>
					<div>{{ item.address?.city?.name }}</div>
					<div>{{ item.address?.street }}</div>
				</template>
				<template #item.user="{ item }">
					<div v-if="item.userId">
						<v-list-item>
							<v-list-item-avatar>
								<v-avatar :size="40">
									<v-img contain :src="item.user.media?.avatar?.src || '/images/default-avatar.jpg'" />
								</v-avatar>
							</v-list-item-avatar>
							<v-list-item-content>
								<v-list-item-title> {{ item.user.firstName }} {{ item.user.lastName }} </v-list-item-title>
								<v-list-item-subtitle>
									<a class="grey--text" :href="`tel:${$options.filters.phone(item.user.phone)}`">{{
										item.user.phone | phone
									}}</a>
								</v-list-item-subtitle>
							</v-list-item-content>
						</v-list-item>
					</div>
					<div v-else>
						<v-list-item>
							<v-list-item-avatar>
								<v-avatar :size="40">
									<v-img contain :src="'/images/default-avatar.jpg'" />
								</v-avatar>
							</v-list-item-avatar>
							<v-list-item-content>
								<v-list-item-title> Anonymous Visitor </v-list-item-title>
								<v-list-item-subtitle> #{{ item.visitorId }} </v-list-item-subtitle>
							</v-list-item-content>
						</v-list-item>
					</div>
				</template>

				<template #item.status="{ item }">
					<status :items="orderStatuses" :value="item.status" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.orderId)">
							<v-icon small>
								mdi-eye
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
		</div>

		<order-crud ref="crud" @closed="refresh" />
	</div>
</template>

<script>
import { orderStatuses } from "~/config/Enum"

export default {
	data() {
		return {
			productTabsModel: "info",
			value: {},
			currentItem: "Text",
			orderStatuses,
			columns: [
				// { text: "", value: "data-table-expand" },

				{
					text: "#",
					sortable: true,
					value: "orderId",
				},
				{
					text: "",
					sortable: false,
					value: "actions",
				},
				{
					text: this.$t("common.user"),
					sortable: true,
					value: "user",
				},
				{
					text: this.$t("common.amount"),
					sortable: true,
					value: "amount",
				},
				{
					text: this.$t("order.payment"),
					sortable: true,
					value: "paymentMethod.module",
				},
				{
					text: this.$t("common.payment-status"),
					sortable: true,
					value: "paymentStatus",
				},
				{
					text: this.$t("common.address"),
					sortable: true,
					value: "address",
				},
				// {
				// 	text: this.$t("order.shipping-status"),
				// 	sortable: true,
				// 	value: "shippingStatus",
				// },
				{
					text: this.$t("common.status"),
					sortable: true,
					value: "status",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},
				// {
				// 	text: this.$t("common.updated-at"),
				// 	sortable: false,
				// 	value: "updatedAt",
				// },
			],
		}
	},

	computed: {},
	watch: {
		"$route.params.status": {
			handler() {
				this.$nextTick(() => {
					this.refresh()
				})
			},
		},
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
		insertOrderItem(orderItems) {
			orderItems.push({
				snapshot: {},
				price: "",
				originalPrice: "",
				discount: "",
				tax: "",
				status: "",
				quantity: "",
			})
		},
	},
}
</script>
