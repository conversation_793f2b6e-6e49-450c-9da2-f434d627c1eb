<template>
	<page :title="$t('title.pages')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-page") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/pages">
				<!-- <template #prepend-action="{ models, filter }">

                    </template> -->
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.pageId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.pageId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="1000"
				:default="defaultItem"
				:item-name="$t('common.page')"
				api="/v1/admin/pages?trans=off"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<v-row>
					<v-col cols="7">
						<Translatable v-slot="{ update, value, on, dir }" v-model="item.body">
							<vc-wysiwyg
								rows="20"
								label="body"
								min-height="800"
								:value="value"
								:rules="[$rules.required($t('common.body'))]"
								class="page-body-wysiwyg"
								:dir="dir"
								@input="update"
								v-on="on"
							/>
						</Translatable>
						<!-- Old Body input -->
						<!-- <Translatable v-slot="{ update, value, on, dir }" v-model="item.body">
							<vc-textarea
								:value="value"
								label="Body"
								counter
								rows="20"
								:rules="[$rules.required($t('common.body'))]"
								:dir="dir"
								v-on="on"
								@input="update"
							></vc-textarea>
						</Translatable> -->
					</v-col>
					<v-col cols="5">
						<Translatable v-slot="{ value, update, on, dir }" v-model="item.name">
							<vc-text-field
								:label="$t('common.page')"
								:rules="[$rules.required($t('common.name'))]"
								:value="value"
								:dir="dir"
								@input="update"
								v-on="on"
							/>
						</Translatable>
						<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaTitle">
							<vc-text-field
								:value="value"
								label="Meta Title"
								counter
								:dir="dir"
								:rules="[$rules.maxLength('Meta Title', 70)]"
								v-on="on"
								@input="update"
							/>
						</Translatable>
						<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaDescription">
							<vc-textarea
								:value="value"
								label="Meta Description"
								counter
								rows="6"
								:rules="[$rules.maxLength('Meta Description', 175)]"
								:dir="dir"
								v-on="on"
								@input="update"
							/>
						</Translatable>
						<Translatable v-slot="{ update, value, on, dir }" v-model="item.source">
							<vc-textarea
								:value="value"
								label="Source"
								counter
								:rules="[$rules.maxLength('Source', 60)]"
								:dir="dir"
								v-on="on"
								@input="update"
							/>
						</Translatable>
					</v-col>
				</v-row>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "pageId",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
	},
}
</script>
