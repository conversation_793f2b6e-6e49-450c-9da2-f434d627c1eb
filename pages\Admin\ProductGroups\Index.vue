<template>
	<page :title="$t('title.product-group')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize"> {{ $t("common.new-product-group") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/groups">
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.productGroupId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.productGroupId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.product-group')"
				api="/v1/admin/groups?trans=off"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field
						:label="$t('common.name')"
						:rules="[$rules.required($t('common.name'))]"
						:dir="dir"
						:value="value"
						@input="update"
						v-on="on"
					/>
				</translatable>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	name: "ProductGroupsIndex",
	data() {
		return {
			columns: [
				{
					text: "#",
					sortable: true,
					value: "productGroupId",
				},

				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
	},
}
</script>
