<template>
	<page :title="$t('product.title')" :desc="$t('product.desc')">
		<template #actions>
			<v-btn color="primary" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon>{{ $t("product.new-product")
				}}
			</v-btn>

			<v-btn color="primary" :disabled="!isEnabledFlush" @click="flush">
				<v-icon left>
					mdi-rotate-3d-variant
				</v-icon>{{ $t("product.flush")
				}}
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/products">
				<!-- <template #prepend-action="{ models, filter }">

                </template> -->
				<template #filter="{ models }">
					<vc-text-field v-model="models.id" label="Product ID" type="number" hide-spin-buttons />
				</template>
				<template #item.media="{ item }">
					<thumbnail
						:src="item?.media?.cover?.[0]?.preview || item?.media?.gallery?.[0]?.preview || '/images/product-placeholder.webp'" />
				</template>
				<template #item.name="{ item }">
					{{ item.name | trans }}
					<a :href="localePath({
						name: 'product',
						params: {
							productSlug: item.slug,
						},
					})
						" target="_blank">
						<v-icon small left>mdi-open-in-new</v-icon>
					</a>
				</template>
				<template #item.categories="{ item }">
					<ul>
						<li v-for="category in item.categories" :key="category.categoryId">
							{{ category.name }}
						</li>
					</ul>
				</template>

				<template #item.updatedAt="{ item }">
					Updated At:{{ item.updatedAt | dateTime }}
					<pre />
					Created At:{{ item.createdAt | dateTime }}
				</template>

				<template #item.isPublished="{ item }">
					<check-mark :value="item.isPublished" />
				</template>

				<template #item.isListed="{ item }">
					<check-mark :value="item.isListed" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.productId)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.productId)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud ref="crud" v-slot="{ item, isEdit }" width="1800" :rules="rules" :default="defaultItem"
				item-name="product" api="/v1/admin/products?trans=off" model="product" :post-item-map="simpleProductMapper"
				:put-item-map="simpleProductMapper" @updated="refreshAndClearCache" @created="refreshAndClearCache"
				@deleted="refreshAndClearCache" @closed="productTabsModel = 0">
				<v-sheet min-height="120">
					<v-row>
						<v-col cols="12" md="3">
							<thumbnail :width="222"
								:src="item?.media?.cover?.[0]?.preview || item?.media?.gallery?.[0]?.preview || '/images/product-placeholder.webp'" />
						</v-col>
						<v-col cols="12" md="6">
							<h1>{{ item.name[$i18n.locale] || $t("product.new-product") }}</h1>
							<div class="text--secondary pt-4 line-clamp-3"
								v-html="item.description[$i18n.locale] || $t('product.no-description')" />
						</v-col>
						<v-col>
							<vc-select v-model="item.type" :items="productsTypes" @change="productTypeHandler" />
						</v-col>
					</v-row>
				</v-sheet>
				<v-tabs v-model="productTabsModel" vertical>
					<template v-for="(tab, tabIndex) in productTabs">
						<v-tab v-if="tab.condition(item)" :key="`product-tab-${tabIndex}`" :href="`#${tab.slug}`"
							class="justify-start" style="min-width: 200px">
							<v-icon left>
								{{ tab.icon }}
							</v-icon>
							{{ tab.name }}
						</v-tab>
					</template>
					<!-- Start of General Tab -->
					<v-tab-item value="general">
						<v-card-text>
							<!-- <vc-text-field v-model="item.name.en" :rules="[$rules.required('name')]"> </vc-text-field> -->
							<Translatable v-slot="{ on, dir, update, value }" v-model="item.name"
								@input="!isEdit && createSlug($event)">
								<vc-text-field label="Name" :value="value" :rules="[$rules.required('name')]" :dir="dir"
									@input="update" v-on="on" />
							</Translatable>
							<lookups-brands v-if="item.type !== 'bundle'" v-model="item.brandId" :multiple="false"
								:categories="item.categories" />
							<vc-text-field v-model="item.SKU" label="SKU" />
							<vc-date v-model="item.releaseAt" label="Released At" />
							<v-switch v-model="item.isListed"
								:label="`Appear on listing page? ${item.isListed ? 'Yes' : 'No'}`" @change="toggleListed" />
							<v-slider v-if="item.isListed" v-model="item.priority" label="Priority" min="0" max="10"
								tick-labels thumb-label="always">
								<template #thumb-label="props">
									<span class="text-subtitle-2">
										{{ props.value }}
									</span>
								</template>
							</v-slider>
							<publish-field v-model="item.isPublished" :published-at.sync="item.publishedAt"
								:expires-at.sync="item.unPublishedAt" />
							<lookups-labels v-model="item.labels" :multiple="true" />
						</v-card-text>
</v-tab-item>
					<!-- End of General Tab -->
					<!-- Start of Description Tab -->
					<v-tab-item value="description">
						<v-card-text>
							<Translatable v-slot="{ on, dir, update, value, key }" v-model="item.description">
								<vc-wysiwyg :key="key" rows="10" label="Description" min-height="800" :value="value"
									class="product-description-wysiwyg" :dir="dir" @input="update" v-on="on" />
							</Translatable>

							<ai-content v-model="item.description" prompt="product-description"
								:body="{ productName: item.name.en }" />
						</v-card-text>
					</v-tab-item>
					<!-- End of Description Tab -->

					<!-- Start of Attributes Tab -->
					<v-tab-item value="category-attributes">
						<v-row>
							<v-col cols="12" md="5">
								<categories v-model="item.categories" multiple />
							</v-col>
							<v-col cols="12" md="7">
								<products-attributes v-model="item.attributes"
									:variation-attributes.sync="item.variationAttributes" :categories="item.categories"
									:has-variation="item.type !== 'simple'" />
							</v-col>
						</v-row>
					</v-tab-item>
					<!-- End of Attributes Tab -->

					<!-- Start of Variances Tab -->
					<v-tab-item value="variances">
						<v-card flat>
							<v-card-title>
								Variances
								<v-spacer />
								<v-btn v-if="isMassEditEnabled" class="mb-4" color="primary"
									:disabled="!item.variationAttributes.length" text @click="cancelMassEdit">
									Cancel
								</v-btn>
								<v-btn v-if="!isMassEditEnabled" class="mb-4" color="primary"
									:disabled="!item.variationAttributes.length" text @click="massEdit()">
									<v-icon left>
										mdi-pencil
									</v-icon>
									Mass Edit
								</v-btn>
								<v-btn v-if="isMassEditEnabled" class="mb-4" color="primary"
									:disabled="!item.variationAttributes.length || !selectedItems.length" text
									@click="openMassEdit">
									<v-icon left>
										mdi-pencil
									</v-icon>
									Edit Selected ({{ selectedItems.length }})
								</v-btn>
								<v-btn class="mb-4" color="primary" :disabled="!item.variationAttributes.length" text
									@click="getSelectedAttributes">
									<v-icon left>
										mdi-plus
									</v-icon>
									Generate Variances
								</v-btn>

								<v-btn v-if="isMassEditEnabled" class="mb-4" color="primary"
									:disabled="!item.variationAttributes.length || !selectedItems.length" text>
									<v-menu offset-x>
										<template #activator="{ on, attrs }">
											<v-btn icon small v-bind="attrs" v-on="on">
												<v-icon>mdi-dots-vertical</v-icon>
											</v-btn>
										</template>
										<v-list dense>
											<v-list-item link @click="outOfStocks">
												<v-list-item-action>
													<v-icon>mdi-flask-empty-off</v-icon>
												</v-list-item-action>
												<v-list-item-title>
{{ $t("product.out-of-stock") }}
												</v-list-item-title>
											</v-list-item>
											<v-list-item link @click="deleteStocks">
												<v-list-item-action>
													<v-icon>mdi-delete</v-icon>
												</v-list-item-action>
												<v-list-item-title>
{{ $t("product.delete-stock")
												}}
</v-list-item-title>
											</v-list-item>
											<v-list-item link @click="deleteImages">
												<v-list-item-action>
													<v-icon>mdi-delete-alert-outline</v-icon>
												</v-list-item-action>
												<v-list-item-title>
{{ $t("product.delete-all-images") }}
												</v-list-item-title>
											</v-list-item>
											<v-list-item link @click="deleteImagesCover">
												<v-list-item-action>
													<v-icon>mdi-delete-circle</v-icon>
												</v-list-item-action>
												<v-list-item-title>
{{ $t("product.delete-images-covor") }}
												</v-list-item-title>
											</v-list-item>
											<v-list-item link @click="deleteImagesGallery">
												<v-list-item-action>
													<v-icon>mdi-delete-empty</v-icon>
												</v-list-item-action>
												<v-list-item-title>
{{ $t("product.delete-images-gallery") }}
												</v-list-item-title>
											</v-list-item>
										</v-list>
									</v-menu>
								</v-btn>
							</v-card-title>
							<v-card-text>
								<v-checkbox v-show="item.variationAttributes.length && isMassEditEnabled"
									v-model="selectAllMassEditModel" class="mb-4" color="primary" label="Check All"
									@change="checkAllMissEditToggle" />
								<div class="d-flex align-baseline">
									<v-dialog v-model="variationDialogModel" max-width="1500">
										>
										<v-card>
											<v-card-title>Variation Attributes</v-card-title>
											<v-card-subtitle>
												Must select at least 2 options for each attribute below to
												generate.
											</v-card-subtitle>
											<v-card-text>
												<v-window v-model="variationAttributesModel">
													<v-window-item value="step1">
														<v-skeleton-loader type="list-item@3" :loading="isGettingAttributes">
															<div>
																<v-list-group v-for="attribute in attributes"
																	:key="attribute.attributeId" :value="true" no-action sub-group>
																	<template #activator>
																		<v-list-item-content>
																			<v-list-item-title>{{ attribute.name }}</v-list-item-title>
																		</v-list-item-content>
																	</template>
																	<v-list-item-group v-model="attribute.variationOptions" class="ms-8"
																		multiple>
																		<v-list-item v-for="option in attribute.options"
																			:key="option.attributeOptionId" :value="option"
																			active-class="primary--text" dense>
																			<template #default="{ active }">
																				<v-list-item-action>
																					<v-checkbox :input-value="active" color="primary" />
																				</v-list-item-action>
																				<v-list-item-content>
																					<v-list-item-title>
																						{{ attribute.prefix }}
																						{{ option.number || option.name }}
																						{{ attribute.suffix }}
																					</v-list-item-title>
																				</v-list-item-content>
																			</template>
																		</v-list-item>
																	</v-list-item-group>
																	<!-- <v-list-item v-for="option in item.options" :key="option.attributeOptionId" link>
																		<v-list-item-title v-text="title"></v-list-item-title>

																		<v-list-item-icon>
																			<v-icon v-text="icon"></v-icon>
																		</v-list-item-icon>
																	</v-list-item> -->
																</v-list-group>
															</div>
														</v-skeleton-loader>
													</v-window-item>
													<v-window-item value="step2">
														<v-toolbar dense flat>
															<v-spacer />
															<v-btn text @click="selectAllGeneratedVariances">
																Select All
															</v-btn>selectAllGeneratedVariances
														</v-toolbar>
														<v-list-item-group v-model="selectedGeneratedVariances" multiple>
															<template v-for="(variance, i) in generatedVariances">
																<v-list-item :key="`item-${i}`" :value="variance"
																	active-class="primary--text"
																	:disabled="item.variances.some((v) => v.slug === variance.slug)"
																	:input-value="item.variances.some((v) => v.slug === variance.slug)
																		">
																	<template #default="{ active }">
																		<v-list-item-content>
																			<v-list-item-title>
																				{{ variance.name | trans }}
																			</v-list-item-title>
																			<v-list-item-subtitle v-if="
																				item.variances.some(
																					(v) => v.slug === variance.slug
																				)
																			">
																				Already Exists
																			</v-list-item-subtitle>
																		</v-list-item-content>

																		<v-list-item-action>
																			<v-checkbox :input-value="active" color="primary" />
																		</v-list-item-action>
																	</template>
																</v-list-item>
															</template>
														</v-list-item-group>
													</v-window-item>
												</v-window>
											</v-card-text>
											<v-card-actions>
												<v-spacer />
												<v-btn text @click="variationDialogModel = false">
													Cancel
												</v-btn>
												<v-btn v-if="variationAttributesModel === 'step2'" text
													@click="variationAttributesModel = 'step1'">
													Back
												</v-btn>
												<v-btn v-if="variationAttributesModel === 'step1'" color="primary" text
													:disabled="!canGenerate" @click="generateVariances">
													Generate
												</v-btn>
												<v-btn v-if="variationAttributesModel === 'step2'" color="primary" text
													@click="selectGeneratedVariances">
													Add Selected
												</v-btn>
											</v-card-actions>
										</v-card>
									</v-dialog>
								</div>
								<v-row>
									<v-col v-if="!item.variationAttributes.length">
										<v-alert text type="info" border="left">
											<div class="d-flex align-center">
												No attributes variation. Please select variation attributes first in the "{{
													productTabs.find((v) => v.slug === "category-attributes").name | trans
												}}" tab.
											</div>
										</v-alert>
									</v-col>
									<v-col v-else-if="!item.variances.length">
										<v-alert text type="info" border="left">
											<div class="d-flex align-center">
												No Variances yet
											</div>
										</v-alert>
									</v-col>
									<v-col v-for="(variance, vIndex) in item.variances" :key="`variance-${vIndex}`" md="12"
										class="d-flex">
										<v-checkbox v-if="isMassEditEnabled" :value="selectedItems.includes(vIndex)" class="me-3"
											@change="massEditCheckboxHandler(vIndex)" />
										<variance-card :item="variance" class="flex-grow-1"
											@click="openVarianceDrawer(variance, vIndex)">
											<template #actions="">
												Default: {{ variance.isDefault }}
												<v-spacer />
												<v-btn v-tooltip="$t('common.default')" small icon :disabled="variance.isDefault"
													@click.stop="setDefaultVariance(item, vIndex)">
													<v-icon small>
														mdi-numeric-1-box-multiple-outline
													</v-icon>
												</v-btn>
												<v-btn v-tooltip="$t('common.delete')" small icon
													@click.stop="removeVariance(item, vIndex)">
													<v-icon small>
														mdi-delete
													</v-icon>
												</v-btn>
											</template>
										</variance-card>
									</v-col>
								</v-row>
							</v-card-text>
						</v-card>
					</v-tab-item>

					<v-tab-item value="bundle">
						<v-card flat>
							<v-card-title>
								bundle
								<v-spacer />
								<variance-select-modal @input="addVarianceToBundle">
									<template #activator="{ on }">
										<v-btn text color="primary" v-on="on">
											<v-icon left>
												mdi-plus
											</v-icon> {{ $t("product.add-variance") }}
										</v-btn>
									</template>
								</variance-select-modal>
								<v-btn text color="primary" @click="openBundleVarianceDrawer(defaultVariance, 1)">
									<v-icon left>
										mdi-plus
									</v-icon> {{ $t("product.new-variance") }}
								</v-btn>
							</v-card-title>
							<v-card-text>
								<v-row>
									<v-col v-for="(variance, vIndex) in item.bundles" :key="`bundles-${vIndex}`" md="12"
										class="d-flex">
										<bundle-card :item="variance" class="flex-grow-1">
											<template #actions="">
												<v-spacer />
												<v-btn v-tooltip="$t('common.delete')" small icon
													@click.stop="removeBundle(item, vIndex)">
													<v-icon small>
														mdi-delete
													</v-icon>
												</v-btn>
											</template>
										</bundle-card>
									</v-col>
								</v-row>
							</v-card-text>
						</v-card>
					</v-tab-item>

					<!-- End of Variances Tab -->

					<!-- Start of Payment and Shipping Tab -->
					<v-tab-item value="payment-shipping">
						<v-card-title>Disable Specific Payment </v-card-title>
						<v-card-text>
							<!-- <v-list-item-group v-model="item.paymentsMethod" multiple>
								<v-list-item v-for="method in paymentMethods" :key="method.value" :value="method.value">
									<template #default="{ active }">
										<v-list-item-action>
											<v-checkbox :input-value="!active" color="primary"></v-checkbox>
										</v-list-item-action>

										<v-list-item-content>
											<v-list-item-title v-text="method.text"></v-list-item-title>
										</v-list-item-content>
									</template>
								</v-list-item>
							</v-list-item-group> -->
							<vc-select v-model="item.disabledPaymentMethods" chips small-chips deletable-chips
								label="Disabled Payments" multiple api="/v1/lookups/paymentMethod" />
						</v-card-text>
						<v-card-title> Shipping </v-card-title>
						<v-card-text>
							<vc-autocomplete v-model="item.shippingCarriers" label="Shipping Carrier"
								api="/v1/lookups/shipping-carriers" multiple />
						</v-card-text>
					</v-tab-item>
					<!-- End of Payment and Shipping Tab -->

					<!-- Start of Stocks Tab -->
					<v-tab-item value="stock">
						<v-card-text>
							<stocks v-model="item.stocks" />
						</v-card-text>
					</v-tab-item>
					<!-- End of Stocks Tab -->

					<!-- Start of Media Tab -->
					<v-tab-item value="media">
						<v-card flat>
							<v-card-text>
								<media v-model="item.media.cover" collection="Cover" />
								<media v-model="item.media.gallery" collection="Gallery" />
							</v-card-text>
						</v-card>
					</v-tab-item>
					<!-- End of Media Tab -->

					<!-- Start of SEO Tab -->
					<v-tab-item value="seo">
						<v-card-text>
							<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaTitle">
								<vc-text-field :value="value" label="Meta Title" counter :dir="dir" v-on="on" @input="update" />
							</Translatable>

							<Translatable v-slot="{ update, value, on, dir }" v-model="item.metaDescription">
								<vc-textarea :value="value" label="Meta Description" counter rows="6" :dir="dir" v-on="on" @input="update" />
							</Translatable>
							<ai-content v-model="item.metaDescription" prompt="generate-meta" :body="{ text: item.name }" />
						</v-card-text>
					</v-tab-item>
					<!-- End of SEO Tab -->
				</v-tabs>

				<!-------------------------------------------------------------------------------------------------->
				<!-- Start of Variance Drawer -->
				<crud v-bind="varianceDrawerBind" ref="variance" :default="defaultVariance" :api="false"
					item-name="variance" @updated="saveVariance" @created="saveMassEdit" @closed="varianceCloseHandler">
					<template v-if="isMassEditCrudOpen" #title>
						Mass Edit
					</template>
					<template #default="{ item: variance }">
						<v-tabs vertical>
							<v-tab v-for="(tab, tabIndex) in varianceTabs" :key="`variance-tab-${tabIndex}`"
								:href="`#${tab.slug}`" class="justify-start">
								{{ tab.name }}
							</v-tab>
							<!-- Start of Variance General Tab -->
							<v-tab-item value="general">
								<v-card-text>
									<Translatable v-slot="{ on, dir, update, value }" v-model="variance.name">
										<vc-text-field label="Name" :disabled="isMassEditCrudOpen" :value="value" :dir="dir"
											@input="update" v-on="on" />
									</Translatable>
									<lookups-brands v-if="variance.type === 'bundle'" v-model="variance.brandId"
										:multiple="false" :categories="variance.categories" :disabled="isMassEditCrudOpen" />
									<vc-text-field v-model="variance.SKU" label="Variance SKU" :disabled="isMassEditCrudOpen" />
									<vc-text-field v-model="variance.modelNumber" label="Variance Model Number" />
									<publish-field v-model="variance.isPublished" :published-at.sync="variance.publishedAt"
										:expires-at.sync="variance.unPublishedAt" :disabled="isMassEditCrudOpen" />
								</v-card-text>
							</v-tab-item>
							<!-- End of Variance General Tab -->

							<!-- Start of Variance Description Tab -->
							<v-tab-item value="description">
								<v-card-text>
									<Translatable v-slot="{ on, dir, update, value }" v-model="variance.description">
										<vc-wysiwyg rows="10" label="Description" :value="value" :dir="dir" @input="update"
											v-on="on" />
									</Translatable>
								</v-card-text>
							</v-tab-item>
							<!-- End of Variance Description Tab -->

							<!-- Start of Stocks Tab -->
							<v-tab-item value="stock">
								<v-card-text>
									<stocks v-model="variance.stocks" />
								</v-card-text>
							</v-tab-item>
							<!-- End of Stocks Tab -->

							<!-- Start of media Tab -->
							<v-tab-item value="media">
								<v-card-text>
									<media v-model="variance.media.cover" collection="Cover" />

									<media v-model="variance.media.gallery" collection="Gallery" />
								</v-card-text>
							</v-tab-item>
							<!-- End of media Tab -->

							<!-- Start of SEO Tab -->
							<v-tab-item value="seo">
								<v-card-text>
									<Translatable v-slot="{ update, value, on, dir }" v-model="variance.metaTitle">
										<vc-text-field :value="value" label="Meta Title" counter :dir="dir"
											:rules="[$rules.required('Meta Title'), $rules.maxLength('Meta Title', 70)]" v-on="on"
											@input="update" />
									</Translatable>
									<Translatable v-slot="{ update, value, on, dir }" v-model="variance.metaDescription">
										<vc-textarea :value="value" label="Meta Description" counter rows="6" :dir="dir"
											:rules="[$rules.required('Meta Description'), $rules.maxLength('Meta Description', 175)]"
											v-on="on" @input="update" />
									</Translatable>
									<ai-content v-model="variance.metaDescription" prompt="generate-meta"
										:body="{ text: item.name }" />
								</v-card-text>
							</v-tab-item>
							<!-- End of SEO Tab -->
						</v-tabs>
					</template>
				</crud>

				<crud v-bind="bundleDrawerBind" ref="bundle" :default="defaultBundle" :api="false" item-name="bundle"
					@updated="saveBundle" @created="saveBundle" @closed="bundleCloseHandler">
					<template #default="{ item: variance }">
						<v-tabs vertical>
							<v-tab v-for="(tab, tabIndex) in bundleVarianceTabs" :key="`variance-tab-${tabIndex}`"
								:href="`#${tab.slug}`" class="justify-start">
								{{ tab.name }}
							</v-tab>
							<v-tab-item value="general">
								<v-card-text>
									<Translatable v-slot="{ on, dir, update, value }" v-model="variance.name">
										<vc-text-field label="Name" :disabled="isMassEditCrudOpen" :value="value" :dir="dir"
											@input="update" v-on="on" />
									</Translatable>
									<lookups-brands v-if="variance.type === 'bundle'" v-model="variance.brandId"
										:multiple="false" :categories="variance.categories" :disabled="isMassEditCrudOpen" />
									<vc-text-field v-model="variance.SKU" label="Variance SKU" :disabled="isMassEditCrudOpen" />
									<vc-text-field v-model="variance.modelNumber" label="Variance Model Number" />
									<publish-field v-model="variance.isPublished" :published-at.sync="variance.publishedAt"
										:expires-at.sync="variance.unPublishedAt" :disabled="isMassEditCrudOpen" />
								</v-card-text>
							</v-tab-item>
							<v-tab-item value="description">
								<v-card-text>
									<Translatable v-slot="{ on, dir, update, value }" v-model="variance.description">
										<vc-wysiwyg rows="10" label="Description" :value="value" :dir="dir" @input="update"
											v-on="on" />
									</Translatable>
								</v-card-text>
							</v-tab-item>
							<v-tab-item value="media">
								<v-card-text>
									<media v-model="variance.media.cover" collection="Cover" />
									<media v-model="variance.media.gallery" collection="Gallery" />
								</v-card-text>
							</v-tab-item>
						</v-tabs>
					</template>
				</crud>
			</crud>
		</div>
	</page>
</template>

<script>
import { isPublished, productsTypes } from "~/config/Enum"
export default {
	name: "ProductsIndex",
	data() {
		return {
			value: {},
			rules: [value => !value || value.size < 2000000 || "Avatar size should be less than 2 MB!"],
			productTabsModel: 0,
			attributes: [],
			attributeOptions: [],
			generatedVariances: [],
			selectedGeneratedVariances: [],
			itemsTab: ["Main Product"],
			types: ["bundle", "alternative"],
			imagePreview: "",
			image: "",
			variationDialogModel: false,
			variationAttributesModel: "step1",
			columns: [
				{
					text: "#",
					sortable: true,
					value: "productId",
				},
				{
					text: "",
					sortable: true,
					value: "media",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.category"),
					sortable: true,
					value: "categories",
				},
				{
					text: this.$t("common.type"),
					sortable: true,
					value: "type",
				},
				{
					text: this.$t("common.published"),
					sortable: true,
					value: "isPublished",
				},
				{
					text: this.$t("common.listed"),
					sortable: true,
					value: "isListed",
				},
				// {
				// 	text: this.$t("common.created-at"),
				// 	sortable: false,
				// 	value: "createdAt",
				// },
				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},
				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			varianceNameModal: false,
			varianceName: {},
			defaultItem: {
				name: { ar: null, en: null },
				slug: '',
				description: {
					ar: null,
					en: null,
				},
				SKU: null,
				releaseAt: null,
				type: "alternative",
				isPublished: true,
				isListed: true,
				priority: 0,
				variances: [],
				bundles: [],
				stocks: [],
				media: {
					cover: [],
					gallery: [],
				},
				attributes: [
					// { attributeId: 1, attributeOptionId: 104 },
					// { attributeId: 2, attributeOptionId: 108 },
					// { attributeId: 3, attributeOptionId: 110 },
				],
				categories: [],
				metaTitle: { en: '{name} - Best Prices', ar: '{name} - أفضل سعر' },
				metaDescription: { ar: '{name} ✔️ تسوق اونلاين في الأردن مع أكشن موبايل ✔️ أسعار مناسبة ✔️ تصفح الموقع الآن', en: '{name} ✔️ Shop Online in Jordan with Action Mobile ✔️ Affordable Prices ✔️ Browse the Website Now' },
				variationAttributes: [],
				shippingCarriers: [1],
				labels: [],
			},
			defaultVariance: {
				alternativeId: null,
				varianceId: null,
				// productId: item.productId,
				// categories: item.categories,
				auctionId: null,
				name: {
					ar: null,
					en: null,
				},
				slug: '',
				brandId: null,
				SKU: null,
				type: "physical",
				metaTitle: { ar: '{name} - Best Prices', en: '{name} - أفضل سعر' },
				metaDescription: { ar: '{name} ✔️ تسوق اونلاين في الأردن مع أكشن موبايل ✔️ أسعار مناسبة ✔️ تصفح الموقع الآن', en: '{name} ✔️ Shop Online in Jordan with Action Mobile ✔️ Affordable Prices ✔️ Browse the Website Now' },
				isDefault: false,
				isPublished: true,
				publishedAt: null,
				attributes: [],
				stocks: [],
				media: {
					cover: [],
					gallery: [],
				},
			},
			defaultBundle: {
				alternativeId: null,
				varianceId: null,
				auctionId: null,
				name: {
					ar: null,
					en: null,
				},
				slug: '',
				brandId: null,
				type: "physical",
				metaTitle: {
					ar: null,
					en: null,
				},
				metaDescription: {
					ar: null,
					en: null,
				},
				publishedAt: null,
				media: {
					cover: [],
					gallery: [],
				},
			},
			isGettingAttributes: false,
			// varianceDrawer: false,
			varianceDrawerIndex: null,
			bundleDrawerIndex: null,
			isMassEditEnabled: false,
			selectedItems: [],
			isMassEditCrudOpen: false,
			selectAllMassEditModel: false,
			isEnabledFlush: true,
			defaultStock: {
				stockId: null,
				quantity: 100,
				sold: 0,
				maxPerUser: 50,
				isOffer: false,
				publishedAt: null,
				unPublishedAt: null,
				sort: 43,
				isPublished: true,
				isPreOrder: false,
				note: null,
				supplierId: null,
				price: {
					value: null,
					// basePrice: 69.19,
					currencyId: 1,
				},
				cost: {
					value: null,
					// basePrice: 1.91,
					currencyId: 1,
				},
				priceBeforeOffer: {
					value: null,
					// basePrice: null,
					currencyId: 1,
				},
			},
		}
	},
	async fetch() {
		await this.getPaymentMethods()
	},
	computed: {
		productTabs() {
			return [
				{
					name: this.$t('product.general'),
					icon: "mdi-home",
					slug: "general",
					condition(item) {
						return true
					},
				},
				{
					name: this.$t('product.description'),
					icon: "mdi-text",
					slug: "description",
					condition(item) {
						return true
					},
				},
				{
					name: this.$t('product.category-and-attributes'),
					icon: "mdi-list-box",
					slug: "category-attributes",
					condition(item) {
						return true
					},
				},
				{
					name: this.$t('product.variances'),
					icon: "mdi-package-variant-plus",
					slug: "variances",
					condition(item) {
						return item.type !== "simple" && item.type !== "bundle"
					},
				},
				{
					name: this.$t('product.bundle'),
					icon: "mdi-package-variant-plus",
					slug: "bundle",
					condition(item) {
						return item.type !== "alternative" && item.type !== "simple"
					},
				},
				{
					name: this.$t('product.stocks-and-prices'),
					icon: "mdi-currency-usd",
					slug: "stock",
					condition(item) {
						return item.type !== "alternative"
					},
				},
				{
					name: this.$t('product.payment-and-shipping'),
					icon: "mdi-truck-delivery",
					slug: "payment-shipping",
					condition(item) {
						return true
					},
				},
				{
					name: this.$t('common.media'),
					icon: "mdi-multimedia",
					slug: "media",
					condition(item) {
						return true
					},
				},
				{
					name: this.$t('product.seo'),
					icon: "mdi-home",
					slug: "seo",
					condition(item) {
						return true
					},
				},
			]
		},
		varianceTabs() {
			return [
				{
					name: "General",
					slug: "general",
					icon: "mdi-home",
				},
				{
					name: "Description",
					slug: "description",
					icon: "mdi-text",
				},
				{
					name: "Stock",
					slug: "stock",
					icon: "mdi-home",
				},
				{
					name: "Media",
					slug: "media",
					icon: "mdi-multimedia",
				},
				{
					name: "SEO",
					slug: "seo",
					icon: "mdi-home",
				},
			]
		},
		bundleVarianceTabs() {
			return [
				{
					name: "General",
					slug: "general",
					icon: "mdi-home",
				},
				{
					name: "Description",
					slug: "description",
					icon: "mdi-text",
				},
				{
					name: "Media",
					slug: "media",
					icon: "mdi-multimedia",
				},
			]
		},
		productsTypes() {
			return productsTypes.map((item) => {
				return {
					text: this.$t(item.key),
					value: item.value,
				}
			})
		},
		varianceDrawerBind() {
			return {
				// title: this.varianceDrawerItem?.name?.[this.$i18n.locale],
			}
		},
		bundleDrawerBind() {
			return {
				// title: this.varianceDrawerItem?.name?.[this.$i18n.locale],
			}
		},
		canGenerate() {
			// make sure that each attribute has at least two option selected
			return this.attributes.every(attribute => attribute.variationOptions.length > 1)
		},
	},
	mounted() {
		// this.getAttributeOptions();
	},

	methods: {
		getPaymentMethods() {
			return this.$axios.$get("/v1/lookups/paymentMethod").then((res) => {
				this.paymentMethods = res
			})
		},

		toggleListed() {
			if (this.$refs.crud.item.isListed === false) {
				this.$refs.crud.item.priority = 0
			}
		},
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/products")
			window.location.reload()
		},
		onImageChoose(e) {
			this.image = e.target.files[0]
			const reader = new FileReader()
			reader.readAsDataURL(this.image)
			reader.onload = (e) => {
				this.imagePreview = e.target.result
			}
		},
		removeImagePreview() {
			this.imagePreview = null
		},
		// insertStock(stocks) {
		// 	stocks.push({
		// 		price: "",
		// 		cost: "",
		// 		quantity: "",
		// 		maxPerUser: "",
		// 		isPublished: false,
		// 		startDate: "",
		// 		endDate: "",
		// 		order: "",
		// 		supplierId: "",
		// 	});
		// },

		addVariance(variance = {}) {
			const item = this.$cloneDeep(this.$refs.crud.item)

			const defaultVariance = {
				alternativeId: null,
				varianceId: null,
				productId: item.productId,
				categories: item.categories,
				auctionId: null,
				name: {
					ar: null,
					en: null,
				},
				slug: {
					ar: null,
					en: null,
				},
				brandId: null,
				SKU: null,
				type: "physical",
				metaTitle: { ar: '{name} - Best Prices', en: '{name} - أفضل سعر' },
				metaDescription: { ar: '{name} ✔️ تسوق اونلاين في الأردن مع أكشن موبايل ✔️ أسعار مناسبة ✔️ تصفح الموقع الآن', en: '{name} ✔️ Shop Online in Jordan with Action Mobile ✔️ Affordable Prices ✔️ Browse the Website Now' },
				isDefault: false,
				publishedAt: null,
				isPublished: true,
				attributes: [],
				stocks: [this.defaultStock],
				media: {
					cover: [],
					gallery: [],
				},
			}

			const newVariance = {
				...defaultVariance,
				...variance,
			}

			this.generatedVariances.push(this.$cloneDeep(newVariance))
		},
		removeVariance(item, vIndex) {
			this.$confirm(`Are you sure you want to delete this ${item.name[this.$i18n.locale]}`, {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					// remove variance from variances
					item.variances.splice(vIndex, 1)
				}
			})
		},
		setDefaultVariance(item, vIndex) {
			item.variances.forEach((v, index) => {
				if (index === vIndex) {
					v.isDefault = true
				} else {
					v.isDefault = false
				}
			})
		},
		generateVariances() {
			console.log("entered")
			/* eslint-disable no-unused-vars */
			const variationAttributes = this.$cloneDeep(this.$refs.crud.item.variationAttributes)
			const item = this.$cloneDeep(this.$refs.crud.item)
			const variances = []

			variationAttributes.forEach((variationAttrId) => {
				const attribute = this.attributes.find(a => a.attributeId === variationAttrId)

				const text = attribute.name
				const prefix = attribute.prefix
				const suffix = attribute.suffix
				const options = this.$cloneDeep(attribute.variationOptions)
				variances.push({
					name: {
						en: text,
						ar: text,
					},
					attributeId: attribute.attributeId,
					prefix,
					suffix,
					options,
					isPublished,
				})
			})

			// sort variances by name
			variances.sort((a, b) => {
				if (a.name.en < b.name.en) {
					return -1
				}
				if (a.name.en > b.name.en) {
					return 1
				}
				return 0
			})
			const combinations = this.getAllCombinations(variances)
			this.generatedVariances = []

			const { toKebabCase } = require("js-convert-case")
			const combinedVariances = combinations
				.map((combination) => {
					return combination.reduce(
						(acc, cur, index, array) => {
							acc.slug.en += toKebabCase(`${cur.value.name || cur.value.number}${cur.suffix}`)
							acc.slug.ar += toKebabCase(`${cur.value.name || cur.value.number}${cur.suffix}`)
							acc.isPublished = true
							acc.name.en += `${cur.value.number || cur.value.name}${cur.suffix}`
							acc.name.ar += `${cur.value.number || cur.value.name}${cur.suffix}`
							// acc.metaTitle.en = acc.name.en
							// acc.metaTitle.ar = acc.name.ar
							// acc.metaDescription.en = acc.name.en
							// acc.metaDescription.ar = acc.name.ar
							acc.attributes.push({
								attributeId: cur.attributeId,
								attributeOptionId: cur.attributeOptionId,
							})
							if (index !== array.length - 1) {
								acc.slug.en += "-"
								acc.slug.ar += "-"
								acc.name.en += ", "
								acc.name.ar += ", "
							}
							return acc
						},
						{
							slug: {
								en: "",
								ar: "",
							},
							name: {
								en: "",
								ar: "",
							},
							attributes: [],
							metaTitle: { en: '{name} - Best Prices', ar: '{name} - أفضل سعر' },
							metaDescription: { ar: '{name} ✔️ تسوق اونلاين في الأردن مع أكشن موبايل ✔️ أسعار مناسبة ✔️ تصفح الموقع الآن', en: '{name} ✔️ Shop Online in Jordan with Action Mobile ✔️ Affordable Prices ✔️ Browse the Website Now' },
						},
					)
				})
				.forEach((variance) => {
					this.addVariance(variance)
					console.log("forEach variance", variance)
				})

			this.variationAttributesModel = "step2"
		},
		getAllCombinations(variances, current = [], index = 0, results = []) {
			if (index === variances.length) {
				results.push(current.slice())
			} else {
				for (let i = 0; i < variances[index].options.length; i++) {
					console.log("options", variances[index].options[i])
					console.log("name", {
						en: variances[index].name.en,
						ar: variances[index].name.ar,
					})
					current.push({
						name: {
							en: variances[index].name.en,
							ar: variances[index].name.ar,
						},
						prefix: variances[index].prefix,
						suffix: variances[index].suffix,
						attributeId: variances[index].attributeId,
						attributeOptionId: variances[index].options[i].attributeOptionId,
						value: variances[index].options[i],
					})
					this.getAllCombinations(variances, current, index + 1, results)
					current.pop()
				}
			}
			return results
		},

		openVarianceDrawer(variance, index) {
			// this.varianceDrawerItem = variance;
			this.varianceDrawerIndex = index
			// this.varianceDrawer = true;
			this.$refs.variance.edit(variance)
		},
		openBundleVarianceDrawer(variance, index) {
			// this.varianceDrawerItem = variance;
			this.bundleDrawerIndex = index
			// this.varianceDrawer = true;
			this.$refs.bundle.edit(variance)
		},
		getSelectedAttributes() {
			this.variationDialogModel = true
			this.attributes = []
			const promises = []
			const clonedAttributes = this.$cloneDeep(this.$refs.crud.item.variationAttributes)
			this.isGettingAttributes = true
			clonedAttributes.forEach((attribute) => {
				promises.push(
					this.$axios.$get(`/v1/admin/attributes/${attribute}`).then((res) => {
						// res.value = res.options.find((option) => option.attributeOptionId === attribute.attributeOptionId);
						res.variationOptions = []
						this.attributes.push(res)
					}),
				)
			})

			Promise.all(promises).then(() => {
				this.isGettingAttributes = false
			})
		},
		selectGeneratedVariances() {
			const item = this.$refs.crud.item
			let hasDefault = item.variances.some(v => v.isDefault)

			this.selectedGeneratedVariances.forEach((variance) => {
				variance.isDefault = !hasDefault
				hasDefault = true
				item.variances.push(variance)
			})
			this.selectedGeneratedVariances = []
			this.generatedVariances = []
			this.variationAttributesModel = "step1"
			this.variationDialogModel = false
		},
		saveVariance(variance) {
			// if (!this.$refs.variance.$form.validate()) return;
			const item = this.$refs.crud.item
			// const variance = this.$cloneDeep(this.varianceDrawerItem);
			const index = this.varianceDrawerIndex

			// item.variances.splice(index, 1, variance);
			this.$set(this.$refs.crud.item.variances, index, variance)

			// this.$refs.variance.$form.reset();
			// this.$refs.variance.$form.resetValidation();
		},
		saveBundle(bundle) {
			// if (!this.$refs.variance.$form.validate()) return;
			const item = this.$refs.crud.item
			// const variance = this.$cloneDeep(this.varianceDrawerItem);
			const index = this.varianceDrawerIndex

			const bundles = this.$cloneDeep(this.$refs.crud.item.bundles)
			this.$set(this.$refs.crud.item.bundles, bundles.length, bundle)

			// item.variances.splice(index, 1, variance);
			// this.$set(this.$refs.crud.item.bundles, index, bundle)
			// this.$refs.variance.$form.reset();
			// this.$refs.variance.$form.resetValidation();
		},
		createSlug(v) {
			const item = this.$refs.crud.item

			item.slug = this.toSlug(v)
		},
		productTypeHandler(type) {
			if (type === "simple") {
				const hasVariances = this.$refs.crud.item.variances.length

				if (hasVariances) {
					this.$confirm("Are you sure you want to change the product type to simple? This will remove all variances.", {
						title: this.$t("messages.confirm-delete-title"),
					}).then((isAccepted) => {
						if (isAccepted) {
							// remove all variances while keeping reactivity
							this.$refs.crud.item.variances.splice(0, this.$refs.crud.item.variances.length)
							this.$refs.crud.item.variances.push(this.$cloneDeep(this.defaultVariance))
						}
					})
				} else {
					this.$refs.crud.item.variances.push(this.$cloneDeep(this.defaultVariance))
				}
			}
			if (type === "bundle") {
				this.$confirm("Are you sure you want to change the product type to bundle? This will remove all.", {
					title: this.$t("messages.confirm-delete-title"),
				}).then((isAccepted) => {
					if (isAccepted) {
						// remove all variances while keeping reactivity
						this.$refs.crud.item.variances = []
					}
				})
			}
		},

		simpleProductMapper(product) {
			//
			if (product.type === "simple") {
				product.variances = [this.defaultVariance]

				product.variances.map((variance) => {
					variance.name = product.name
					variance.slug = product.slug
					variance.brandId = product.brandId
					variance.type = "physical" // or digital but not implemented yet;
					variance.metaTitle = product.metaTitle
					variance.metaDescription = product.metaDescription
					variance.SKU = product.SKU
					variance.isPublished = product.isPublished
					variance.publishedAt = product.publishedAt
					variance.unPublishedAt = product.unPublishedAt
					variance.stocks = product.stocks
					return variance
				})
			}
			return product
		},
		openMassEdit() {
			this.isMassEditCrudOpen = true
			// this.defaultVariance.metaTitle = this.$refs.crud.item.variances[0].metaTitle
			// this.defaultVariance.metaDescription = this.$refs.crud.item.variances[0].metaDescription
			// this.defaultVariance.description = this.$refs.crud.item.description
			// this.defaultVariance.stocks = []
			this.$refs.variance.new()
		},
		massEditCheckboxHandler(index) {
			if (this.selectedItems.includes(index)) {
				this.selectedItems = this.selectedItems.filter(i => i !== index)
			} else {
				this.selectedItems.push(index)
			}
		},
		saveMassEdit(variancePayload) {
			this.selectedItems.forEach((index) => {
				const originalVariance = this.$cloneDeep(this.$refs.crud.item.variances[index])
				const variance = this.$cloneDeep(variancePayload)
				variance.varianceId = originalVariance.varianceId
				variance.name = originalVariance.name
				variance.slug = originalVariance.slug
				variance.brandId = originalVariance.brandId
				variance.type = originalVariance.type
				variance.attributes = originalVariance.attributes
				variance.SKU = originalVariance.SKU
				variance.isPublished = originalVariance.isPublished
				if (variance.description?.en == null && variance.description?.ar == null) {
					variance.description = originalVariance.description
				}
				if (variance.metaTitle?.en == null && variance.metaTitle?.ar == null) {
					variance.metaTitle = originalVariance.metaTitle
				}
				if (variance.metaDescription?.en == null && variance.metaDescription?.ar == null) {
					variance.metaDescription = originalVariance.metaDescription
				}
				if (!variance.stocks?.length) {
					variance.stocks = originalVariance.stocks.concat(variance.stocks)
				}
				variance.media.cover = originalVariance.media.cover.concat(variance.media.cover)
				variance.media.gallery = originalVariance.media.gallery.concat(variance.media.gallery)
				this.$set(this.$refs.crud.item.variances, index, variance)
			})
			this.selectedItems = []
			this.isMassEditEnabled = false
			this.isMassEditCrudOpen = false
		},
		varianceCloseHandler() {
			this.isMassEditCrudOpen = false
		},
		bundleCloseHandler() {
			this.isMassEditCrudOpen = false
		},
		selectAllGeneratedVariances() {
			// except the disabled ones
			this.selectedGeneratedVariances = this.generatedVariances.filter(
				variance => !this.$refs.crud.item.variances.some(v => v.slug.en === variance.slug.en),
			)
		},

		checkAllMissEditToggle(value) {
			const indexes = this.$refs.crud.item.variances.map((v, i) => i)

			if (value) {
				this.selectedItems = indexes
			} else {
				this.selectedItems = []
			}
		},
		cancelMassEdit() {
			this.isMassEditEnabled = false
			this.isMassEditCrudOpen = false
			this.selectAllMassEditModel = false
			this.selectedItems = []
		},
		massEdit() {
			this.isMassEditEnabled = true
			this.selectedItems = []
		},
		outOfStocks() {
			this.selectedItems.forEach((index) => {
				const originalVariance = this.$cloneDeep(this.$refs.crud.item.variances[index])
				originalVariance.stocks.forEach((stock) => {
					stock.quantity = 0
				})
				this.$set(this.$refs.crud.item.variances, index, originalVariance)
			})
			this.selectedItems = []
			this.$toast.success(`${this.$t("product.out-of-stocks")}`)
		},

		deleteStocks() {
			this.selectedItems.forEach((index) => {
				const originalVariance = this.$cloneDeep(this.$refs.crud.item.variances[index])
				originalVariance.stocks = []
				this.$set(this.$refs.crud.item.variances, index, originalVariance)
			})
			this.selectedItems = []
			this.$toast.success(`${this.$t("product.deleted-stocks")}`)
		},

		deleteImages() {
			this.$confirm(`Are you sure you want to delete  images`, {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					const idesMedia = []
					this.selectedItems.forEach((index) => {
						const originalVariance = this.$cloneDeep(this.$refs.crud.item.variances[index])
						if (originalVariance.media && originalVariance.media.cover) {
							originalVariance.media.cover.forEach((cover) => {
								if (cover.id !== undefined && cover.id !== null) {
									idesMedia.push(cover.id)
								}
							})
						}
						if (originalVariance.media && originalVariance.media.gallery) {
							originalVariance.media.gallery.forEach((gallery) => {
								if (gallery.id !== undefined && gallery.id !== null) {
									idesMedia.push(gallery.id)
								}
							})
						}
						originalVariance.media = []
						this.$set(this.$refs.crud.item.variances, index, originalVariance)
					})
					this.selectedItems = []
					if (idesMedia.length > 0) {
						this.$axios
							.$post(`/v1/admin/media/deleteList`, { ides: idesMedia }).then((response) => {
								this.$toast.success(`${this.$t("product.deleted-images")}`)
							}).catch((error) => {
								console.log(error)
							})
					}
				}
			})
		},
		deleteImagesCover() {
			this.$confirm(`Are you sure you want to delete  images`, {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					const idesMedia = []
					console.log("idesMedia", idesMedia)
					this.selectedItems.forEach((index) => {
						const originalVariance = this.$cloneDeep(this.$refs.crud.item.variances[index])
						if (originalVariance.media && originalVariance.media.cover) {
							originalVariance.media.cover.forEach((cover) => {
								if (cover.id !== undefined && cover.id !== null) {
									idesMedia.push(cover.id)
								}
							})
						}
						originalVariance.media.cover = []
						this.$set(this.$refs.crud.item.variances, index, originalVariance)
					})
					this.selectedItems = []
					if (idesMedia.length > 0) {
						this.$axios
							.$post(`/v1/admin/media/deleteList`, { ides: idesMedia }).then((response) => {
								this.$toast.success(`${this.$t("product.deleted-cover")}`)
							}).catch((error) => {
								console.log(error)
							})
					}
				}
			})
		},

		deleteImagesGallery() {
			this.$confirm(`Are you sure you want to delete  images`, {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					const idesMedia = []
					this.selectedItems.forEach((index) => {
						const originalVariance = this.$cloneDeep(this.$refs.crud.item.variances[index])
						if (originalVariance.media && originalVariance.media.cover) {
							originalVariance.media.gallery.forEach((gallery) => {
								if (gallery.id !== undefined && gallery.id !== null) {
									idesMedia.push(gallery.id)
								}
							})
						}
						originalVariance.media.gallery = []
						this.$set(this.$refs.crud.item.variances, index, originalVariance)
					})
					this.selectedItems = []
					if (idesMedia.length > 0) {
						this.$axios
							.$post(`/v1/admin/media/deleteList`, { ides: idesMedia }).then((response) => {
								this.$toast.success(`${this.$t("product.deleted-gallery")}`)
							}).catch((error) => {
								console.log(error)
							})
					}
				}
			})
		},
		addVarianceToBundle(variance) {
			const bundles = this.$cloneDeep(this.$refs.crud.item.bundles)
			this.$set(this.$refs.crud.item.bundles, bundles.length, variance)
		},
		removeBundle(item, vIndex) {
			this.$confirm(`Are you sure you want to delete this ${item.name[this.$i18n.locale]}`, {
				title: this.$t("messages.confirm-delete-title"),
			}).then((isAccepted) => {
				if (isAccepted) {
					// remove variance from variances
					item.bundles.splice(vIndex, 1)
				}
			})
		},

		flush() {
			this.$confirm(`Are you sure you want to flush all products?`, {
				title: this.$t("messages.confirm-flush-product"),
			}).then((isAccepted) => {
				if (isAccepted) {
					this.isEnabledFlush = false
					this.$axios
						.$post(`/v1/admin/products/flush`)
						.then((response) => {
							this.$toast.success(`${this.$t("product.flushed")}`)
						})
						.catch((error) => {
							console.log(error)
						}).finally(() => {
							setTimeout(() => {
								this.isEnabledFlush = true
							}, 20000)
						})
				}
			})
		},
	},
}
</script>
<style>
/* stylelint-disable-next-line */
.product-description-wysiwyg .ProseMirror {
	min-height: 300px;
	max-height: 800px;
	overflow-y: auto;
}

ul {
	list-style-type: none;

	/* margin: 0;
	padding: 0; */
}
</style>
