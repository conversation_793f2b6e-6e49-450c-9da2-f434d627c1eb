<template>
	<page :title="$t('title.ratings')" :desc="$t('.desc')">
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/ratings">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->

				<template #filter="{ models }">
					<vc-select
						v-model="models.status"
						:label="$t('common.publish-status')"
						:items="statusRatingItems"
						clearable
					/>
				</template>
				<template #item.user.fullName="{ item }">
{{ item.user.firstName }} {{ item.user.lastName }}
</template>
				<template #item.status="{ item }">
					<status :items="statusRatingItems" :value="item.status" />
				</template>
				<template #item.product="{ item }">
					{{ item.productId }}
					<a
						:href="
							localePath({
								name: 'product',
								params: {
									productSlug: item.productId,
								},
							})
						"
					>
						<v-icon small left>mdi-open-in-new</v-icon>
					</a>
				</template>

				<template #item.rating="{ item }">
					<v-rating :value="item.rating" background-color="default" dense readonly small color="warning" length="5" />
				</template>

				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.ratingId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<div v-if="item.status === 'UnPublished'">
							<v-btn
								v-tooltip="$t('common.published')"
								class="mx-4"
								color="success"
								small
								icon
								@click="publishedRating(item.ratingId)"
							>
								<v-icon>mdi-checkbox-marked-circle</v-icon>
							</v-btn>
						</div>
						<div v-if="item.status === 'Published'">
							<v-btn v-tooltip="$t('common.unpublish')" text rounded color="error" @click="unpublishRating(item.ratingId)">
								<v-icon>mdi-close</v-icon>
							</v-btn>
						</div>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.rating')"
				api="/v1/admin/ratings?trans=off"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				hello
			</crud>
		</div>
	</page>
</template>

<script>
import { statusRating } from "~/config/Enum"
export default {
	name: "RatingsIndex",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "ratingId",
				},

				{
					text: this.$t("common.review"),
					sortable: true,
					value: "review",
				},
				{
					text: this.$t("common.reviewer"),
					sortable: true,
					value: "user.fullName",
				},

				{
					text: this.$t("common.rating"),
					sortable: true,
					value: "rating",
				},
				{
					text: this.$t("common.product"),
					sortable: true,
					value: "product",
				},
				{
					text: this.$t("common.status"),
					sortable: true,
					value: "status",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],

			defaultItem: {
				review: {},
				rating: {},
			},
		}
	},

	computed: {
		statusRatingItems() {
			return statusRating.map(rating => ({
				text: this.$t(rating.key),
				value: rating.value,
			}))
		},
	},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
		publishedRating(rating) {
			return this.$axios
				.$put("/v1/admin/ratings/" + rating + "/publishing")
				.then((res) => {
					this.$refs.dataTable.refresh()
				})
				.catch((error) => {
					console.log(error)
				})
		},
		unpublishRating(rating) {
			return this.$axios
				.$put("/v1/admin/ratings/" + rating + "/publishing")
				.then((res) => {
					this.$refs.dataTable.refresh()
				})
				.catch((error) => {
					console.log(error)
				})
		},
	},
}
</script>
