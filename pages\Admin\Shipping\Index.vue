<template>
	<page :tabs="tabs" :title="$t('title.shippings')">
		<template #actions>
			<div id="action" />
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	name: "ShippingsIndex",
	data() {
		return {
			tabs: [

				// {
				// 	text: this.$t("title.shippings-methods"),
				// 	icon: "mdi-package-variant-plus",
				// 	to: this.localePath({
				// 		name: "shippings-methods",
				// 		params: {
				// 			type: "",
				// 		},
				// 	}),
				// },
				{
					text: this.$t("title.shippings-carriers"),
					icon: "mdi-domain",
					to: this.localePath({
						name: "shippings-carriers",
						params: {
							type: "",
						},
					}),
				},
			],
		}
	},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new()
		},
	},
}
</script>
