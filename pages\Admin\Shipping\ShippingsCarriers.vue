<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon>
				<span class="text-capitalize"> {{ $t("common.new-shipping-carrier") }}</span>
			</v-btn>
		</teleport>

		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/shipping-carriers">
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.shippingCarrierId)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>

						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.shippingCarrierId)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>

						<v-btn v-tooltip="$t('common.set-shipping-for-all-product')" small icon :loading="isLoading"
							@click="setShippingForAllProduct(item.shippingCarrierId)">
							<v-icon small>
								mdi-vector-union
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud ref="crud" v-slot="{ item }" width="500" :default="defaultItem"
				:item-name="$t('common.shipping-carrier')" api="/v1/admin/shipping-carriers?trans=off"
				@updated="refreshAndClearCache" @created="refreshAndClearCache" @deleted="refreshAndClearCache">
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field :label="$t('common.name')" :dir="dir" :value="value" @input="update" v-on="on" />
				</translatable>
				<translatable v-slot="{ value, update, on, dir }" v-model="item.label">
					<vc-text-field hint="How the name will appear on site for clients" :label="$t('common.label')" :dir="dir"
						:value="value" @input="update" v-on="on" />
				</translatable>

				<vc-phone v-model="item.phone" :label="$t('common.phone')" />

				<v-list>
					<div class="d-flex">
						<v-subheader> Prices </v-subheader>
						<v-spacer />
						<v-btn color="primary" icon @click="item.prices.push($cloneDeep(defaultPrice))">
							<v-icon>mdi-plus</v-icon>
						</v-btn>
					</div>

					<v-lazy v-for="(priceItem, i) in item.prices" :key="`priceItem${i}`" min-height="88">
						<v-list-item three-line>
							<div class="d-flex">
								<vc-autocomplete v-model="priceItem.cityId" api="/v1/lookups-website/cities" />:
								<money-field v-model="priceItem.price" />
							</div>
							<v-list-item-action>
								<v-btn small icon>
									<v-icon small>
										mdi-pencil
									</v-icon>
								</v-btn>
								<v-btn small icon @click="removePriceItem(i)">
									<v-icon small>
										mdi-delete
									</v-icon>
								</v-btn>
							</v-list-item-action>
						</v-list-item>
					</v-lazy>
					<v-alert v-if="!item.prices.length" type="warning" text dense>
						You don't have any prices for this shipping carrier, please add at least one price.
					</v-alert>
				</v-list>

				<!-- <div v-if="item.address">
					<vc-text-field v-model="item.address.street" :label="$t('common.street')"></vc-text-field>
					<vc-phone v-model="item.address.phone" :label="$t('common.phone')"></vc-phone>
					<vc-text-field v-model="item.address.district" :label="$t('common.district')"></vc-text-field>
					<vc-text-field v-model="item.address.apartmentNumber" :label="$t('common.apartment-number')"> </vc-text-field>
					<vc-text-field v-model="item.address.buildingNumber" :label="$t('common.building-number')"></vc-text-field>
					<vc-autocomplete v-model="item.address.cityId" :label="$t('common.city')" api="/v1/lookups/cities"></vc-autocomplete>
				</div> -->
			</crud>
		</div>
	</div>
</template>

<script>
export default {
	name: "ShippingsCarriersIndex",
	data() {
		return {
			columns: [
				{
					text: "#",
					sortable: true,
					value: "shippingCarrierId",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.phone"),
					sortable: true,
					value: "phone.number",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},
				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},
				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				shippingCarrierId: 0,
				name: {},
				slug: {},
				prices: [],
				phone: {
					code: "962",
					number: "",
					iso: "JO",
				},
				label: {},
				updatedAt: null,
				createdAt: null,
			},
			defaultPrice: {
				price: {
					value: 0,
					// priceId: 2557,
					currencyId: 1,
				},
				cityId: 0,
			},
			isLoading: false,
		}
	},

	computed: {},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/shipping-carriers")
		},
		removePriceItem(i) {
			this.$confirm("Are you sure you want to delete this item?").then((isAccepted) => {
				if (!isAccepted) { return }
				this.$refs.crud.item.prices.splice(i, 1)
			})
		},
		setShippingForAllProduct(shippingCarrierId) {
			this.isLoading = true
			this.$confirm("are you sure want assagin this shiping for all product?").then((isAccepted) => {
				if (!isAccepted) {
					this.isLoading = false
					return 0
				}
				this.$axios
					.$post(`/v1/admin/shipping-carriers/${shippingCarrierId}/set-default-for-all`)
					.then((res) => {
						this.$toast.success(`Shipping has been updated successfully`)
						this.isLoading = false
					})
					.catch((e) => {
						this.genericErrorHandler(e, this.$refs.crudDrawer.$form)
					})
			})
		},
	},
}
</script>
