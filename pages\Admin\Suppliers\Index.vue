<template>
	<page :title="$t('title.suppliers')" :desc="$t('.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-supplier") }}</span>
			</v-btn>
		</template>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/suppliers">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.supplierId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.supplierId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.supplier')"
				api="/v1/admin/suppliers?trans=off"
				model="supplier"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field
						:label="$t('common.name')"
						:rules="[$rules.required($t('common.name'))]"
						:dir="dir"
						:value="value"
						@input="update"
						v-on="on"
					/>
				</translatable>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	name: "SuppliersIndex",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "supplierId",
				},

				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/suppliers")
		},
	},
}
</script>
