<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon><span class="text-capitalize">{{ $t("common.new-banner") }}</span>
			</v-btn>
		</teleport>

		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/banners">
				<template #item.media="{ item }">
					<thumbnail :width="80" :aspect-ratio="4 / 3" :src="item.media.image?.preview" />
				</template>

				<template #item.isPublished="{ item }">
					<check-mark :value="item.isPublished" />
				</template>
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.bannerId)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.bannerId)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud ref="crud" v-slot="{ item }" width="500" :default="defaultItem" :item-name="$t('common.banner')"
				api="/v1/admin/banners?trans=off" @updated="refreshAndClearCache" @created="refreshAndClearCache"
				@deleted="refreshAndClearCache">
				<upload v-model="item.media.image" label="Image" :rules="[$rules.required($t('common.media'))]" :max="1"
					width="100" />
				<vc-text-field :prefix="baseUrl" v-model="item.url" :label="$t('common.url')"
					:rules="[$rules.required($t('common.url'))]" />

				<publish-field v-model="item.isPublished" :published-at.sync="item.publishedAt"
					:expires-at.sync="item.unPublishedAt" />
			</crud>
		</div>
	</div>
</template>

<script>
export default {
	name: "BannerIndex",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "bannerId",
				},

				{
					text: this.$t("common.media"),
					sortable: true,
					value: "media",
				},
				{
					text: this.$t("common.url"),
					sortable: true,
					value: "url",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},
				{
					text: this.$t("common.isPublished"),
					sortable: false,
					value: "isPublished",
				},
				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				url: null,
				extra: {},
				isPublished: true,
				publishedAt: null,
				unPublishedAt: null,
				media: {
					image: {},
				},
			},
		}
	},

	computed: {
		baseUrl() {
			return process.env.NUXT_ENV_BASE_URL
		},
	},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/banners")
		},
	},
}
</script>
