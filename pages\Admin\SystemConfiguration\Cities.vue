<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon><span class="text-capitalize">{{ $t("common.new-city") }}</span>
			</v-btn>
		</teleport>

		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/cities">
				<template #filter="{ models }">
					<vc-select v-model="models.countryId" api="/v1/lookups/countries" :label="$t('common.country')" />
				</template>

				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.cityId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.cityId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.city')"
				api="/v1/admin/cities?trans=off"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field
						:label="$t('common.name')"
						:rules="[$rules.required($t('common.name'))]"
						:value="value"
						:dir="dir"
						@input="update"
						v-on="on"
					/>
				</translatable>
				<vc-autocomplete
					v-model="item.countryId"
					:label="$t('common.country')"
					:rules="[$rules.required($t('common.country-id'))]"
					api="/v1/lookups/countries"
				/>
				<vc-text-field
					v-model="item.codeCity"
					:label="$t('common.code-city')"
					:rules="[$rules.required($t('common.code-city'))]"
				/>
			</crud>
		</div>
	</div>
</template>

<script>
export default {
	name: "CitiesIndex",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "cityId",
				},

				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.code-city"),
					sortable: true,
					value: "codeCity",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
				codeCity: null,
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/cities")
		},
	},
}
</script>
