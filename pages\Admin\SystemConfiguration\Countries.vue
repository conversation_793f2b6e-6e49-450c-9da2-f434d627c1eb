<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-country") }}</span>
			</v-btn>
		</teleport>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/countries">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.countryId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.countryId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.country')"
				api="/v1/admin/countries?trans=off"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field
						:label="$t('common.name')"
						:rules="[$rules.required($t('common.name'))]"
						:value="value"
						:dir="dir"
						@input="update"
						v-on="on"
					/>
				</translatable>
				<vc-text-field
					v-model="item.iSOCode"
					:label="$t('common.iso-code')"
					:rules="[$rules.required($t('common.iso-code'))]"
				/>
				<vc-text-field
					v-model="item.phoneCode"
					:label="$t('common.phone-code')"
					:rules="[$rules.required($t('common.phone-code'))]"
				/>
			</crud>
		</div>
	</div>
</template>

<script>
export default {
	name: "CountriesIndex",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "countryId",
				},

				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.iso-code"),
					sortable: true,
					value: "iSOCode",
				},

				{
					text: this.$t("common.phone-code"),
					sortable: true,
					value: "phoneCode",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
				phoneCode: null,
				iSOCode: null,
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/countries")
		},
	},
}
</script>
