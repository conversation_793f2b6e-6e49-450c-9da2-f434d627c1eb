<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-currency") }}</span>
			</v-btn>
		</teleport>

		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/currencies">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.currencyId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.currencyId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.currency')"
				api="/v1/admin/currencies"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<vc-select
					v-model="item.symbol"
					:items="symbolsTypesItems"
					:label="$t('common.symbol')"
					:rules="[$rules.required($t('common.symbol'))]"
				/>
				<vc-select
					v-model="item.name"
					:items="currenciesTypesItems"
					:label="$t('common.currency')"
					:rules="[$rules.required($t('common.currency'))]"
				/>
				<vc-text-field
					v-model="item.valueToBasePrice"
					:label="$t('common.value-to-base-price')"
					:rules="[$rules.required($t('common.value-to-base-price'))]"
				/>
			</crud>
		</div>
	</div>
</template>

<script>
import { currenciesTypesEnum, symbolsTypesEnum } from "~/config/Enum"

export default {
	name: "CurrenciesIndex",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "currencyId",
				},
				{
					text: this.$t("common.currency"),
					sortable: true,
					value: "name",
				},
				{
					text: this.$t("common.symbol"),
					sortable: true,
					value: "symbol",
				},

				{
					text: this.$t("common.value-to-base-price"),
					sortable: true,
					value: "valueToBasePrice",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: null,
				symbol: null,
				valueToBasePrice: null,
			},
		}
	},

	computed: {
		currenciesTypesItems() {
			return currenciesTypesEnum.map(currency => ({
				text: this.$t(currency.key),
				value: currency.value,
			}))
		},
		symbolsTypesItems() {
			return symbolsTypesEnum.map(symbol => ({
				text: this.$t(symbol.key),
				value: symbol.value,
			}))
		},
	},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/currencies")
		},
	},
}
</script>
