<template>
	<page :tabs="tabs" :title="$t('title.system-configuration')">
		<template #actions>
			<div id="action" />
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	name: "SystemConfigurationIndex",
	data() {
		return {
			tabs: [
				{
					text: this.$t("title.currencies"),
					icon: "mdi-message-text",
					to: this.localePath({
						name: "currencies",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("title.payments-methods"),
					icon: "mdi-message-text",
					to: this.localePath({
						name: "paymentsMethods",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("title.countries"),
					icon: "mdi-domain",
					to: this.localePath({
						name: "countries",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("title.cities"),
					icon: "mdi-domain",
					to: this.localePath({
						name: "cities",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("title.banners"),
					icon: "mdi-banner",
					to: this.localePath({
						name: "banners",
						params: {
							type: "",
						},
					}),
				},
			],
		}
	},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new()
		},
	},
}
</script>
