<template>
	<div>
		<div class="row">
			<div class="col-md-6">
				<teleport to="#action">
					<v-btn color="primary" class="text-lowercase" @click="updateSortableAttributes()">
						<v-icon left>
							mdi-update
						</v-icon><span class="text-capitalize">{{ $t("common.update-sortable-attributes") }}</span>
					</v-btn>
				</teleport>
				<data-table ref="dataTableSortableAttributes" :columns="columnsSortable"
					api="/v1/admin/settings/meilisearch/sortable-attributes-products">
					<template #item="{ item }">
						{{ item }}
					</template>
				</data-table>
			</div>
			<div class="col-md-6">
				<teleport to="#action">
					<v-btn color="primary" class="text-lowercase" @click="updateFilterableAttributes()">
						<v-icon left>
							mdi-update
						</v-icon><span class="text-capitalize">{{ $t("common.update-filterable-attributes") }}</span>
					</v-btn>
				</teleport>
				<data-table ref="dataTableFilterableAttributes" :columns="columnsFilterable"
					api="/v1/admin/settings/meilisearch/filterable-attributes-products">
					<template #item="{ item }">
						{{ item }}
					</template>
				</data-table>
			</div>
		</div>
	</div>
</template>

<script>

export default {
	name: "MailsearchIndex",
	data() {
		return {
			value: {},
			columnsSortable: [
				{
					text: this.$t("common.key"),
					value: "key",
				},
			],
			columnsFilterable: [
				{
					text: this.$t("common.key"),
					value: "key",
				},
			],
		}
	},

	computed: {
		baseUrl() {
			return process.env.NUXT_ENV_BASE_URL
		},
	},
	watch: {},
	methods: {
		updateSortableAttributes() {
			this.$axios.post("/v1/admin/settings/meilisearch/sortable-attributes-products")
				.then(() => {
					this.$refs.dataTableSortableAttributes.refresh()
				})
		},
		updateFilterableAttributes() {
			this.$axios.post("/v1/admin/settings/meilisearch/filterable-attributes-products")
				.then(() => {
					this.$refs.dataTableFilterableAttributes.refresh()
				})
		},
	},

}
</script>
