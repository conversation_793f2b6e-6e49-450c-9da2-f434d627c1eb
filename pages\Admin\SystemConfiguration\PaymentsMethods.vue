<template>
	<div>
		<teleport to="#action">
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize">{{ $t("common.new-payments-methods") }}</span>
			</v-btn>
		</teleport>

		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/payments-methods">
				<template #filter="{ models }">
					<vc-select v-model="models.status" :label="$t('common.status')" :items="statusPaymentMethodItems" clearable />
				</template>

				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.media="{ item }">
					<thumbnail v-if="item.media.logo?.preview" contain :width="80" :aspect-ratio="4 / 3" :src="item.media.logo.preview" />
				</template>

				<template #item.status="{ item }">
					<status :items="statusPaymentMethodItems" :value="item.status" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon @click="$refs.crud.delete(item.paymentMethodId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.paymentMethodId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
						<div v-if="item.status === 'inactive'">
							<v-btn
								v-tooltip="$t('common.active')"
								class="mx-4"
								color="success"
								small
								icon
								@click="activePaymentMethod(item.paymentMethodId)"
							>
								<v-icon>mdi-checkbox-marked-circle</v-icon>
							</v-btn>
						</div>
						<div v-if="item.status === 'active'">
							<v-btn
								v-tooltip="$t('common.inactive')"
								text
								rounded
								color="error"
								@click="inactivePaymentMethod(item.paymentMethodId)"
							>
								<v-icon>mdi-close</v-icon>
							</v-btn>
						</div>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.payment-method')"
				api="/v1/admin/payments-methods?trans=off"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
					<vc-text-field
						:label="$t('common.name')"
						:rules="[$rules.required($t('common.name'))]"
						:value="value"
						:dir="dir"
						@input="update"
						v-on="on"
					/>
				</translatable>
				<vc-text-field
					v-model="item.module"
					:label="$t('common.module')"
					:rules="[$rules.required($t('common.module'))]"
				/>
				<vc-radio
					v-model="item.status"
					:label="$t('common.status')"
					:items="statusPaymentMethodItems"
					:rules="[$rules.required($t('common.status'))]"
				/>
				<upload v-model="item.media.logo" single :max="1" width="340" />
			</crud>
		</div>
	</div>
</template>

<script>
import { statusPaymentMethod } from "~/config/Enum"

export default {
	name: "PaymentsMethodsIndex",
	data() {
		return {
			columns: [
				{
					text: "#",
					sortable: true,
					value: "paymentMethodId",
				},
				{
					text: this.$t("common.media"),
					sortable: true,
					value: "media",
				},
				{
					text: this.$t("common.name"),
					sortable: true,
					value: "name",
				},

				{
					text: this.$t("common.module"),
					sortable: true,
					value: "module",
				},
				{
					text: this.$t("common.status"),
					sortable: true,
					value: "status",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},
				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},
				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],

			defaultItem: {
				name: {},
				status: null,
				module: null,
				media: {
					logo: {},
				},
			},
		}
	},

	computed: {
		statusPaymentMethodItems() {
			return statusPaymentMethod.map(paymentMethod => ({
				text: this.$t(paymentMethod.key),
				value: paymentMethod.value,
			}))
		},
	},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/paymentMethod")
		},

		activePaymentMethod(paymentMethodId) {
			return this.$axios
				.$put("/v1/admin/payments-methods/" + paymentMethodId + "/active-payment-method")
				.then((res) => {
					this.$refs.dataTable.refresh()
					this.$store.dispatch("cache/clearKey", "/v1/lookups/paymentMethod")
				})
				.catch((error) => {
					console.log(error)
				})
		},
		inactivePaymentMethod(paymentMethodId) {
			return this.$axios
				.$put("/v1/admin/payments-methods/" + paymentMethodId + "/inactive-payment-method")
				.then((res) => {
					this.$refs.dataTable.refresh()
				})
				.catch((error) => {
					console.log(error)
				})
		},
	},
}
</script>
