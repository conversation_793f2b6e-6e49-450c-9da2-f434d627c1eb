<template>
	<page v-model="tabsModel" exact :tabs="tabs" :title="$t('title.translations')"
		:desc="$t('common.translations-locale')">
		<template #actions>
			<v-btn outlined color="primary" class="text-lowercase" @click="$refs.comparison.new()">
				<v-icon left>
					mdi-compare-horizontal
				</v-icon>
				<span class="text-capitalize"> {{ $t("common.bulk-update") }}</span>
			</v-btn>
			<!-- <v-btn outlined color="primary" @click="importAndExportModel = true"> <v-icon left>mdi-import</v-icon>Import / Export</v-btn> -->
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
					mdi-plus
				</v-icon>
				<span class="text-capitalize"> {{ $t("common.new-translation") }} </span>
			</v-btn>
		</template>

		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/portal/translations"
				:ignore-models-badge="['abbreviation', 'is_missing']">
				<template #prepend-action="{ models, filter }">
					<v-switch v-model="models.is_missing" hide-details :true-value="true" :false-value="null" color="primary"
						class="me-2" :label="$t('common.show-only-missing')" @change="filter">
						Show only missing {{ filter }}
					</v-switch>
				</template>
				<template #filter="{ models, options }">
					<vc-select v-model="models.group" :label="$t('common.by-group')" :items="options.group" clearable
						@clear="models.group = undefined" />
					<vc-select v-model="models.isPublished" :label="$t('common.publish-status')" :items="publishOptions"
						clearable @clear="models.isPublished = undefined" />
				</template>

				<template #item.fullKey="{ item }">
					<v-chip label dark small class="text-lower" @click="copyKey(item)">
						{{ item.group }}.{{ item.key }}
						<v-icon x-small right>
							mdi-content-copy
						</v-icon>
					</v-chip>
				</template>

				<template #item.value="{ item }">
					<div v-for="language in item.languages.filter((item) => !!item.value)"
						:key="`found-${language.abbreviation}-${language.value}`">
						<div class="line-clamp-1">
							<template v-if="!$route.query.abbreviation">
								<b class="text-uppercase">{{ language.abbreviation }}</b>
								:
							</template>
							{{ language.value ? language.value : "N/A" }}
						</div>
					</div>
					<div v-if="item.languages.filter((item) => !item.value).length" class="warning--text">
						<b>Missing</b>:
						<span v-for="language in item.languages.filter((item) => !item.value)"
							:key="`missing-${language.abbreviation}-${language.value}`" class="text-uppercase">
							{{ language.abbreviation }}
						</span>
					</div>
				</template>
				<template #item.tags="{ item }">
					<div v-if="item.tags" class="d-flex align-center">
						<v-chip v-for="(tag, key) in item.tags" :key="key" label dark outlined small color="primary"
							class="text-uppercase me-1 mb-1">
							{{ tag.text }}
						</v-chip>
					</div>
				</template>
				<template #item.locales="{ item }">
					<v-chip-group>
						<v-chip v-for="locale in item.locales" :key="'available-locale-' + locale" label small
							class="text-uppercase">
							{{ locale }}
						</v-chip>
					</v-chip-group>
				</template>

				<template #item.missing="{ item }">
					<v-chip v-for="locale in item.missing" :key="'missing-locale-' + locale" width="40" label small
						class="text-uppercase">
						{{ locale }}
					</v-chip>
				</template>

				<template #item.publishedAt="{ item }">
					{{ item.publishedAt | dateTime }}
				</template>
				<template #item.isPublished="{ item }">
					<status :items="isPublishedItems" :value="item.isPublished" />
				</template>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>
								mdi-pencil
							</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>
								mdi-delete
							</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud ref="crud" v-slot="{ item, isEdit }" width="1000" :default="defaultItem"
				:item-name="$t('common.translation')" api="/v1/admin/portal/translations" @updated="updateHandler"
				@created="updateHandler" @deleted="refresh" @loaded="OriginalKeyHandler">
				<v-row class="fill-height no-wrap">
					<v-col md="8">
						<section v-if="!isEdit || isEditAllowed" class="pb-0">
							<label>{{ $t("common.key") }}: </label>
							<div class="d-flex align-baseline">
								<translations-group ref="group" v-model="item.group" class="me-4" />
								<!-- <translations-sub-group v-model="item.sub_group" :group="item.group" /> -->
								<translations-key ref="key" v-model="item.key" :group="item.group" />
								<v-divider vertical inset class="ma-2" />

								<v-btn color="primary" text @click="pasteHandler">
									<v-icon left>
										mdi-content-paste
									</v-icon> {{ $t("common.paste") }}
								</v-btn>
							</div>
						</section>

						<section class="mt-0 pt-0">
							<vc-radio v-model="item.type" item-text="text" item-value="value" :items="itemTypes" row
								:label="$t('common.key-type')" @input="changeKeyType(item)" />
						</section>

						<section>
							<translations-full-key ref="fullKey" v-model="isEditAllowed" :item-key="item.key"
								:item-group="item.group" :edit-mode="isEdit" />
						</section>

						<section>
							<label>{{ $t("common.translations") }} : </label>

							<translations-placeholders :placeholders="item.placeholders" @add="item.placeholders.push($event)"
								@insert="insertPlaceHolder" @delete="deletePlaceHolder" />
							<translations-editor ref="editor" :item="item" />
						</section>
					</v-col>
					<v-divider vertical />
					<v-col cols="4">
						<section class="d-flex justify-space-between align-center">
							<span class="text-body-2"> {{ $t("common.is-published") }} ?</span>
							<translations-is-published v-model="item.isPublished" class="my-0" />
						</section>
						<section>
							<label>{{ $t("common.show-on") }}</label>
							<!-- <vc-chips v-model="item.tags" :rules="[$rules.required('common.tags')]" :multiple="true" api="v1/lookups/translation-tags"></vc-chips>-->
							<translations-tags v-model="item.tags" :rules="[$rules.required('common.tags')]" />
						</section>
					</v-col>
				</v-row>
			</crud>
			<translations-bulk-update ref="comparison" @updated="refresh" />
		</div>
	</page>
</template>

<script>
import responsive from "~/mixins/responsive"
import translations from "~/mixins/translations"
import permissions from "~/mixins/permissions.js"
import {
	isPublished,
	translationTypesEnum,
} from "~/config/Enum"
// const json = require("~/locales/en.json");
export default {
	permission: "accounts",
	mixins: [responsive, translations, permissions],
	data() {
		return {
			isPublishedItems: isPublished,
			itemTypes: translationTypesEnum,
			permissions: [],
			value: {},
			columns: [{
				text: "#",
				align: "start",
				sortable: false,
				value: "id",
			},
			{
				text: this.$t('common.key'),
				value: "fullKey",
				searchable: true,
				// permission: this.$gates.hasAllPermissions("static_translations.view.group|static_translations.view.key"),
			},
			/*  {
				text: "Target",
				value: "exception",
			},
			*/

			{
				text: this.$t('common.content'),
				value: "value",
				searchable: true,
				// permission: this.$gates.hasPermission("languages.view"),
			},

			{
				text: this.$t('common.tags'),
				value: "tags",
				searchable: true,
			},

			{
				text: this.$t('common.published'),
				value: "isPublished",
			},
			{
				text: this.$t('common.action'),
				value: "actions",
			},
			],
			defaultItem: {
				group: "common",
				key: null,
				languages: [],
				tags: [],
				type: translationTypesEnum[0].value,
				placeholders: [],
				isPublished: true,
				// exception: null,
			},
			editKey: false,
			isEditAllowed: false,
			groupByModel: false,
			tabsModel: this.localePath({
				name: "translations",
				query: {
					...this.$route.query,
					abbreviation: this.$route.query.abbreviation,
				},
			}),

			publishOptions: [
				//	{ text: "All", value: undefined },
				{
					text: "Published",
					value: true,
				},
				{
					text: "Unpublished",
					value: false,
				},
			],
			importAndExportModel: false,
			languages: [],
		}
	},
	async fetch() {
		await this.$axios.$getOnce("/v1/lookups/languages?active=true").then((res) => {
			const languages = res.map((item) => {
				return {
					language_id: item.value,
					abbreviation: item.meta.abbreviation,
					value: null,
					direction: item.meta.direction,
					countable: item.meta.countable,
				}
			})

			this.languages = res.map(item => ({
				text: item.text,
				img: "/images/languages/" + item.meta.abbreviation + ".svg",
				abbreviation: item.meta.abbreviation,
				countable: item.meta.countable,
				isActive: this.$route.query.abbreviation === item.value,
				to: this.localePath({
					name: this.getRouteBaseName(),
					query: {
						...this.$route.query,
						abbreviation: item.meta.abbreviation,
					},
				}),
			}))

			this.$set(this.defaultItem, "languages", languages)
		})
	},

	computed: {
		tabs() {
			return [{
				text: "All",
				icon: "mdi-earth",
				to: this.localePath({
					name: this.getRouteBaseName(),
					query: {
						...this.$route.query,
						abbreviation: undefined,
					},
				}),
				isActive: !this.$route.query.abbreviation,
			},

			...this.languages,
			]
		},
	},
	watch: {
		"$route.query.abbreviation"(type) {
			this.$refs.dataTable.models.abbreviation = type
			// this.$refs.dataTable.filterValues.is_published = undefined;
			this.$refs.dataTable.refresh()
		},
	},
	methods: {
		changeKeyType(item) {
			const labelToAdd = {
				menuModel: false,
				text: "Count",
				value: "count",
			}
			const index = item.placeholders.findIndex(item => item.value === labelToAdd.value)
			if (index < 0 && item.type === "countable") {
				// If label not found but item is countable add label.
				item.placeholders.push(labelToAdd)
			}

			if (index >= 0 && item.type !== "countable") {
				// If label found and item is not countable remove label.
				item.placeholders.splice(index, 1)
			}
		},
		setKey(item) {
			const keys = item.key.split(".")
			if (keys.length === 3) {
				item.newKey.key1 = keys[0]
				item.newKey.key2 = keys[1]
				item.newKey.key3 = keys[2]
			} else if (keys.length === 2) {
				item.newKey.key1 = keys[0]
				item.newKey.key2 = keys[1]
			} else if (keys.length === 1) {
				item.newKey.key1 = keys[0]
			}
		},
		// async toggleMissing(v) {
		// 	const isNavigatedPrevented = await this.$router.push(
		// 		this.localePath({ name: "translations-locale", params: { ...this.$route.params }, query: { missing: v || undefined } })
		// 	);

		// 	if (!isNavigatedPrevented) {
		// 		this.$refs?.dataTable?.refresh();
		// 	}
		// },
		editTranslation(item) {
			// this.tabsModel = this.$store.getters.languages.findIndex((locale) => locale.abbreviation === item.abbreviation);
			this.$refs.crud.edit(item.id)
		},

		insertPlaceHolder(value) {
			this.$refs.editor.insertPlaceHolder(value)
		},
		OriginalKeyHandler(item) {
			this.$refs.fullKey?.setOriginalKey()
		},
		updateHandler(item) {
			this.$refs.dataTable.refresh()
			this.copyKey(item)
		},
		refresh() {
			this.$refs.dataTable.refresh()
		},
	},
}
</script>
