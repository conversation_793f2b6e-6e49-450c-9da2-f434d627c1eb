<template>
	<page v-model="tabsModel" exact :tabs="tabs" :title="$t('title.translations-tags')" :desc="$t('portal.translations-tags.desc')">
		<template #actions>
			<v-btn color="primary" class="text-lowercase" @click="$refs.crud.new()">
				<v-icon left>
mdi-plus
</v-icon>
				<span class="text-capitalize"> {{ $t("common.new-translation-tag") }}</span>
			</v-btn>
		</template>

		<div>
			<data-table
				ref="dataTable"
				:columns="columns"
				api="/v1/admin/portal/translation-tags"
				:ignore-models-badge="['abbreviation', 'is_missing']"
			>
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.translation-tag')"
				api="/v1/admin/portal/translation-tags"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<v-row class="fill-height no-wrap">
					<v-col md="12">
						<vc-text-field
							v-model="item.name"
							name="name"
							:label="$t('common.name')"
							:rules="[$rules.required($t('static-translation-tags.name'))]"
						/>
					</v-col>
				</v-row>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: this.$t("common.name"),
					value: "name",
					searchable: true,
				},
				{
					text: this.$t("common.created-at"),
					value: "createdAt",
					searchable: true,
				},
				{
					text: this.$t("common.updated-at"),
					value: "updatedAt",
					searchable: true,
				},
				{
					text: this.$t("common.action"),
					value: "actions",
				},
			],
			defaultItem: {
				name: null,
			},
		}
	},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/translation-tags")
		},
	},
}
</script>
