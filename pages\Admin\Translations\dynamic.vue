<template>
	<page v-model="tabsModel" exact :tabs="tabs" :title="$t('translations-tags.title')" :desc="$t('portal.translations-tags.desc')">
		<template #actions>
			<v-btn color="primary" @click="$refs.crud.new()">
<v-icon left>
mdi-plus
</v-icon>New Key
</v-btn>
		</template>

		<div>
			<data-table
				ref="dataTable"
				:columns="columns"
				api="/v1/admin/portal/translation-tags"
				:ignore-models-badge="['abbreviation', 'is_missing']"
			>
				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.translation')"
				api="/v1/admin/portal/translation-tags"
				@updated="updateHandler"
				@created="updateHandler"
				@deleted="refresh"
			>
				<v-row class="fill-height no-wrap">
					<v-col md="12">
						<vc-text-field
							v-model="item.name"
							name="name"
							:label="$t('static-translation-tags.name')"
							:rules="[$rules.required($t('static-translation-tags.name'))]"
						/>
					</v-col>
				</v-row>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "common.name",
					value: "name",
					searchable: true,
				},
				{ text: "common.created-at", value: "created_at", searchable: true },
				{ text: "common.updated-at", value: "updated_at", searchable: true },
				{ text: "", value: "actions" },
			],
			defaultItem: {
				name: null,
			},
		}
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
	},
}
</script>
