<template>
	<div>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/deleted">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.status="{ item }">
					<status :items="isStatusItems" :value="item.status" />
				</template>

				<template #item.media="{ item }">
					<avatar :width="30" :aspect-ratio="4 / 3" :src="item.media.avatar?.preview" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.edit')" small icon @click="$refs.crud.edit(item.userId)">
							<v-icon small>
mdi-pencil
</v-icon>
						</v-btn>
						<v-btn v-tooltip="$t('common.restore')" small icon @click="restoreUser(item.userId)">
							<v-icon small>
mdi-arrow-up-bold-box-outline
</v-icon>
						</v-btn>
					</div>
				</template>
			</data-table>
			<crud
				ref="crud"
				v-slot="{ item }"
				width="500"
				:default="defaultItem"
				:item-name="$t('common.user')"
				api="/v1/admin/deleted"
				@updated="refreshAndClearCache"
				@created="refreshAndClearCache"
				@deleted="refreshAndClearCache"
			>
				<vc-text-field
					v-model="item.firstName"
					:label="$t('common.first-name')"
					:rules="[$rules.required($t('common.first-name'))]"
				/>
				<vc-text-field
					v-model="item.lastName"
					:label="$t('common.last-name')"
					:rules="[$rules.required($t('common.last-name'))]"
				/>
				<vc-password
					v-model="item.password"
					name="password"
					no-strength
					:rules="[$rules.required($t('common.password')), $rules.minLength($t('common.password'), 8)]"
					:label="$t('common.password')"
				/>
				<vc-password
					v-model="item.passwordConfirmation"
					name="passwordConfirmation"
					no-strength
					:rules="[$rules.required($t('common.password-confirmation')), $rules.minLength($t('common.password-confirmation'), 8)]"
					:label="$t('common.password-confirmation')"
				/>
				<vc-text-field
					v-model="item.email"
					name="email"
					:label="$t('common.email')"
					:rules="[$rules.required($t('common.email')), $rules.email($t('common.email'))]"
				/>
				<vc-phone v-model="item.phone" outlined :label="$t('common.phone')" />
				<vc-date-time
					v-model="item.birthday"
					:text-field-props="{ clearable: true, outlined: true, dense: true }"
					append-icon="mdi-calendar"
					:label="$t('common.birthday')"
				/>
				<vc-select
					v-model="item.gender"
					:rules="[$rules.required($t('common.gender'))]"
					:items="isGendersItems"
					:label="$t('common.gender')"
				/>
				<vc-radio
					v-model="item.status"
					:label="$t('common.status')"
					:items="isStatusItems"
					:rules="[$rules.required($t('common.status'))]"
				/>

				<upload v-model="item.media.avatar" :max="1" width="340" />
			</crud>
		</div>
	</div>
</template>

<script>
import { statusUser, genders } from "~/config/Enum"
export default {
	name: "DeletedUsersIndex",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "userId",
				},
				{
					text: this.$t("common.media"),
					sortable: true,
					value: "media",
				},

				{
					text: this.$t("common.full-name"),
					sortable: true,
					value: "fullName",
				},

				{
					text: this.$t("common.email"),
					sortable: true,
					value: "email",
				},
				{
					text: this.$t("common.phone"),
					sortable: true,
					value: "phone.number",
				},
				{
					text: this.$t("common.status"),
					sortable: false,
					value: "status",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				lastName: null,
				firstName: null,
				password: null,
				passwordConfirmation: null,
				status: null,
				email: null,
				gender: null,
				birthday: null,
				phone: {
					code: "962",
					number: null,
					iso: "JO",
				},
				media: {
					avatar: {},
				},
			},
		}
	},

	computed: {
		isStatusItems() {
			return statusUser.map(status => ({
				text: this.$t(status.key),
				value: status.value,
			}))
		},

		isGendersItems() {
			return genders.map(gender => ({
				text: this.$t(gender.key),
				value: gender.value,
			}))
		},
	},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/users")
		},

		restoreUser(userId) {
			return this.$axios
				.$post("/v1/admin/users/" + userId + "/restore")
				.then((res) => {
					this.$refs.dataTable.refresh()
					this.$toast.success(this.$t("common.user-has-been-restore-successfully"))
				})
				.catch((error) => { this.genericErrorHandler(error) })
		},
	},
}
</script>
