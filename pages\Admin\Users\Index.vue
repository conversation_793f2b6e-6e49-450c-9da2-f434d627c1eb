<template>
	<page :tabs="tabs" :title="$t('title.users')">
		<template #actions>
			<div id="action" />
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	name: "UsersIndex",
	data() {
		return {
			tabs: [
				{
					text: this.$t("common.customers"),
					icon: "mdi-account-circle",
					to: this.localePath({
						name: "customers",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("common.request-change-name"),
					icon: "mdi-checkbox-marked-circle",
					to: this.localePath({
						name: "requestChangeName",
						params: {
							type: "",
						},
					}),
				},
				{
					text: this.$t("common.deleted-users"),
					icon: "mdi-account-circle",
					to: this.localePath({
						name: "deletedUsers",
						params: {
							type: "",
						},
					}),
				},
			],
		}
	},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new()
		},
	},
}
</script>
