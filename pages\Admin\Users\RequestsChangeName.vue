<template>
	<div>
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/admin/requests">
				<!-- <template #prepend-action="{ models, filter }">
                </template> -->
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>

				<template #item.updatedAt="{ item }">
					{{ item.updatedAt | dateTime }}
				</template>

				<template #item.status="{ item }">
					<status :items="statusRequestsItems" :value="item.status" />
				</template>

				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="$t('common.delete')" small icon
							@click="$refs.crud.delete(item.requestsChangeNameId)">
							<v-icon small>
mdi-delete
</v-icon>
						</v-btn>
						<div v-if="item.status === 'pending'">
							<v-btn v-tooltip="$t('common.active')" class="mx-4" color="success" small icon
								@click="activeRequest(item.requestsChangeNameId)">
								<v-icon>mdi-checkbox-marked-circle</v-icon>
							</v-btn>
						</div>
						<div v-if="item.status === 'active'">
							<v-btn v-tooltip="$t('common.pending')" text rounded color="error"
								@click="pendingRequest(item.requestsChangeNameId)">
								<v-icon>mdi-close</v-icon>
							</v-btn>
						</div>
					</div>
				</template>
			</data-table>

			<crud ref="crud" width="500" :default="defaultItem" :item-name="$t('common.request-change-name')"
				api="/v1/admin/requests?trans=off" @updated="refreshAndClearCache" @created="refreshAndClearCache"
				@deleted="refreshAndClearCache" />
		</div>
	</div>
</template>

<script>
import { statusRequestsChangeName } from "~/config/Enum"

export default {
	name: "RequestsChangeNameIndex",

	data() {
		return {
			value: {},
			statusRequestsItems: statusRequestsChangeName,
			columns: [
				{
					text: "#",
					sortable: true,
					value: "requestsChangeNameId",
				},

				{
					text: this.$t("common.full-name"),
					sortable: true,
					value: "fullName",
				},

				{
					text: this.$t("common.first-name"),
					sortable: true,
					value: "firstName",
				},

				{
					text: this.$t("common.last-name"),
					sortable: true,
					value: "lastName",
				},

				{
					text: this.$t("common.status"),
					sortable: false,
					value: "status",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.updated-at"),
					sortable: false,
					value: "updatedAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {
				name: {},
			},
		}
	},

	computed: {},
	watch: {},

	methods: {
		refreshAndClearCache() {
			this.$refs.dataTable.refresh()
			this.$store.dispatch("cache/clearKey", "/v1/lookups/users")
		},
		activeRequest(request) {
			return this.$axios
				.$post(`/v1/admin/request-change/${request}/activation`)
				.then((res) => {
					this.$refs.dataTable.refresh()
				})
				.catch((error) => {
					console.log(error)
				})
		},
		pendingRequest(request) {
			return this.$axios
				.$post(`/v1/admin/request-change/${request}/activation`)
				.then((res) => {
					this.$refs.dataTable.refresh()
				})
				.catch((error) => {
					console.log(error)
				})
		},
	},
}
</script>
