<template>
	<v-container>
		<div class="mt-6">
			<h2 class="text-center mb-8">
{{ $t("common.our-branches") }}
</h2>
			<v-row>
				<v-col v-for="(branch, key) in branches" :key="key" cols="12" sm="3" md="4" lg="4" xl="3">
					<branch-card :item="branch" :loading="isFetching" />
				</v-col>
			</v-row>
		</div>
	</v-container>
</template>

<script>
export default {
	name: "Branches",
	layout: "website",
	data() {
		return {
			branches: [],
			isFetching: false,
		}
	},
	async fetch() {
		await this.$axios.$get(`/v1/branches`).then((resp) => {
			this.branches = resp
		})
	},
}
</script>

<style lang="scss">
</style>
