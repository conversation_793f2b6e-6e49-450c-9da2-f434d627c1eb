<template>
	<v-stepper v-model="stepModel" alt-labels flat elevation="0" tile>
		<v-stepper-header>
			<v-stepper-step :color="(stepModel > 1 && 'success') || undefined" :step="1" editable :complete="stepModel > 1"
				edit-icon="mdi-check">
				{{ $t("order.contact-info") }}
			</v-stepper-step>

			<v-divider />

			<v-stepper-step :color="(stepModel > 2 && 'success') || undefined" :step="2" :editable="stepModel >= 2"
				:complete="stepModel > 2" edit-icon="mdi-check">
				{{ $t("order.shipping") }}
			</v-stepper-step>

			<v-divider />

			<v-stepper-step :color="(stepModel > 3 && 'success') || undefined" :step="3" :editable="stepModel >= 3"
				:complete="stepModel > 3" edit-icon="mdi-check">
				{{ $t("order.payment") }}
			</v-stepper-step>
		</v-stepper-header>
		<v-stepper-items>
			<v-stepper-content :step="1" class="px-0">
				<v-lazy>
					<checkout-contact-info :order="order" @next="next" @cancel="cancel" />
				</v-lazy>
			</v-stepper-content>
			<v-stepper-content :step="2" class="px-0">
				<v-lazy>
					<checkout-shipping :order="order" @next="next" @prev="prev" @cancel="cancel" />
				</v-lazy>
			</v-stepper-content>
			<v-stepper-content :step="3" class="px-0">
				<v-lazy>
					<checkout-payment :order="order" @next="next" @prev="prev" @cancel="cancel" />
				</v-lazy>
			</v-stepper-content>
		</v-stepper-items>
	</v-stepper>
</template>

<script>
export default {
	props: {
		order: {
			type: Object,
			default: () => { },
		},
	},
	data() {
		return {
			stepModel: Number(this.$route.params.step),
			isSittingAddress: false,
		}
	},
	watch: {
		stepModel(val) {
			this.$router.push(this.localePath({ name: this.getRouteBaseName(), params: { step: val } })).then(this.$fetch)
		},
	},
	methods: {
		next() {
			this.stepModel++
		},
		prev() {
			this.stepModel--
		},
		cancel() {
			this.$confirm("Are you sure you want to cancel this order?", {
				title: "Cancel Order",
			}).then((isAccepted) => {
				if (isAccepted) {
					const orderId = this.$route.params.orderId
					this.$axios.$post(`/v1/orders/${orderId}/canceled`).then((res) => {
						this.$router.push(this.localePath({ name: "my-orders" }))
					})
				}
			})
		},
	},
}
</script>
