<template>
	<v-container>
		<v-sheet max-width="1280" class="mx-auto">
			<v-row>
				<v-col md="8">
					<NuxtChild :order="order" />
				</v-col>
				<v-col md="4">
					<sticky-sidebar>
						<v-skeleton-loader :loading="$fetchState.pending && isRefreshOnly === false" type="paragraph@3">
							<v-card :loading="$fetchState.pending && !isRefreshOnly">
								<v-card-title> {{ $t("order.order") }} </v-card-title>
								<v-card-text>
									<v-list-item v-for="(item, i) in order.orderItems" :key="'order-item-' + i" three-line>
										<v-img class="me-4 flex-grow-0" :width="80" contain :aspect-ratio="4 / 3"
											:src="item.product.media?.cover?.[0]?.src" />

										<v-list-item-content>
											<v-list-item-title class="line-clamp-2">
												{{ item.product.name }}
											</v-list-item-title>
											<v-list-item-subtitle class="line-clamp-2">
												{{ item.variance?.name }}
											</v-list-item-subtitle>
											<v-list-item-subtitle>{{ item.price | money }} × {{ item.quantity
												}}</v-list-item-subtitle>
											<span
												v-if="(!item?.product?.stock && item?.product?.type == 'bundle') || (!item?.variance?.stock && ['simple', 'alternative'].includes(item?.product?.type))"
												class="warning--text">
												{{ $t("product.out-of-stock") }}
											</span>
										</v-list-item-content>
									</v-list-item>
									<v-divider class="mb-3" />

									<v-row no-gutters>
										<v-col cols="6" class="text-subtitle-1">
											{{ $t("order.subtotal") }}
										</v-col>
										<v-col cols="6" class="text-subtitle-1 text-end">
											{{ order.subTotal | money }}
										</v-col>
									</v-row>
									<v-row no-gutters>
										<v-col cols="6" class="text-subtitle-1">
											{{ $t("order.shipping") }}
										</v-col>
										<v-col v-if="order.shippingPrice" cols="6" class="text-subtitle-1 text-end">
											{{
												order.shippingPrice | money
											}}
										</v-col>
										<v-col v-else cols="6" class="text-subtitle-1 text-end">
											-
										</v-col>
									</v-row>
									<v-row no-gutters>
										<v-col cols="6" class="text-subtitle-1">
											{{ $t("order.total") }}
										</v-col>
										<v-col cols="6" class="text-subtitle-1 text-end">
											{{ order.total | money }}
										</v-col>
									</v-row>
								</v-card-text>
							</v-card>
						</v-skeleton-loader>
					</sticky-sidebar>
				</v-col>
			</v-row>
		</v-sheet>
	</v-container>
</template>

<script>
import StickySidebar from "~/components/StickySidebar.vue"
export default {
	key: route => "checkout-page",
	components: { StickySidebar },
	layout: "website",
	fetchOnServer: false,
	validate({ params, query, store }) {
		// make sure params.step is a number
		const step = Number(params.step)
		return step > 0 && step <= 3
	},
	data() {
		return {
			order: {},
			isRefreshOnly: false,
		}
	},
	async fetch() {
		await this.$axios.$get(`/v1/orders/${this.$route.params.orderId}`).then((res) => {
			this.order = res
		})
		this.isRefreshOnly = false
		if (this.order.status !== 'draft') {
			this.$router.replace({ params: { ...this.$route.params, step: 3 } })
		}
	},
	computed: {},
	watch: {
		"$route.params.step"(val) {
			this.isRefreshOnly = true
			this.$fetch()
		},
	},
	methods: {},
}
</script>

<style scoped>
.checkout-field-max-width {
	max-width: 400px;
}
</style>
