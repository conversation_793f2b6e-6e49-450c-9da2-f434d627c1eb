<template>
	<v-container>
		<v-form ref="contact" class="mx-6 mt-4" @submit.prevent="submit">
			<h2 class="text-center my-10">
{{ $t("common.contact-us") }}
</h2>

			<v-row>
				<v-col cols="12" sm="6" md="6">
					<vc-text-field
						v-model="form.name"
						:label="$t('common.your-name')"
						:rules="[$rules.required($t('common.name'))]"
					/>
				</v-col>
				<v-col cols="12" sm="6" md="6">
					<vc-text-field
						v-model="form.email"
						type="email"
						:label="$t('common.email')"
						:rules="[$rules.required($t('common.email'))]"
					/>
				</v-col>
			</v-row>
			<v-row>
				<v-col cols="12" sm="6" md="6">
					<vc-phone
						v-model="form.phone"
						outlined
						name="phone"
						:label="$t('common.phone')"
						:rules="[$rules.required($t('common.phone'))]"
					/>
				</v-col>
				<v-col cols="12" sm="6" md="6">
					<vc-select
						v-model="form.problemType"
						:rules="[$rules.required($t('common.problem-type'))]"
						:items="problemTypesItems"
						:label="$t('common.problem-type')"
					/>
				</v-col>
			</v-row>
			<v-row>
				<v-col cols="12" sm="12" md="12">
					<vc-textarea
						v-model="form.message"
						:rules="[$rules.required($t('common.message'))]"
						:label="$t('common.message')"
						rows="8"
					/>
				</v-col>
			</v-row>
			<v-btn color="primary" type="submit" :loading="loading" class="mx-2 mt-4">
{{ $t("common.submit-now") }}
</v-btn>
		</v-form>
	</v-container>
</template>

<script>
import { problemTypes } from "~/config/Enum"

export default {
	name: "ContactUs",
	layout: "website",
	data() {
		return {
			branches: [],
			loading: false,
			form: {
				name: null,
				email: null,
				problemType: "",
				phone: {
					iso: "JO",
					number: "",
					code: "962",
				},
				message: null,
			},
		}
	},
	async fetch() {
		await this.$axios.$get(`/v1/branches`).then((resp) => {
			this.branches = resp
		})
	},

	computed: {
		problemTypesItems() {
			return problemTypes.map(type => ({
				text: this.$t(type.key),
				value: type.value,
			}))
		},
	},

	methods: {
		submit() {
			if (!this.$refs.contact.validate()) {
				return
			}
			this.loading = true
			this.$axios
				.$post(`v1/contact-us`, this.form)
				.then((resp) => {
					this.form = ""
					this.$toast.success(`${this.$t("common.has-been-save-successfully")}`)
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.contact)
				})
				.finally(() => {
					this.loading = false
				})
		},
	},
}
</script>
