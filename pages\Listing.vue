<template>
	<v-container>
		<div class="overflow-x-hidden">
			<v-navigation-drawer v-model="drawerModel" :right="isRTL" width="350" app fixed class="hidden-md-and-up"
				temporary>
				<v-lazy max-height="100dvh">
					<div class="pe-1 pb-2" :class="{ 'disable-overlay': isFiltering }">
						<!-- <client-only> -->
						<div v-for="(filter, objectKey) in filters" :key="objectKey">
							<!-- {{ objectKey }}:{{ filter.type }} -->
							<!-- {{ filter.name }} -->
							<component :is="`website-filters-${filter.type}`" v-model="filter.value" :name="filter.name"
								:items="filter.options" :config="filter.config" class="mb-4" @filter="debouncedFilterHandler" />
						</div>
						<!-- </client-only> -->
					</div>
				</v-lazy>
			</v-navigation-drawer>
		</div>
		<v-row class="flex-nowrap">
			<v-col md="3" class="hidden-sm-and-down">
				<sticky-sidebar>
					<div class="pe-1 pb-2" :class="{ 'disable-overlay': isFiltering }">
						<!-- <client-only> -->
						<div v-for="(filter, objectKey) in filters" :key="objectKey">
							<!-- {{ objectKey }}:{{ filter.type }} -->
							<!-- {{ filter.name }} -->
							<component :is="`website-filters-${filter.type}`" v-model="filter.value" :name="filter.name"
								:items="filter.options" :config="filter.config" class="mb-4" @filter="debouncedFilterHandler" />
						</div>
						<!-- </client-only> -->
					</div>
				</sticky-sidebar>
			</v-col>
			<v-divider vertical class="hidden-sm-and-down" />
			<v-col cols="12" md="9">
				<v-row align="center">
					<v-col cols="12" md="8">
						<v-skeleton-loader type="heading" min-height="48" :loading="isFetching && !isFiltering && !isSorting">
							<h1 class="mb-2">
								{{ pageTitle }}
							</h1>
						</v-skeleton-loader>
						<breadcrumbs :items="breadcrumbItems" />
					</v-col>
					<v-col cols="12" md="4" class="d-flex">
						<v-select v-model="orderBy" :items="OrderByItems" :label="$t('common.order-by')" class="me-3"
							hide-details clearable @change="orderByHandler" />
						<v-btn icon tile class="mt-3">
							<v-icon>mdi-filter</v-icon>
						</v-btn>
					</v-col>
				</v-row>
				<v-row v-if="!isSearchPage">
					<v-col cols="12" class="overflow-x-hidden">
						<category-cards v-if="hasSubcategory" :data="category"
							:loading="isFetching && !isFiltering && !isSorting" />
					</v-col>
				</v-row>
				<v-row>
					<v-col v-for="product in keyedProducts" :key="product.key" cols="6" sm="4" md="4" lg="3">
						<product-card :category-id="category.categoryId" :item="product.data" :loading="isFetching"
							:heading="'h2'" />
					</v-col>
					<v-col v-if="hasError">
						<v-alert type="error" text>
							{{ $t("common.something-went-wrong") }} <v-btn plain color="error" @click="$fetch">
								Retry
							</v-btn>
						</v-alert>
					</v-col>
					<v-col v-else-if="!isFetching && !products.length">
						<v-banner v-if="hasFilter" icon="mdi-filter-off" icon-color="warning" two-line max-width="900"
							class="mx-auto">
							There are no products matching the selection. Please try again with fewer filters

							<template #actions>
								<v-btn text @click="clearAllFilters">
									<v-icon left>
										mdi mdi-close
									</v-icon>
									Clear Filters
								</v-btn>
								<v-btn text @click="$router.back()">
									<v-icon left>
										mdi-arrow-left
									</v-icon>
									Previous Result
								</v-btn>
							</template>
						</v-banner>
					</v-col>
				</v-row>

				<v-pagination v-if="pagination.total > pagination.perPage" v-model="pagination.page"
					:total-visible="isMobile ? 5 : 8" :disabled="isFetching" :items-per-page="pagination.perPage"
					:length="pagination.lastPage" @input="paginationHandler" />
			</v-col>
		</v-row>
	</v-container>
</template>

<script>
import debounce from "debounce"
export default {
	name: "Listing",
	layout: "website",
	data() {
		return {
			products: 8,
			category: {},
			filters: null,
			query: this.$route.query.q || null,
			lastSearchFullPath: null,
			orderBy: this.$route.query.orderBy || null,
			pagination: {
				total: null,
				count: 0,
				perPage: 24,
				page: Number(this.$route.query.page) || 1,
				lastPage: null,
			},
			isFiltering: false,
			isSorting: false,
			cancelToken: null,
			hasError: false,
			drawerModel: false,
		}
	},
	async fetch() {
		// url can be any number of categories slug in path
		this.products = this.pagination.perPage
		this.hasError = false
		const query = {
			...this.$route.query,
			perPage: this.pagination.perPage,
		}
		if (process.client) {
			// Cancel previous requests if any
			if (this.cancelToken && typeof this.cancelToken.cancel === "function") {
				this.cancelToken.cancel()
			}
			this.cancelToken = this.$axios.CancelToken.source()
		}
		const promises = []
		if (!this.isSearchPage && this.currentCategorySlug !== this.category.slug) {
			promises.push(
				this.$axios
					.$get(`/v1/categories/${this.categoryPath}`, {
						cancelToken: this.cancelToken?.token,
					})
					.then((resp) => {
						this.category = resp
						// const slugs = {
						// 	en: [],
						// 	ar: [],
						// }

						// recursive function to get all parent categories from this.category

						const getParentCategories = (category) => {
							// slugs.en.push(category.slug.en)
							// slugs.ar.push(category.slug.ar)

							if (category.parent) {
								getParentCategories(category.parent)
							}
						}
						getParentCategories(this.category)
						// await this.$store.dispatch("i18n/setRouteParams", {
						// 	en: { slugs: slugs.en.reverse().join("/") },
						// 	ar: { slugs: slugs.ar.reverse().join("/") },
						// })
					}).catch((errors) => {
						this.category = {}
						this.hasError = true
						// if (errors?.response?.status === 301 && errors.response?.data?.details?.redirectUrl) {
						// 	const slugs = decodeURIComponent(errors.response.data.details.redirectUrl)
						// 	const path = this.localePath({
						// 		name: 'category',
						// 		params: {
						// 			slugs,
						// 		},
						// 	})

						// 	if (process.server) {
						// 		console.log('redirecting...', path)
						// 		this.$nuxt.context.redirect(301, path)
						// 	} else {
						// 		this.$router.replace(path)
						// 	}
						// } else {
						// 	this.pageErrorHandler(errors)
						// }
					}),
				// .catch((errors) => {
				// 	this.category = {}
				// 	this.hasError = true
				// 	if (errors.code === 301 && errors.details && errors.details.redirectUrl) {
				// 		const redirectUrl = errors.details.redirectUrl
				// 		// Redirect the user to the provided URL
				// 		this.localePath({
				// 			name: 'category',
				// 			params: {
				// 				slugs: `${redirectUrl}`,
				// 			},
				// 		})
				// 		// const path = localePath({ name: 'product', params: { productSlug, varianceSlug } })
				// 		// redirect(301, path)
				// 		// this.$router.push(redirectUrl)
				// 	} else {
				// 		// Handle other errors
				// 		console.error(errors)
				// 	}
				// }),
			)
		}
		promises.push(
			this.$axios
				.$get(`/v1/search`, {
					params: {
						categories: this.categoryPath,
						slug: this.currentCategorySlug,
						slugs: this.categoryPathSegments,
						orderBy: this.orderBy,

						...query,
					},
					cancelToken: this.cancelToken?.token,
				})
				.then((resp) => {
					this.products = resp.items
					this.filters = resp.filters
					this.pagination = resp.pagination
				})
				.catch((errors) => {
					this.products = []
					this.hasError = true
					if (errors?.response?.status === 301 && errors?.response?.data?.details?.redirectUrl) {
						const slugs = errors.response.data.details.redirectUrl
						// const path = this.localePath({ name: 'category', params: { slugs } })
						const path = decodeURIComponent(this.localePath({
							name: 'category',
							params: {
								slugs,
							},
						}))

						if (process.server) {
							console.log("redirecting....", path)
							return this.$nuxt.context.redirect(301, path)
						} else {
							this.$router.replace(path)
						}
					} else {
						this.pageErrorHandler(errors)
					}
				}),

		)

		await Promise.all(promises).catch((error) => {
			if (this.$axios.isCancel(error)) {
				console.log("Request canceled:", error.message)
			} else {
				this.genericErrorHandler(error)
			}
		}).finally(() => {
			this.isFiltering = false
			this.isSorting = false
		})
	},
	head() {
		return {
			script: [
				{
					type: "application/ld+json",
					innerHTML: JSON.stringify(this.jsonld), // <- set jsonld object in data or wherever you want
				},
			],
			__dangerouslyDisableSanitizers: ["script"], // <- this is important

			title: this.metaTitle,
			titleTemplate: (titleChunk) => {
				return titleChunk
			},
			meta: [
				{
					hid: "description",
					name: "description",
					content: this.metaDescription,
				},
				{
					hid: "og:title",
					property: "og:title",
					content: this.metaTitle,
				},
				{
					hid: "og:description",
					property: "og:description",
					content: this.metaDescription,
				},
				{
					hid: "og:image",
					property: "og:image",
					content:
						this.category?.media?.cover,

				},
				{
					hid: "og:url",
					property: "og:url",
					content: this.$route.fullPath,
				},
				{
					hid: "og:type",
					property: "og:type",
					content: "category",
				},
				{
					hid: "og:site_name",
					property: "og:site_name",
					content: "Action.jo",
				},
				{
					hid: "twitter:title",
					name: "twitter:title",
					content: this.metaTitle,
				},
				{
					hid: "twitter:description",
					name: "twitter:description",
					content: this.metaDescription,
				},
				{
					hid: "twitter:image",
					name: "twitter:image",
					content: this.category?.media?.cover,
				},
				{
					hid: "twitter:card",
					name: "twitter:card",
					content: "summary_large_image",
				},
			],
			link: this.generateLinks,
		}
	},

	computed: {
		isSearchPage() {
			return this.getRouteBaseName() === "search"
		},
		pageTitle() {
			if (this.isSearchPage) {
				return this.$t("listing.search-results")
			}
			return this.category.brand ? this.category.brand.name : this.category.name
		},
		metaTitle() {
			const text = this.pagination.page > 1 ? ` - ${this.$t("common.page")} ${this.$t("common.number")} ${this.pagination.page}` : ""
			return this.isSearchPage ? this.$t("listing.search-results") : `${this.category.metaTitle} ${text}` || ''
		},
		metaDescription() {
			return this.isSearchPage ? this.$t("listing.search-results") : this.category.metaDescription || ''
		},
		keyedProducts() {
			let products = []
			if (typeof this.products === "number") {
				products = Array.from({ length: this.products })
			} else {
				products = this.products
			}
			return products.map((product, index) => {
				return {
					data: product,
					key: "product-" + index,
				}
			})
		},
		categoryPath() {
			return this.$route.params.slugs
		},
		categoryPathSegments() {
			return this.categoryPath?.split("/")
		},
		currentCategorySlug() {
			if (this.categoryPathSegments?.length > 0) {
				return this.categoryPathSegments[this.categoryPathSegments?.length - 1]
			} else {
				return null
			}
		},
		OrderByItems() {
			return [
				{
					text: this.$t("common.date-from-recent-to-oldest"),
					value: "createdAt,desc",
				},
				{
					text: this.$t("common.avg-desc-customer-review"),
					value: "avgRate,desc",
				},
				{
					text: this.$t("common.avg-asc-customer-review"),
					value: "avgRate,asc",
				},
				{
					text: this.$t("common.date-from-oldest-to-recent"),
					value: "createdAt,asc",
				},
				{
					text: this.$t("common.price-from-high-to-low"),
					value: "basePrice,desc",
				},
				{
					text: this.$t("common.price-from-low-to-high"),
					value: "basePrice,asc",
				},
			]
		},
		hasSubcategory() {
			return this.category?.children?.length
		},
		isShowBrandIngListing() {
			return this.category.isShowBrandIngListing
		},
		breadcrumbItems() {
			const items = []
			if (!this.isSearchPage) {
				// recursive function to get all parent categories

				const getParentCategories = (category) => {
					// if (category.parent) {
					// 	const slug = category.parent.parent
					// 		? `${category.parent.parent.slug}/${category.parent.slug}`
					// 		: category.parent.slug
					// 	items.unshift({
					// 		text: category.parent.name,
					// 		disabled: false,
					// 		to: decodeURIComponent(this.localePath({
					// 			name: "category",
					// 			params: {
					// 				slugs: slug,
					// 			},
					// 		})),
					// 		link: true,
					// 		nuxt: true,
					// 		exactPath: true,
					// 	})
					// 	getParentCategories(category.parent)
					// }

					if (category.parent) {
						const slug = category.parent.parent && category.parent.parent.parent
							? `${category.parent.parent.parent.slug}/${category.parent.parent.slug}/${category.parent.slug}`
							: category.parent.parent
								? `${category.parent.parent.slug}/${category.parent.slug}`
								: category.parent.slug
						items.unshift({
							text: category.parent.name,
							disabled: false,
							to: decodeURIComponent(this.localePath({
								name: "category",
								params: {
									slugs: slug,
								},
							})),
							link: true,
							nuxt: true,
							exactPath: true,
						})
						getParentCategories(category.parent)
					}
				}

				getParentCategories(this.category)

				// add current category

				items.push({
					text: this.category.name,
					disabled: true,
				})

				items.unshift({
					text: this.$t("common.home"),
					disabled: false,
					to: this.localePath({
						name: "index",
					}),
					link: true,
					nuxt: true,
					exactPath: true,
				})
			} else {
				items.push({
					text: this.$t("listing.search-results"),
					disabled: true,
				})
			}

			return items
		},
		hasFilter() {
			return (
				this.filters &&
				Object.keys(this.filters).filter((key) => {
					// the filter is not a pagination filter
					return this.pagination[key] === undefined
				}).length
			)
		},
		generateLinks() {
			if (this.$fetchState.pending) { return [] }

			const links = [
				{
					rel: "canonical",
					href: `${process.env.NUXT_ENV_BASE_URL}/${this.$i18n.locale}/category/${this.categoryPath}`,
				},
				{
					rel: "alternate",
					hreflang: `ar-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/ar/category/${this.categoryPath}`,
				},
				{
					rel: "alternate",
					hreflang: `en-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/en/category/${this.categoryPath}`,
				},
				{
					rel: "alternate",
					hreflang: "x-default",
					href: `${process.env.NUXT_ENV_BASE_URL}/en/category/${this.categoryPath}`,
				},
			]
			return links
		},
	},
	watch: {
		"$route.query.q": {
			handler() {
				this.$nextTick(() => {
					this.filterHandler()
				})
			},
			// deep: true,
		},
	},

	methods: {
		debouncedFilterHandler() {
			return debounce(this.filterHandler, 500)()
		},
		clearAllFilters() {
			this.isFiltering = true
			this.$router
				.push(
					decodeURIComponent(
						this.localePath({
							name: this.getRouteBaseName(),
							params: this.$route.params,
							query: {
								// ...this.$route.query,
								// ...this.filters,
								page: 1,
							},
						}),
					),
				)
				.then(() => {
					this.$fetch()
				})
				.catch(() => { })
		},
		filterHandler() {
			const query = {}
			for (const key in this.filters) {
				if (this.filters[key].value) {
					if (Array.isArray(this.filters[key].value) && this.filters[key].value.length) {
						query[key] = this.filters[key].value.join(",")
						// } else if(typeof this.filters[key].value === "object") {
						// 	query[key] = this.filters[key].value.value;
					} else if (
						this.filters[key].value &&
						(typeof this.filters[key].value === "string" || typeof this.filters[key].value === "number")
					) {
						query[key] = this.filters[key].value
					}
				}
			}
			// push q query if exists
			if (this.$route.query.q) {
				query.q = this.$route.query.q
			}
			// push orderBy query if exists
			if (this.orderBy) {
				query.orderBy = this.orderBy
			}
			const newRoute = decodeURIComponent(
				this.localePath({
					name: this.getRouteBaseName(),
					params: this.$route.params,
					query,
				}),
			)
			// check if new route != current route

			this.isFiltering = true
			this.$router
				.push(newRoute)
				.then(() => {
					this.$vuetify.goTo(0, { duration: 200 })
					this.$fetch()
				})
				.catch(() => {
					this.$vuetify.goTo(0, { duration: 200 })
					this.$fetch()
				})
		},
		orderByHandler() {
			this.isSorting = true
			this.filterHandler()

			// const query = { ...this.$route.query };
			// if (this.orderBy) {
			// 	query.orderBy = this.orderBy;
			// } else {
			// 	// delete query.orderBy;
			// }
			// this.$router
			// 	.push(
			// 		decodeURIComponent(
			// 			this.localePath({
			// 				name: this.getRouteBaseName(),
			// 				params: this.$route.params,
			// 				query,
			// 			})
			// 		)
			// 	)
			// 	.then(() => {
			// 		// this.$fetch();
			// 	});
		},
		paginationHandler(page) {
			this.isFiltering = true
			const query = { ...this.$route.query }
			if (page > 1) {
				query.page = page
			} else {
				delete query.page
			}

			this.$router
				.push({
					name: this.$route.name,
					params: this.$route.params,
					query,
				})
				.then(() => {
					this.$fetch()
				})
				.catch(() => { })
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/mixins.scss";

.filter {
	// width: 300px;
	@include responsive("width", 0, 300px);
	@include responsive("flex-shrink", 1, 0);
	@include responsive("margin-inline-end", 0, 16px);
	padding-inline-start: 10px;
	flex-shrink: 0;
	padding: 10px;
}

.view {
	flex-grow: 1;
}

.toolbar {
	.icon {
		width: 36px;
		height: 36px;
		min-width: 0 !important;
		padding: 0 !important;
	}
}
</style>
