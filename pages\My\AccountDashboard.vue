<template>
	<div>
		<h3 class="mb-2">
			{{ $t("common.my-dashboard") }}
		</h3>
		<v-card outlined>
			<v-list-item two-line>
				<v-list-item-avatar>
					<v-img alt="user" :src="userAvatar" />
				</v-list-item-avatar>
				<v-list-item-content>
					<v-list-item-title class="text-overline">
						{{ $t("common.hello") }} {{ user.fullName }}
					</v-list-item-title>
					<v-list-item-subtitle> {{ user.email }}</v-list-item-subtitle>
				</v-list-item-content>
			</v-list-item>
		</v-card>
		<h3 class="mb-2 mt-8">
			Recent Activities
		</h3>
	</div>
</template>

<script>
export default {
	name: "AccountDashboard",
	layout: "website",
	data() {
		return {
			user: {},
		}
	},
	async fetch() {
		await this.$axios.$get(`v1/my/profile`).then((resp) => {
			this.user = resp
		})
	},

	computed: {
		userAvatar() {
			return (
				this.user.media?.avatar?.original_url ||
				"/images/default-avatar.jpg"
			)
		},
	},
}
</script>
