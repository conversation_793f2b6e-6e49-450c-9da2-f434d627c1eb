<template>
	<v-card outlined>
		<v-card-text class="pa-0 pa-md-4">
			<v-skeleton-loader :loading="$fetchState.pending" type="paragraph@6">
				<div>
					<div class="">
						<div class="d-flex justify-space-between">
							<div class="d-flex">
								<!-- <v-avatar v-if="hasImage" max-width="600" class="pt-3 mr-5">
									<v-img alt="user" :src="user.media.avatar.url"></v-img>
								</v-avatar> -->
								<!-- <span class="mt-1 mx-4"> {{ $t("common.profile-picture") }} </span> -->
								<avatar class="mt-1 mx-4" :src="hasImage" />
							</div>
							<div>
								<profile-picture />
							</div>
						</div>

						<v-divider class="mx-2 my-2" />

						<!-- <profile-change-name class="mx-6 mt-4"></profile-change-name> -->
						<div v-if="!isEditName" class="d-flex justify-space-between">
							<p class="mt-1 mx-4">
								{{ form.firstName }} {{ form.lastName }}
							</p>
							<v-btn color="primary" text @click.prevent="editName()">
								{{ $t("common.edit") }}
							</v-btn>
						</div>
						<div v-else class="d-flex justify-space-between">
							<vc-text-field v-model="form.firstName" :label="$t('common.first-name')"
								:rules="[$rules.required($t('common.first-name'))]" />

							<vc-text-field v-model="form.lastName" :label="$t('common.last-name')"
								:rules="[$rules.required($t('common.last-name'))]" />
							<div class="d-flex">
								<v-btn text :disabled="updating === 'name'" @click="cancel()">
									{{ $t("common.cancel") }}
								</v-btn>
								<v-btn color="primary" text type="submit" :loading="updating === 'name'"
									@click.prevent="updateName">
									{{ $t("common.update") }}
								</v-btn>
							</div>
						</div>
						<v-alert v-if="form.status === 'pending'" type="warning" variant="outlined">
							<span> {{ $t("auth.your-username-change-request-is-pending") }} </span>
						</v-alert>
						<v-spacer />

						<v-divider class="mx-2 my-2" />

						<div v-if="!isEditEmail" class="d-flex flex-wrap justify-space-between">
							<p v-if="!form.email" class="mt-1 mx-4">
								{{ $t("auth.verify-your-email") }}
							</p>

							<p v-else class="mt-1 mx-4">
								{{ form.email }}
							</p>
							<div class="d-flex">
								<v-btn v-if="!ifEmailVerified" outlined :loading="isLoading" color="primary"
									@click.prevent="verifyEmail()">
									{{ $t("auth.verify-email-now") }}
								</v-btn>
								<!-- START OF OTP DIALOG -->
								<v-dialog v-model="showEmailOtpDialog" color="primary" variant="flat" max-width="500"
									max-height="500">
									<v-form v-if="showEmailOtpDialog" ref="otp" @submit.prevent="submitOTP">
										<!-- //	otp vuetify 1 -->
										<v-card class="py-8 px-6 text-center mx-auto ma-4 text-h5 my-4 relative justify-center"
											elevation="12" max-width="500" width="100%">
											<h3 class="text-h6 mb-4">
												{{ $t("auth.verify-your-email") }}
											</h3>

											<div class="text-body-2">
												{{ $t("auth.otp-verification-code-profile") }}
												{{ form.email }} <br>
											</div>

											<v-sheet color="surface">
												<v-otp-input v-model="otp" color="primary" focus-all focused variant="solo"
													:length="4" />
											</v-sheet>

											<v-btn type="submit" class="my-4" color="primary" height="40" width="70%">
												{{
													$t("auth.verify-email-now")
												}}
											</v-btn>

											<div class="text-caption">
												{{ $t("auth.didnt-receive-the-code") }}
												<a href="#" @click.prevent="resend">{{ $t("auth.resend") }}</a>
											</div>
										</v-card>
									</v-form>
								</v-dialog>
								<!-- END OF OTP DIALOG -->
								<v-btn color="primary" text @click="editEmail()">
									{{ $t("common.edit") }}
								</v-btn>
							</div>
						</div>
						<div v-else class="d-flex justify-space-between">
							<div>
								<vc-text-field v-model="form.email" label="Email" type="email"
									:rules="[$rules.required($t('common.email'))]" />
							</div>
							<div class="d-flex">
								<v-btn :disabled="updating === 'email'" text @click="cancel()">
									{{ $t("common.cancel") }}
								</v-btn>
								<v-btn color="primary" :loading="updating === 'email'" text @click="updateEmail()">
									{{
										$t("common.update") }}
								</v-btn>
							</div>
						</div>

						<v-divider class="mx-2 my-2" />

						<div v-if="!isEditPhone" class="d-flex flex-wrap justify-space-between">
							<p class="mt-1 mx-4">
								{{ form.phone | phone }}
							</p>

							<div class="d-flex">
								<v-btn v-if="!ifPhoneVerified" outlined :loading="isLoading" color="primary"
									@click="verifyPhone()">
									{{
										$t("auth.verify-phone-now")
									}}
								</v-btn>
								<!-- START OF OTP DIALOG -->
								<v-dialog v-model="showPhoneOtpDialog" color="primary" variant="flat" max-width="500"
									max-height="500">
									<v-form v-if="showPhoneOtpDialog" ref="otp" @submit.prevent="submitOTP">
										<!-- //	otp vuetify 1 -->
										<v-card class="py-8 px-6 text-center mx-auto ma-4 text-h5 my-4 relative justify-center"
											elevation="12" max-width="500" width="100%">
											<h3 class="text-h6 mb-4">
												{{ $t("auth.verify-your-phone-number") }}
											</h3>

											<div class="text-body-2">
												{{ $t("auth.otp-verification-code-profile") }}
												{{ form.phone | phone }}
												<br>
											</div>

											<v-sheet color="surface">
												<v-otp-input v-model="otp" color="primary" focus-all focused variant="solo"
													:length="4" />
											</v-sheet>

											<v-btn type="submit" class="my-4" color="primary" height="40" width="70%">
												{{
													$t("auth.verify-phone-now")
												}}
											</v-btn>

											<div class="text-caption">
												{{ $t("auth.didnt-receive-the-code") }}
												<a href="#" @click.prevent="resend">{{ $t("auth.resend") }}</a>
											</div>
										</v-card>
									</v-form>
								</v-dialog>
								<!-- END OF OTP DIALOG -->
								<v-btn color="primary" text @click="editPhone()">
									{{ $t("common.edit") }}
								</v-btn>
							</div>
						</div>
						<div v-else class="d-flex justify-space-between">
							<div>
								<vc-phone v-model="form.phone" outlined name="phone" :label="$t('common.phone')"
									:rules="[$rules.required($t('common.phone'))]" />
							</div>
							<div class="d-flex">
								<v-btn :disabled="updating === 'phone'" text @click="cancel()">
									{{ $t("common.cancel") }}
								</v-btn>
								<v-btn color="primary" :loading="updating === 'phone'" text @click="updatePhoneNumber()">
									{{
										$t("common.update") }}
								</v-btn>
							</div>
						</div>

						<v-divider class="mx-2 my-2" />

						<div class="d-flex justify-space-between">
							<p v-if="!form.birthday" class="mt-1 mx-4">
								{{ $t("common.please-select-the-birthday") }}
							</p>
							<p v-if="form.birthday" class="mt-1 mx-4">
								{{ form.birthday | date }}
							</p>

							<div class="d-flex">
								<v-btn color="primary" text @click="openDialog">
									{{ $t("common.edit") }}
								</v-btn>
							</div>
						</div>

						<div>
							<v-dialog v-model="dialog" color="primary" variant="flat" max-width="300" max-height="300">
								<v-card>
									<v-card-title>
										<span class="text-h5"> {{ $t("common.birthday") }} </span>
									</v-card-title>
									<v-date-picker v-model="form.birthday" class="pa-2" outlined show-adjacent-months
										color="primary" />
									<div class="d-flex">
										<v-btn :disabled="updating === 'birthday'" text @click="closeDialog()">
											{{
												$t("common.cancel") }}
										</v-btn>
										<v-btn color="primary" :loading="updating === 'birthday'" text @click="updateAccount()">
											{{
												$t("common.update") }}
										</v-btn>
									</div>
								</v-card>
							</v-dialog>
						</div>

						<v-divider class="mx-2 my-2" />

						<div v-if="!isEditGender" class="d-flex justify-space-between">
							<p v-if="!form.gender" class="mt-1 mx-4">
								{{ $t("common.please-select-the-gender") }}
							</p>
							<p v-if="form.gender" class="mt-1 mx-4">
								{{ form.gender }}
							</p>

							<div class="d-flex">
								<v-btn color="primary" text @click="editGender()">
									{{ $t("common.edit") }}
								</v-btn>
							</div>
						</div>

						<div v-else class="d-flex justify-space-between">
							<div>
								<vc-select v-model="form.gender" :rules="[$rules.required($t('common.gender'))]"
									:items="gendersItems" :label="$t('common.gender')" />
							</div>
							<div class="d-flex">
								<v-btn :disabled="updating === 'gender'" text @click="cancel()">
									{{ $t("common.cancel")
									}}
								</v-btn>
								<v-btn color="primary" :loading="updating === 'gender'" text @click="updateAccount()">
									{{
										$t("common.update") }}
								</v-btn>
							</div>
						</div>
						<v-divider class="mx-2 my-2" />
						<!-- <profile-change-password ></profile-change-password> -->
						<v-form ref="password" @submit.prevent="updatePassword">
							<div>
								<div class="d-flex justify-space-between">
									<div class="mt-1 mx-4">
										<p>{{ $t("common.password") }}</p>
									</div>
									<div class="pb-4">
										<v-btn color="primary" text @click="editPassword()">
											{{ $t("common.change-password") }}
										</v-btn>
									</div>
								</div>
								<div v-if="isActive" class="d-flex justify-space-between">
									<v-row>
										<v-col cols="12" lg="8">
											<v-row>
												<v-col cols="12">
													<vc-password v-model="password.oldPassword" name="oldPassword" no-strength
														:rules="[$rules.required($t('common.old-password'))]"
														:label="$t('common.old-password')" />
												</v-col>
											</v-row>
											<v-row>
												<v-col cols="12" lg="6">
													<vc-password v-model="password.password" name="password" no-strength :rules="[
														$rules.required($t('common.password')),
														$rules.minLength($t('common.password'), 8),
													]" :label="$t('common.password')" />
												</v-col>

												<v-col cols="12" lg="6">
													<vc-password v-model="password.passwordConfirmation" name="passwordConfirmation"
														no-strength :rules="[
															$rules.required($t('common.password-confirmation')),
															$rules.minLength($t('common.password-confirmation'), 8),
														]" :label="$t('common.password-confirmation')" />
												</v-col>
											</v-row>
										</v-col>
										<v-col cols="12" lg="4">
											<div v-if="isActive" class="d-flex">
												<v-btn text @click="cancel()">
													{{ $t("common.cancel") }}
												</v-btn>
												<v-btn color="primary" text type="submit" :loading="loading">
													{{
														$t("common.save") }}
												</v-btn>
											</div>
										</v-col>
									</v-row>
								</div>
							</div>
						</v-form>
					</div>
				</div>
			</v-skeleton-loader>
		</v-card-text>
	</v-card>
</template>

<script>
import { genders } from "~/config/Enum"

export default {
	name: "AccountInformation",
	layout: "website",

	data() {
		return {
			isEditEmail: false,
			isEditPhone: false,
			isEditGender: false,
			isEditName: false,
			isEditingPassword: false,
			isEditBirthday: false,
			editing: null,
			updating: null,
			form: {
				firstName: "",
				lastName: "",
				email: "",
				gender: "",
				phone: {
					iso: "JO",
					number: "",
					code: "962",
				},
				birthday: "",
				media: {
					avatar: {
						id: null,
						src: "",
						preview: "",
						fileSize: null,
						mimeType: "",
						disk: "",
					},
				},
			},
			status: null,
			loading: false,
			isEdit: false,
			dialogName: false,
			dialog: false,
			password: {
				oldPassword: "",
				password: "",
				passwordConfirmation: "",
			},
			isActive: false,
			isButtonActive: false,
			showPhoneOtpDialog: false,
			showEmailOtpDialog: false,
			isLoading: false,
			otp: "",
			ifEmailVerified: false,
			ifPhoneVerified: false,
		}
	},
	fetch() {
		this.form = this.$cloneDeep(this.$auth.user)
	},
	computed: {
		// ifEmailVerified() {
		// 	return this.$store.$auth.user.emailVerifiedAt;
		// },
		// ifPhoneVerified() {
		// 	return this.$store.$auth.user.phoneVerifiedAt;
		// },
		hasImage() {
			return this.$auth.user?.media?.avatar || "/images/default-avatar.jpg"
		},
		gendersItems() {
			return genders.map(gender => ({
				text: this.$t(gender.key),
				value: gender.value,
			}))
		},
	},

	methods: {
		submitOTP() {
			this.$store
				.dispatch("authExtend/verificationOtpCode", { code: this.otp })
				.then((resp) => {
					this.$store.$auth.user = { ...this.$store.$auth.user, ...resp.user }
					this.ifEmailVerified = resp.emailVerifiedAt
					this.ifPhoneVerified = resp.phoneVerifiedAt
					this.$toast.success(this.$t("auth.otp-verified"))
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.token)
				})
				.finally(() => {
					this.isLoading = false
					this.otp = ""
					this.showPhoneOtpDialog = false
					this.showEmailOtpDialog = false
				})
		},
		verifyPhone() {
			this.isLoading = true
			this.showPhoneOtpDialog = true
			console.log("HIIIIIII", this.form.phone)
			this.$store.dispatch("authExtend/sendOtpCode", { phone: this.form.phone })
			// .then((resp) => {
			// 	console.log("HELLO RESP", resp);
			// })
			// .catch((e) => {
			// 	console.log(e);
			// 	if (e.response.status === 422) {
			// 		this.apiError = e.response.data.errors.email[0];
			// 	} else {
			// 		this.genericErrorHandler(e, this.$refs.email);
			// 	}
			// })
			// .finally(() => {});
		},
		verifyEmail() {
			this.isLoading = true
			this.showEmailOtpDialog = true
			console.log("HIIIIIII", this.form.email)
			this.$store.dispatch("authExtend/sendOtpCode", { email: this.form.email })
			// .then((resp) => {
			// 	console.log("HELLO RESP", resp);
			// })
			// .catch((e) => {
			// 	console.log(e);
			// 	if (e.response.status === 422) {
			// 		this.apiError = e.response.data.errors.email[0];
			// 	} else {
			// 		this.genericErrorHandler(e, this.$refs.email);
			// 	}
			// })
			// .finally(() => {});
		},

		openDialog() {
			this.dialog = true
		},
		closeDialog() {
			this.dialog = false
		},
		togglePassword() {
			this.isActive = !this.isActive
			this.isButtonActive = !this.isButtonActive
		},

		updatePassword() {
			console.log("this.$refs", this.$refs)
			if (!this.$refs.password.validate()) {
				this.scrollToError()
				return
			}
			this.loading = true
			this.$axios
				.$put(`v1/my/update-password`, this.password)
				.then(() => {
					this.password = {
						oldPassword: "",
						password: "",
						passwordConfirmation: "",
					}
					this.$toast.success(`${this.$t("common.password-has-been-updated-successfully")}`)
					this.cancel()
					this.isEditingPassword = false
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.password)
				})
				.finally(() => {
					this.loading = false
				})
		},
		editEmail() {
			this.cancel()
			this.isEditEmail = true
			this.editing = "email"
		},
		editPhone() {
			this.cancel()
			this.isEditPhone = true
			this.editing = "phone"
		},
		editGender() {
			this.cancel()
			this.isEditGender = true
			this.editing = "gender"
		},
		editBirthday() {
			this.cancel()
			this.isEditBirthday = true
			this.editing = "birthday"
		},
		editName() {
			this.cancel()
			this.isEditName = true
			this.editing = "name"
		},
		editPassword() {
			this.cancel()
			this.isEditingPassword = true
			this.editing = "password"
			this.isActive = true
		},
		cancel() {
			this.isEditName = false
			this.isEditEmail = false
			this.isEditGender = false
			this.isEditPhone = false
			this.isEditingPassword = false
			this.isActive = false
			this.isButtonActive = false
			this.dialog = false
		},

		updateAccount() {
			// if (!this.$refs.account.validate()) {
			// 	this.scrollToError();
			// 	return;
			// }
			this.loading = true
			this.updating = this.editing
			this.$axios
				.$put(`v1/my/update-Information`, this.form)
				.then((resp) => {
					this.form = resp
					this.cancel()
					this.$toast.success(`${this.$t("common.has-been-updated-successfully")}`)
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.account)
				})
				.finally(() => {
					this.loading = false
					this.updating = null
				})
		},

		updateEmail() {
			// if (!this.$refs.account.validate()) {
			// 	this.scrollToError();
			// 	return;
			// }
			this.loading = true
			this.updating = this.editing
			this.$axios
				.$put(`v1/my/update-email`, this.form)
				.then((resp) => {
					this.form = resp
					this.cancel()
					this.$toast.success(`${this.$t("common.has-been-updated-successfully")}`)
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.account)
				})
				.finally(() => {
					this.loading = false
					this.updating = null
				})
		},

		updatePhoneNumber() {
			// if (!this.$refs.account.validate()) {
			// 	this.scrollToError();
			// 	return;
			// }
			this.loading = true
			this.updating = this.editing
			this.$axios
				.$put(`v1/my/update-phone-number`, this.form)
				.then((resp) => {
					this.form = resp
					this.cancel()
					this.$toast.success(`${this.$t("common.has-been-updated-successfully")}`)
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.account)
				})
				.finally(() => {
					this.loading = false
					this.updating = null
				})
		},

		updateName(e) {
			e.preventDefault()
			this.isEditName = false
			// if (!this.$refs.name.validate()) {
			// 	this.scrollToError();
			// 	return;
			// }
			this.loading = true
			this.$axios
				.$post(`v1/my/request-change`, this.name)
				.then(() => {
					this.name = ""
					this.$toast.success(`${this.$t("common.the-name-will-be-changed-upon-approval-of-the-application-by-the-manager")}`)
					this.closeNameDialog()
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.name)
				})
				.finally(() => {
					this.loading = false
					this.dialog = false
				})
		},
	},
}
</script>
