<template>
	<v-container>
		<v-row class="mt-6">
			<v-col cols="12">
				<div>
					<h4>
						{{ $t("common.edit-address") }}
					</h4>
				</div>
				<div>
					<p>{{ $t("common.contact-information") }}</p>
				</div>
				<v-form ref="form" @submit.prevent="submit">
					<v-card width="900">
						<v-card-text>
							<v-row>
								<v-col cols="12" sm="6" md="6" lg="6">
									<vc-text-field
										v-model="form.recipientName"
										:label="$t('common.recipient-name')"
										:rules="[$rules.required($t('common.recipient-name'))]"
									/>
								</v-col>
								<v-col cols="12" sm="6" md="6" lg="6">
									<vc-phone
										v-model="form.phone"
										outlined
										name="phone"
										:label="$t('common.recipient-phone')"
										:rules="[$rules.required($t('common.phone'))]"
									/>
								</v-col>
							</v-row>
						</v-card-text>
					</v-card>
					<div class="my-8">
						<p>{{ $t("common.address") }}</p>
					</div>
					<v-card width="900">
						<v-card-text>
							<v-row>
								<v-col cols="12" sm="4" md="4" lg="4">
									<vc-text-field
										v-model="form.street"
										:label="$t('common.street')"
										autocomplete="street-address"
										:rules="[$rules.required($t('common.street'))]"
									/>
								</v-col>
								<v-col cols="12" sm="4" md="4" lg="4">
									<vc-text-field
										v-model="form.district"
										:label="$t('common.district')"
										autocomplete="on"
										:rules="[$rules.required($t('common.district'))]"
									/>
								</v-col>
								<v-col cols="12" sm="4" md="4" lg="4">
									<vc-text-field
										v-model="form.apartmentNumber"
										:label="$t('common.apartment-number')"
										autocomplete="on"
										:rules="[$rules.required($t('common.apartment-number'))]"
									/>
								</v-col>
								<v-col cols="12" sm="4" md="4" lg="4">
									<vc-text-field
										v-model="form.buildingNumber"
										:label="$t('common.building-number')"
										autocomplete="on"
										:rules="[$rules.required($t('common.building-number'))]"
									/>
								</v-col>
								<v-col cols="12" sm="4" md="4" lg="4">
									<vc-select
										v-model="form.cityId"
										:label="$t('common.city')"
										:rules="[$rules.required($t('common.city-id'))]"
										api="/v1/lookups-website/cities"
									/>
								</v-col>
							</v-row>
						</v-card-text>
						<div class="mx-4 pb-2">
							<v-card-actions class="d-flex justify-end">
								<v-btn color="primary" :loading="isLoading" type="submit">
									{{ $t("common.save-address") }}
								</v-btn>
							</v-card-actions>
						</div>
					</v-card>
				</v-form>
			</v-col>
		</v-row>
	</v-container>
</template>

<script>
export default {
	name: "AddressesEdit",
	layout: "website",
	data() {
		return {
			form: {
				phone: {
					code: "962",
					number: null,
					iso: "JO",
				},
				district: null,
				cityId: null,
				recipientName: null,
				apartmentNumber: null,
				buildingNumber: null,
				street: null,
				default: false,
			},
			isLoading: false,
		}
	},
	async fetch() {
		await this.$axios.$get(`/v1/my/addresses/${this.$route.params.id}`).then((resp) => {
			this.form = resp
		})
	},
	methods: {
		submit() {
			if (!this.$refs.form.validate()) {
				this.scrollToError(this.$refs.form)
				return
			}
			this.isLoading = true
			this.$axios
				.$put(`/v1/my/addresses/${this.$route.params.id}`, this.form)
				.then((resp) => {
					this.form = resp
					this.$toast.success(`${this.$t("common.has-been-updated-successfully")}`)
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.form)
				})
				.finally(() => {
					this.isLoading = false
				})
		},
	},
}
</script>
