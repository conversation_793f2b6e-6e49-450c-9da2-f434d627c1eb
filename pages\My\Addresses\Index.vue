<template>
	<v-skeleton-loader :loading="isLoading" type="card">
		<v-container fluid>
			<v-row>
				<v-col cols="12">
					<v-row align="center">
						<v-col cols="6">
							<h2>{{ $t("common.address-book") }}</h2>
						</v-col>

						<v-col cols="6">
							<v-btn color="primary" class="text-lowercase" :to="localePath({
								name: 'address-create',
							})
								">
								<v-icon left>
									mdi-plus
								</v-icon>
								<span class="text-capitalize">{{ $t("common.add-new-address") }}</span>
							</v-btn>
						</v-col>
					</v-row>
					<v-row>
						<v-col v-for="(address, index) in addresses" :key="index" cols="12" sm="12" md="12" lg="12" xl="12">
							<profile-my-addresses :item="address" @fetchAddress="fetchAddress" @deleted="deleteAddress" />
						</v-col>

						<v-col v-if="!addresses.length && !$fetchState.pending">
							<v-card flat>
								<v-card-text class="text-center">
									<div class="mb-8">
										<v-avatar color="primary" dark size="160" class="mx-auto">
											<v-icon size="80" color="white">
												mdi-domain
											</v-icon>
										</v-avatar>
									</div>
									No items in your addresses list. Consider adding new address to ease your next order
									process.
								</v-card-text>

								<v-card-actions>
									<v-btn block color="primary" text :to="localePath({
										name: 'address-create',
									})
										">
										Add address
									</v-btn>
								</v-card-actions>
							</v-card>
						</v-col>
					</v-row>
				</v-col>
			</v-row>
			<v-divider class="mx-4" vertical />
		</v-container>
	</v-skeleton-loader>
</template>

<script>
export default {
	name: "AddressesIndex",
	layout: "website",
	data() {
		return {
			addresses: [],
			isLoading: false,
		}
	},

	async fetch() {
		this.isLoading = true
		console.log("this.isLoading", this.isLoading)
		await this.$axios.$get(`v1/my/addresses`).then((resp) => {
			this.addresses = resp
			this.isLoading = false
		})
	},
	mounted() {
		this.$fetch()
	},
	methods: {
		fetchAddress() {
			this.$fetch()
		},
		deleteAddress(id) {
			const index = this.addresses.findIndex(item => item.addressId === id)
			this.addresses.splice(index, 1)
		},
	},
}
</script>
