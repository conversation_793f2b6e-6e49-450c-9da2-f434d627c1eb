<template>
	<v-container>
		<medium-container class="pt-md-8">
			<v-row class="flex-nowrap">
				<v-col cols="auto" class="me-4" :class="{ 'hidden-sm-and-down': !isIndex }">
					<MyMenu />
				</v-col>

				<v-col class="pa-0 pa-md-4">
					<client-only><nuxt-child /></client-only>
				</v-col>
			</v-row>
		</medium-container>
	</v-container>
</template>

<script>
export default {
	name: "Profile",
	layout: "website",
	middleware: ["auth"],
	computed: {
		isIndex() {
			return this.getRouteBaseName() === "my-index"
		},
	},

}
</script>
