<template>
	<v-skeleton-loader type="table" :loading="loading">
		<div>
			<h2>
				{{ $t("title.orders") }}
			</h2>
			<v-data-table elevation="0" outlined :items-per-page="itemsPerPage" :items-length="totalItems" :items="items"
				:headers="columns" :loading="loading" item-value="value">
				<template #item.orderItems="{ item }">
					<div v-for="(order, index) in item.orderItems" :key="index">
						{{ order.snapshot.product?.name }}
					</div>
				</template>
				<template #item.status="{ item }">
					<v-chip dark :color="getStatus(item.status).color">
						{{ getStatus(item.status).text }}
					</v-chip>
				</template>
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>
				<template #item.actions="{ item }">
					<v-btn text color="primary" :to="localePath({
						name: 'my-order',
						params: {
							order: item.orderId,
						},
					})
						">
						{{ $t("common.order-details") }}
						<v-icon class="rtl-rotate" right>
							mdi-chevron-right
						</v-icon>
					</v-btn>
				</template>
			</v-data-table>
		</div>
	</v-skeleton-loader>
</template>

<script>
export default {
	data() {
		return {
			value: {},
			items: [],
			columns: [
				{
					text: "#",
					sortable: true,
					value: "orderId",
				},
				{
					text: this.$t("common.products"),
					sortable: true,
					value: "orderItems",
				},
				{
					text: this.$t("common.total"),
					sortable: true,
					value: "total.value",
				},
				{
					text: this.$t("common.status"),
					sortable: true,
					value: "status",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {},
			itemsPerPage: 50,
			totalItems: 0,
			loading: false,
		}
	},

	async fetch() {
		this.loading = true
		await this.$axios
			.$get("/v1/my/orders")
			.then((resp) => {
				this.items = resp.items
				if (resp.pagination) {
					this.$refs.content.pagination = resp.pagination
				}
				if (resp.filters) {
					this.filterObject = resp.filters
				}
			})
			.catch((error) => {
				this.genericErrorHandler(error)
			})
		this.loading = false
		return this.items
	},
	computed: {},
	watch: {},
	methods: {
		cancelOrder(orderId) {
			this.loading = true
			this.$axios.post(`/v1/my/orders/${orderId}/cancel`).then((resp) => {
				this.$fetch()
			}).finally(() => {
				this.isLoading = false
			})
		},
		deleteOrder(orderId) {
			this.isLoading = true
			this.$axios.delete(`/v1/my/orders/${orderId}`).then((resp) => {
				this.$fetch()
			}).finally(() => {
				this.isLoading = false
			})
		},
		getStatus(status) {
			switch (status) {
				case "draft":
					return { color: "warning", text: this.$t("common.incomplete-order") }
				case "editing":
					return { color: "warning", text: this.$t("common.editing") }
				case "received":
					return { color: "success", text: this.$t("common.received") }
				case "canceled":
					return { color: "grey", text: this.$t("common.canceled") }
				case "completed":
					return { color: "success", text: this.$t("common.completed") }
				case "processing":
					return { color: "success", text: this.$t("common.processing") }
				default:
					return {
						color: "grey",
						text: this.$t("common.unknown"),
					}
			}
		},
	},
}
</script>
