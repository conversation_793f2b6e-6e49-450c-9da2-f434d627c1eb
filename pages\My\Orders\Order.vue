<template>
	<v-container fluid>
		<h4>{{ $t("common.my-order") }} #{{ order?.orderId }}</h4>

		<v-card flat>
			<v-card-title>{{ $t("common.order-details") }}</v-card-title>
			<v-card-text>
				<v-row>
					<v-col cols="6" md="3">
						<v-label>Status</v-label>
					</v-col>
					<v-col>
						<v-chip>
							{{ order?.status }}
						</v-chip>
					</v-col>
				</v-row>
				<v-row>
					<v-col cols="6" md="3">
						<v-label>Payment</v-label>
					</v-col>
					<v-col>
						<v-chip :color="paymentColor">
							{{ order?.paymentStatus }}
						</v-chip>
					</v-col>
				</v-row>
				<v-row>
					<v-col cols="6" md="3">
						<v-label>Date</v-label>
					</v-col>
					<v-col>
						{{ order?.createdAt | dateTime }}
					</v-col>
				</v-row>
				<v-row>
					<v-col cols="6" md="3">
						<v-label>Recipient Name</v-label>
					</v-col>
					<v-col>
						{{ order?.address?.recipientName }}
					</v-col>
				</v-row>
				<v-row>
					<v-col cols="6" md="3">
						<v-label>Address</v-label>
					</v-col>
					<v-col>
						{{ order?.address?.district }} - {{ order?.address?.city }}, {{ order?.address?.country }}
						<div>
							{{ order?.address?.street }} - {{ order?.address?.buildingNumber }} - {{
								order?.address?.apartmentNumber }}
						</div>
					</v-col>
				</v-row>
				<!-- <pre>
					{{ order }}
				</pre> -->
				<v-row>
					<v-col cols="6" md="3">
						<v-label>Items</v-label>
					</v-col>
					<v-col>
						<v-row dense>
							<v-col v-for="(item, ind) in order?.orderItems && order?.orderItems" :key="ind" cols="12">
								<profile-my-orders :item="item" />
							</v-col>
							<v-col>
								<v-divider class="my-4" />
								<v-col cols="12">
									<div class="text-end mb-2">
										<v-label>{{ $t("common.subtotal") }}:</v-label> {{ order?.subTotal?.value | money }}
									</div>
									<div class="text-end mb-2">
										<v-label>{{ $t("common.shipping-price") }}:</v-label> {{ order?.shippingPrice?.value |
											money }}
									</div>
									<div class="text-end mb-2">
										<v-label>{{ $t("common.total") }}:</v-label> {{ order?.total?.value | money }}
									</div>
								</v-col>
							</v-col>
						</v-row>
					</v-col>
				</v-row>
			</v-card-text>
		</v-card>
	</v-container>
</template>

<script>
export default {
	layout: "website",
	data() {
		return {
			loading: true,
			order: {},
			e1: 1,
		}
	},

	async fetch() {
		await this.$axios.$get(`/v1/my/orders/${this.$route.params.order}`).then((resp) => {
			console.log("resp IN ORDER DETAILS", resp)
			this.order = resp
			this.loading = false
		})
	},
	computed: {
		paymentColor() {
			switch (this.order?.paymentStatus) {
				case "paid":
					return "success"
				case "unPaid":
					return "grey"
				case "partialPaid":
					return "warning"
				default:
					return "grey"
			}
		},
	},
}
</script>
