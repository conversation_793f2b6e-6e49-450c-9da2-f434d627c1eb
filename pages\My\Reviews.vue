<template>
	<div>
		<h2>
			{{ $t("title.ratings") }}
		</h2>
		<v-data-table :items-per-page="itemsPerPage" :items-length="totalItems" :items="items" :headers="headers"
			:loading="loading" item-value="value">
			<template #item.rating="{ item }">
				<v-rating :value="item.rating" background-color="default" dense color="warning" readonly small length="5" />
			</template>
			<template #item.createdAt="{ item }">
				{{ item.createdAt | dateTime }}
			</template>
		</v-data-table>
	</div>
</template>

<script>
export default {
	name: "Reviews",
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "ratingId",
				},
				{
					text: this.$t("ratings.product"),
					sortable: true,
					value: "product.name",
				},
				{
					text: this.$t("ratings.rating"),
					sortable: true,
					value: "rating",
				},
				{
					text: this.$t("ratings.review"),
					sortable: true,
					value: "review",
				},
				{
					text: this.$t("ratings.status"),
					sortable: true,
					value: "status",
				},

				{
					text: this.$t("ratings.Created-at"),
					sortable: false,
					value: "createdAt",
				},

				// {
				// 	text: this.$t("common.action"),
				// 	sortable: false,
				// 	value: "actions",
				// },
			],
			defaultItem: {},
			items: [],
			headers: [
				{
					text: this.$t("ratings.rating"),
					value: "rating",
				},
				{
					text: this.$t("ratings.review"),
					value: "review",
				},
				{ text: this.$t("ratings.product-number"), value: "product.productId" },
				{ text: this.$t("ratings.product-name"), value: "product.name" },
				{ text: this.$t("ratings.status"), value: "status" },
				{ text: this.$t("common.created-at"), value: "createdAt" },
			],
			filterObject: {},
			loading: true,
			itemsPerPage: 5,
			totalItems: 0,
			itemsTest: [
				{
					ratingId: 2404,
					review: "Minus nam ducimus corporis et deleniti nesciunt. In amet alias natus voluptate nostrum doloribus. Voluptatum est sint nihil. Adipisci nesciunt recusandae minus similique delectus voluptas.",
					rating: 2,
					userId: 13,
					productId: 833,
					status: "UnPublished",
					createdAt: "2024-03-04T11:13:49.000000Z",
					updatedAt: "2024-03-04T11:13:49.000000Z",
					product: {
						productId: 833,
						description:
							"Natus aut pariatur culpa soluta magnam voluptatem. Delectus nesciunt a consequuntur totam ut et ipsa.",
						name: "Huawei Nova 5 T Antwan Witting",
					},
				},
			],
		}
	},
	async fetch() {
		await this.$axios
			.$get("/v1/my/ratings")
			.then((resp) => {
				this.items = resp.items
				if (resp.pagination) {
					this.$refs.content.pagination = resp.pagination
				}
				if (resp.filters) {
					this.filterObject = resp.filters
				}
			})
			.catch((error) => {
				this.genericErrorHandler(error)
			})
		this.loading = false
		return this.items
	},

	methods: {},
}
</script>
