<template>
	<v-container>
		<v-row class="mt-6">
			<v-col cols="12">
				<div>
					<h4>
						{{ $t("common.edit-address") }}
					</h4>
				</div>
				<v-form ref="form" @submit.prevent="submit">
					<div class="my-8">
						<p>{{ $t("common.used-product") }}</p>
					</div>
					<v-card width="900">
						<v-card-text>
							<v-row>
								<v-col cols="12" lg="12">
									<translatable v-slot="{ value, update, on, dir }" v-model="item.name">
										<vc-text-field :label="$t('common.name')" :rules="[$rules.required($t('common.name'))]"
											:value="value" :dir="dir" @input="update" v-on="on" />
									</translatable>
								</v-col>
							</v-row>

							<v-row>
								<v-col cols="12" lg="12">
									<Translatable v-slot="{ on, dir, update, value }" v-model="item.description">
										<vc-wysiwyg :label="$t('common.description')" :value="value" :dir="dir" @input="update"
											v-on="on" />
									</Translatable>
								</v-col>
							</v-row>

							<v-row>
								<v-col cols="12" sm="6" md="6" lg="6">
									<used-categories v-model="item.usedCategories" />
								</v-col>

								<v-col cols="12" sm="6" md="6" lg="6">
									<vc-select :key="item.usedCategories.join(',')" v-model="item.brandId"
										:label="$t('common.brand')" :rules="[$rules.required($t('common.brands'))]"
										:api="'/v1/lookups/brands?used-categories=' + item.usedCategories.join(',')"
										:disabled="!item.usedCategories.length" />
								</v-col>
							</v-row>

							<v-row>
								<v-col cols="12" sm="12" md="12" lg="12">
									<used-product-attributes v-model="item.attributes" :categories="item.usedCategories" />
								</v-col>
							</v-row>

							<v-row>
								<v-col cols="12" lg="12">
									<vc-select v-model="item.cityId" :label="$t('common.city')"
										:rules="[$rules.required($t('common.cities'))]" api="/v1/lookups/cities" />
								</v-col>
							</v-row>

							<v-row />
							<v-row>
								<v-col cols="12" lg="12">
									<money-field v-model="item.price" :label="$t('common.price')" />
								</v-col>
							</v-row>
						</v-card-text>

						<div class="mx-4 pb-2">
							<v-card-actions class="d-flex justify-end">
								<v-btn color="primary" :loading="isLoading" type="submit">
									{{ $t("common.save-used-product") }}
								</v-btn>
							</v-card-actions>
						</div>
					</v-card>
				</v-form>
			</v-col>
		</v-row>
	</v-container>
</template>

<script>
export default {
	name: "UsedProductsEdit",
	layout: "website",
	data() {
		return {
			item: {
				name: {},
				description: {},
				usedCategories: [],
				attributes: [],
				brandId: null,
				cityId: null,

				price: {
					value: "",
					basePrice: "",
					currencyId: "",
				},
			},

			isLoading: false,
		}
	},
	async fetch() {
		await this.$axios.$get(`/v1/my/used-products/${this.$route.params.usedProduct}?trans=off`).then((resp) => {
			this.item = resp
		})
	},
	methods: {
		submit() {
			if (!this.$refs.form.validate()) {
				this.scrollToError(this.$refs.form)
				return
			}
			this.isLoading = true
			this.$axios
				.$put(`/v1/my/used-products/${this.$route.params.usedProduct}`, this.item)
				.then((resp) => {
					this.item = resp
					this.$toast.success(`${this.$t("common.has-been-updated-successfully")}`)
					this.$router.push(
						this.localePath({
							name: "my-ads",
						}),
					)
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.form)
				})
				.finally(() => {
					this.isLoading = false
				})
		},
	},
}
</script>
