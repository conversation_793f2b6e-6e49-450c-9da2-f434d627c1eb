<template>
	<page :title="$t('title.ads')">
		<div>
			<div class="mx-4 pb-2">
				<v-card-actions class="d-flex justify-end">
					<v-btn
						color="primary"
						:to="
							localePath({
								name: 'add-used-product',
							})
						"
					>
						{{ $t("common.post-ad-now") }}
					</v-btn>
				</v-card-actions>
			</div>

			<data-table ref="dataTable" :columns="columns" api="/v1/my/used-products">
				<template #item.createdAt="{ item }">
					{{ item.createdAt | dateTime }}
				</template>
				<template #item.isPublished="{ item }">
					<status :items="isPublishedItems" :value="item.isPublished" />
				</template>

				<template #item.price="{ item }">
					{{ (item.price?.price || item.price?.currency) | money }}
				</template>
				<template #item.actions="{ item }">
					<v-btn
						v-tooltip="$t('common.delete')"
						small
						icon
						:loading="itemLoading === item.usedProductId"
						@click="deleteUsedProduct(item.usedProductId)"
					>
						<v-icon small>
mdi-delete
</v-icon>
					</v-btn>

					<v-btn
						small
						icon
						:to="
							localePath({
								name: 'edit-used-product',
								params: {
									usedProduct: item.usedProductId,
								},
							})
						"
					>
						<v-icon small>
mdi-pencil
</v-icon>
					</v-btn>
				</template>
			</data-table>
		</div>
	</page>
</template>

<script>
import { isPublished } from "~/config/Enum"
export default {
    name: "UsedProductsIndex",
	data() {
		return {
			isPublishedItems: isPublished,
			itemLoading: null,
			columns: [
				{
					text: "#",
					sortable: true,
					value: "usedProductId",
				},
				{
					text: "used-product",
					sortable: true,
					value: "name",
				},
				{
					text: "status",
					sortable: true,
					value: "isPublished",
				},
				{
					text: "price",
					sortable: true,
					value: "price",
				},

				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},

				{
					text: this.$t("common.action"),
					sortable: false,
					value: "actions",
				},
			],
			defaultItem: {},
		}
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh()
		},
		deleteUsedProduct(usedProduct) {
			this.itemLoading = usedProduct
			this.$confirm(this.$t("messages.confirm-delete-text"), {
				title: this.$t("messages.confirm-delete-used-product"),
			}).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`v1/my/used-products/${usedProduct}`)
						.then((res) => {
							this.$toast.success(this.$t("messages.has-been-deleted-successfully"))
						})
						.catch((error) => {
							console.log(error)
						})
						.finally(() => {
							this.itemLoading = null
						})
				}
			})
		},
	},
}
</script>
