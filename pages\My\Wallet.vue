<template>
	<div>
		<h2>
			{{ $t("title.transactions") }}
		</h2>
		<v-data-table :items-per-page="itemsPerPage" :items-length="totalItems" :items="items" :headers="columns"
			:loading="loading" item-value="value">
			<template #item.amount="{ item }">
				{{ item.amount | money }}
			</template>
			<template #item.status="{ item }">
				{{ item.status }}
			</template>
			<template #item.createdAt="{ item }">
				{{ item.createdAt | dateTime }}
			</template>
		</v-data-table>
	</div>
</template>

<script>
export default {
	data() {
		return {
			value: {},
			columns: [
				{
					text: "#",
					sortable: true,
					value: "transactionId",
				},
				{
					text: this.$t("common.amount"),
					sortable: false,
					value: "amount",
				},
				{
					text: this.$t("common.created-at"),
					sortable: false,
					value: "createdAt",
				},
				{
					text: this.$t("common.status"),
					sortable: false,
					value: "status",
				},
				// {
				// 	text: this.$t("common.action"),
				// 	sortable: false,
				// 	value: "actions",
				// },
			],
			defaultItem: {},
			items: [],
			loading: true,
			totalItems: 0,
			itemsPerPage: 10,
		}
	},
	async fetch() {
		await this.$axios
			.$get("/v1/my/transactions")
			.then((resp) => {
				console.log("resp", resp.data)
				this.items = resp.data
				if (resp.pagination) {
					this.$refs.content.pagination = resp.pagination
				}
				if (resp.filters) {
					this.filterObject = resp.filters
				}
			})
			.catch((error) => {
				this.genericErrorHandler(error)
			})
		this.loading = false
		return this.items
	},
}
</script>
