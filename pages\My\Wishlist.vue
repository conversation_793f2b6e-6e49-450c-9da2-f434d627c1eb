<template>
	<v-container>
		<v-row>
			<v-col>
				<h2>{{ $t("title.wishlist") }}</h2>
			</v-col>
		</v-row>
		<v-row class="mt-6">
			<v-col v-for="(product, index) in products" :key="index" cols="6" sm="3" md="4" lg="4" xl="3">
				<ProductCard :item="product" :loading="isFetching" @wishlist:remove="removeFromList" />
			</v-col>
		</v-row>
		<no-wishlist-items v-if="!products?.length && !$fetchState.pending" />

		<v-pagination v-if="pagination?.total > pagination?.perPage" v-model="pagination.page" :total-visible="9"
			:disabled="isFetching" :items-per-page="pagination.perPage" :length="pagination?.total"
			@input="paginationHandler" />
	</v-container>
</template>

<script>
export default {
	name: "Wishlist",
	layout: "website",

	data() {
		return {
			products: [],
			pagination: {
				total: null,
				count: 0,
				perPage: 6,
				page: Number(this.$route.query.page) || 1,
				lastPage: null,
			},
		}
	},

	async fetch() {
		this.products = this.pagination.perPage

		const query = {
			perPage: this.pagination.perPage,
		}
		const promises = []

		promises.push(
			this.$axios
				.$get(`v1/my/wishlist`, {
					params: {
						...this.$route.query,
						...query,
					},
				})
				.then((resp) => {
					this.products = resp
					this.pagination = resp.pagination
				}),
		)

		await Promise.all(promises).catch((error) => {
			this.genericErrorHandler(error)
		})
	},

	methods: {
		removeFromList(id) {
			const index = this.products.findIndex(item => item.productId === id)
			this.products.splice(index, 1)
			this.$toast.success(`${this.$t("common.product-has-been-deleted-from-your-wishlist")}`)
		},
		paginationHandler(page) {
			this.isFiltering = true
			const query = {
				...this.$route.query,
			}
			if (page > 1) {
				query.page = page
			} else {
				delete query.page
			}

			this.$router
				.push({
					name: this.$route.name,
					query,
				})
				.then(() => {
					this.$fetch()
				})
		},
	},
}
</script>
