<template>
	<div />
</template>

<script>
export default {
	layout: 'website',
	async asyncData({ params, $axios, error, redirect, store, i18n, localePath }) {
		return await $axios
			.$get(`/v1/products-by-old-id/${params.productId}`)
			.then((response) => {
				const productSlug = response.slug
				const varianceSlug = response.variance.slug
				const path = localePath({ name: 'product', params: { productSlug, varianceSlug } })
				redirect(301, path)
			})
			.catch((error1) => {
				error({ statusCode: 404, message: error1 })
			})
	},
}
</script>
