<template>
	<v-container>
		<div class="my-10">
			<h1 class="text-center">
				{{ page.name }}
			</h1>
			<v-card flat class="px-6 py-4 my-4 rounded-lg" v-html="page.body" />
		</div>
	</v-container>
</template>

<script>
export default {
	name: "Page",
	layout: "website",

	data() {
		return {
			page: {},
		}
	},

	async fetch() {
		await this.$axios.$get(`v1/pages/${this.$route.params.slug}`).then((resp) => {
			this.page = resp
		})
	},
	head() {
		return {
			title: this.metaTitle,
			titleTemplate: (titleChunk) => {
				const appName = this.$applicationName()
				return titleChunk ? `${appName} | ${titleChunk}` : appName
			},
			meta: [
				{
					hid: "description",
					name: "description",
					content: this.metaDescription,
				},
				{
					hid: "og:title",
					property: "og:title",
					content: this.metaTitle,
				},
				{
					hid: "og:description",
					property: "og:description",
					content: this.metaDescription,
				},
				{
					hid: "og:image",
					property: "og:image",
					content: "/images/logo.png",

				},
				{
					hid: "og:url",
					property: "og:url",
					content: this.$route.fullPath,
				},
				{
					hid: "og:type",
					property: "og:type",
					content: "category",
				},
				{
					hid: "og:site_name",
					property: "og:site_name",
					content: "Action.jo",
				},
				{
					hid: "twitter:title",
					name: "twitter:title",
					content: this.metaTitle,
				},
				{
					hid: "twitter:description",
					name: "twitter:description",
					content: this.metaDescription,
				},
				{
					hid: "twitter:image",
					name: "twitter:image",
					content: "/images/logo.png",
				},
				{
					hid: "twitter:card",
					name: "twitter:card",
					content: "summary_large_image",
				},
			],
			link: this.generateLinks,
		}
	},
	computed: {
		pageTitle() {
			return this.page.name
		},

		metaTitle() {
			return this.page.metaTitle || ''
		},
		metaDescription() {
			return this.page.metaDescription || ''
		},
		generateLinks() {
			if (this.$fetchState.pending) { return [] }

			const links = [
				{
					rel: "canonical",
					href: `${process.env.NUXT_ENV_BASE_URL}/${this.$i18n.locale}/page/${this.pagePath}`,
				},
				{
					rel: "alternate",
					hreflang: `ar-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/ar/page/${this.pagePath}`,
				},
				{
					rel: "alternate",
					hreflang: `en-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/en/page/${this.pagePath}`,
				},
				{
					rel: "alternate",
					hreflang: "x-default",
					href: `${process.env.NUXT_ENV_BASE_URL}/en/page/${this.pagePath}`,
				},
			]
			return links
		},

		pagePath() {
			return this.$route.params.slug
		},

	},
}
</script>

<style lang="scss">
@import "~/assets/variables.scss";

.product-page {
	.main-image {
		// filter: $product-drop-shadow;
		overflow: hidden;
	}
}
</style>
