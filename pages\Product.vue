<template>
	<v-container>
		<div class="my-2">
			<v-skeleton-loader :loading="isFetching" type="text">
				<breadcrumbs :items="breadcrumbItems" />
			</v-skeleton-loader>
		</div>
		<section :class="{ 'muted-4': isSwitchingVariance }" :style="{ minHeight: isFetching ? '100dvh' : undefined }">
			<v-row>
				<v-col cols="12" xxl="5" xl="5" lg="5" md="5" sm="12" xs="12">
					<v-skeleton-loader :loading="isFetching" type="heading" class="hidden-md-and-up">
						<div>
							<v-sheet class="d-flex">
								<v-sheet>
									<nuxt-img v-if="product.brand?.media?.logoName?.length" provider="s3"
										style="width: auto; max-height: 24px; max-width:100px"
										:src="product.brand.media.logoName[0].preview || '/images/logo-light.webp'" />
									<div v-else>
										{{ product.brand?.name }}
									</div>
								</v-sheet>
								<v-spacer />
								<ShareButtons :product="product" />
								<v-btn v-if="!$store.getters['wishlist/isWishlisted'](product.productId)" color="primary"
									:loading="isWishlisting" icon @click.prevent="toggleWishlist">
									<v-icon>mdi-heart-outline</v-icon>
								</v-btn>
								<v-btn v-else color="primary" :loading="isWishlisting" icon @click.prevent="toggleWishlist">
									<v-icon>mdi mdi-heart</v-icon>
								</v-btn>
							</v-sheet>
							<template v-if="isTypeAlternative">
								<h1>{{ product?.name }}</h1>
								<div class="text--secondary text-h6">
									{{ currentVariance?.name }}
								</div>
							</template>
							<template v-else>
								<h1>
									{{ title }}
								</h1>
							</template>
						</div>
					</v-skeleton-loader>
					<v-skeleton-loader type="image@2" :loading="isFetching">
						<div>
							<product-gallery v-if="gallery.length" :items="gallery" />
							<v-img v-else :aspect-ratio="4 / 3" cover src="/images/product-placeholder.webp" />
						</div>
					</v-skeleton-loader>
				</v-col>

				<v-col cols="12" xxl="4" xl="4" lg="4" md="4" sm="12" xs="12">
					<v-skeleton-loader :loading="isFetching" type="heading" class="hidden-sm-and-down">
						<div>
							<v-sheet class="d-flex">
								<v-sheet>
									<nuxt-img v-if="product.brand?.media?.logoName?.length"
										style="width: auto; max-height: 24px;max-width:100px"
										:src="product.brand.media.logoName[0].preview || '/images/logo-light.webp'" provider="s3" />
									<div v-else>
										{{ product.brand?.name }}
									</div>
								</v-sheet>
								<v-spacer />
								<ShareButtons :product="product" />

								<v-btn v-if="!$store.getters['wishlist/isWishlisted'](product.productId)" color="primary"
									:loading="isWishlisting" icon @click.prevent="toggleWishlist">
									<v-icon>mdi-heart-outline</v-icon>
								</v-btn>
								<v-btn v-else color="primary" :loading="isWishlisting" icon @click.prevent="toggleWishlist">
									<v-icon>mdi mdi-heart</v-icon>
								</v-btn>
							</v-sheet>
							<template v-if="isTypeAlternative">
								<div class="text-h5">
{{ product?.name }}
</div>
								<div class="text--secondary text-h6">
									{{ currentVariance?.name }}
								</div>
							</template>
							<h1 v-else>
								{{ title }}
							</h1>
						</div>
					</v-skeleton-loader>
					<v-skeleton-loader :loading="isFetching" type="paragraph@3">
						<div>
							<div v-if="currentVariance?.SKU && !isTypeBundle" class="muted-3 text-subtitle-2">
								{{ $t("product.product-sku") }} {{ currentVariance?.SKU }}
							</div>

							<div class="d-flex align-center">
								<v-rating :value="ratings.avgRating" background-color="default" dense readonly small
									color="warning" length="5" />
								<span v-if="ratings.avgRating" class="text-subtitle-2 mx-2">({{ ratings.avgRating }})</span>
								<v-btn plain small @click="$vuetify.goTo('#v-lazy-write-a-review')">
									<v-icon small left>
										mdi-pencil
									</v-icon>
									{{ $t("product.write-a-review") }}
								</v-btn>
							</div>
							<div class="py-4">
								<div v-if="isItemAvailable" class="mb-3">
									<div v-if="discount">
										<div class="d-flex align-center my-4">
											<sup class="muted-1 text-subtitle-1 pe-2 error--text">
												{{ $t("product.was") }}
												<del>{{ isTypeBundle ? product?.stock.priceBeforeOffer :
													currentVariance?.stock.priceBeforeOffer
													|| 60 | money }} </del>
											</sup>

											<span class="text-h5 price--text me-4">
												{{ $t("product.now") }}
												{{ isTypeBundle ? product?.stock.price : currentVariance?.stock.price | money }}
											</span>
											<span class="text-subtitle-1 me-4 text--secondary d-flex align-center">
												<v-icon small left>mdi-tag</v-icon>{{ $t("product.save") }} {{ discount || 10 |
													money }}
											</span>
										</div>
										<product-offer-count-down :date="discountExpiresIn" />
									</div>
									<div v-else>
										<span class="text-h5 price--text me-4">
											{{ isTypeBundle ? product?.stock.price : currentVariance?.stock.price | money }}
										</span>
										<v-sheet v-if="isPreOrder" shaped class="pre-order-label-detail float-left mt-2">
											<span> {{ $t('product.pre-order') }}</span>
										</v-sheet>
									</div>
								</div>
								<div v-else>
									<v-alert type="warning" border="left" dense text>
										{{ $t("product.this-item-is-out-of-stock")
										}}
									</v-alert>
								</div>
								<v-form @submit.prevent="switchVariance">
									<div v-for="(variationAttribute, variationAttributeIndex) in product.variationAttributes"
										:key="`variance-option-${variationAttributeIndex}`" class="mb-3">
										<h2 class="font-weight-bold text-subtitle-1">
											{{ variationAttribute?.name }}
										</h2>
										<v-row>
											<v-col v-for="(option, optionIndex) in variationAttribute.options"
												:key="`variance-option-${variationAttributeIndex}-option-${optionIndex}`"
												cols="auto">
												<!-- <template v-if="variationAttribute.type === 'color'">
													<color-btn :color="option.hexCode" /> {{ option.name }}
												</template>
												<template v-else> -->
												<v-btn ref="attributeOption" color="primary"
													:outlined="!selectedVarianceOptionsIds.includes(option.attributeOptionId)"
													active-class="primary--border" type="submit" depressed
													:value="option.attributeOptionId"
													:input-value="selectedVarianceOptionsIds.includes(option.attributeOptionId)"
													@click="
														replaceSelectedVarianceOptionId(
															variationAttribute.value.attributeOptionId,
															option.attributeOptionId
														)
														">
													{{ option.name }}
													{{ variationAttribute.prefix }}
													{{ variationAttribute.suffix }}
												</v-btn>
												<!-- </template> -->
											</v-col>
										</v-row>
									</div>
								</v-form>
								<v-row v-if="isItemAvailable">
									<v-col class="d-inline pt-4">
										<v-list-item>
											<v-list-item-icon class="me-2">
												<v-icon color="success">
													mdi-check
												</v-icon>
											</v-list-item-icon>
											<v-list-item-title>{{ $t("product.availability-in-stock") }}</v-list-item-title>
										</v-list-item>
									</v-col>
								</v-row>
								<v-container fluid class="pa-0">
									<v-row v-if="isTypeBundle" align="center" dense>
										<template v-for="bundle, i in product.bundles">
											<v-col :key="`bundle-${bundle.bundleId}`" cols="auto" class="fill-height">
												<v-card color="var(--v-accent13-base)" flat
													class="d-flex align-center pa-2 fill-height" min-height="52">
													<!-- :to="bundle.slug?
													localePath({
														name: "product", params: { productSlug: bundle.slug?.[this.$i18n.locale], varianceSlug:
													bundle.variance?.slug?.[this.$i18n.locale], }, })
													:
													undefined" -->
													<div class="me-2">
														<nuxt-picture provider="s3" loading="lazy" sizes="xs:60px" width="60"
															class="img d-flex" :src="(Array.isArray(bundle?.media?.gallery) && bundle.media.gallery.length > 0 ? bundle.media.gallery[0].preview : null) ||
																(Array.isArray(bundle?.media?.covor) && bundle.media.covor.length > 0 ? bundle.media.covor[0].preview : null) ||
																(Array.isArray(bundle?.media?.gallery) && bundle.media.gallery.length > 0 ? bundle.media.gallery[0].preview : null) ||
																'/images/product-placeholder.webp'" />
													</div>
													<div class="text text-body-2">
														{{ bundle.name }}
													</div>
												</v-card>
											</v-col>
											<template v-if="product.bundles[i + 1]">
												<v-col :key="`bundle-plus-${bundle.bundleId}`" cols="auto">
													<v-icon>
														mdi-plus
													</v-icon>
												</v-col>
											</template>
										</template>
									</v-row>
								</v-container>
							</div>
						</div>
					</v-skeleton-loader>
				</v-col>
				<v-col cols="12" xxl="3" xl="3" lg="3" md="3" sm="12" xs="12">
					<sticky-sidebar>
						<v-skeleton-loader type="paragraph@3" :loading="isFetching">
							<v-card class="buy-card">
								<v-card-text>
									<v-list>
										<!-- <v-list-item v-if="currentVariance?.stock?.price">
											<v-list-item-icon>
												<v-icon large>
													mdi-cash
												</v-icon>
											</v-list-item-icon>
											<v-list-item-title>
												<div>
													{{ currentVariance?.stock?.price | money }}
												</div>
											</v-list-item-title>
										</v-list-item> -->
										<v-list-item v-for="(item, index) in product.paymentMethods" :key="index">
											<v-list-item-icon v-if="item?.media?.logo?.preview" center>
												<v-img max-width="28" :src="item.media.logo.preview" />
											</v-list-item-icon>
											<v-list-item-title>{{ item.name }}</v-list-item-title>
										</v-list-item>
									</v-list>

									<div class="d-flex align-center mt-4 mb-2">
										<b class="text-subtitle-2 font-weight-bold">{{ $t("product.quantity") }}</b>
										<v-spacer />
										<v-chip v-model.number="quantity" :disabled="!isItemAvailable" color="primary"
											class="text-center">
											<v-avatar v-ripple outlined icon small left @click="decrementQuantity">
												<v-icon small>
													mdi-minus
												</v-icon>
											</v-avatar>
											{{ quantity }}

											<v-avatar v-ripple outlined icon small right @click="incrementQuantity">
												<v-icon small>
													mdi-plus
												</v-icon>
											</v-avatar>
										</v-chip>
									</div>
									<div v-if="isPreOrder" class="d-flex align-center justify-center mt-4 mb-6">
										<v-sheet v-if="isPreOrder" shaped class="pre-order-label-detail float-left mt-2">
											<span> {{ notePreOrder }}</span>
										</v-sheet>
									</div>
									<v-btn large color="primary" block :loading="isAddingToCart" :disabled="!isItemAvailable"
										class="mb-2" @click="addToCart">
										<div class="d-flex justify-start fill-width">
											<div style="width: 25%" />
											<v-icon left size="28">
												mdi-cart-plus
											</v-icon>
											<span class="mx-4">{{ $t("product.add-to-my-cart") }}</span>
											<v-spacer />
										</div>
									</v-btn>

									<v-btn large color="primary" block outlined :disabled="!isItemAvailable" class="mb-2"
										:loading="isBuyingNow" @click="buyNow">
										<div class="d-flex justify-start fill-width">
											<div style="width: 25%" />
											<v-icon left size="28">
												mdi-cart-variant
											</v-icon>
											<span class="mx-4">{{ $t("product.buy-now") }}</span>
											<v-spacer />
										</div>
									</v-btn>
									<v-btn v-if="!$store.getters['wishlist/isWishlisted'](product.productId)" large
										color="primary" block text :loading="isWishlisting" @click.prevent="toggleWishlist">
										<div class="d-flex justify-start fill-width">
											<div style="width: 25%" />
											<v-icon left size="28">
												mdi-heart-outline
											</v-icon>
											<span class="mx-4">{{ $t("product.save-to-my-wishlist") }}</span>
										</div>
									</v-btn>
									<v-btn v-else large color="primary" block text :loading="isWishlisting"
										@click.prevent="toggleWishlist">
										<div class="d-flex justify-start fill-width">
											<div style="width: 25%" />
											<v-icon left size="28" color="wishlist">
												mdi mdi-heart
											</v-icon>
											<span class="mx-4">{{ $t("product.added-to-my-wishlist") }}</span>
										</div>
									</v-btn>
								</v-card-text>
							</v-card>
						</v-skeleton-loader>
					</sticky-sidebar>
				</v-col>
			</v-row>
		</section>

		<v-row>
			<v-col cols="12" xxl="6" xl="6" lg="6" md="6" sm="12" xs="12">
				<h2 class="mb-4">
					{{ $t("product.description") }}
				</h2>
				<div id="description" v-html="product.description" />
			</v-col>
			<v-col cols="auto">
				<v-divider vertical class="mx-2" />
			</v-col>
			<v-col cols="12" xxl="5" xl="5" lg="5" md="5" sm="12" xs="12">
				<sticky-sidebar>
					<h2 class="mb-4">
						{{ $t("product.specification") }}
					</h2>
					<v-simple-table>
						<tbody>
							<tr v-for="(attribute, a) in combinedAttributes" :key="'attr-' + a" :class="{ alt: a % 2 }">
								<td class="px-2">
									{{ attribute.name }}
								</td>
								<td v-if="attribute.type === 'textarea'">
									<pre>{{ attribute.prefix }} {{ attribute.value | attrValue(attribute.type) }} {{ attribute.suffix
									}} </pre>
								</td>
								<td v-else>
									{{ attribute.prefix }} {{ attribute.value | attrValue(attribute.type) }} {{ attribute.suffix
									}}
								</td>
							</tr>
						</tbody>
					</v-simple-table>
				</sticky-sidebar>
			</v-col>
		</v-row>
		<v-divider />
		<!-- Rating and reviews section -->
		<v-lazy id="v-lazy-write-a-review" min-height="100" class="mt-8">
			<product-rating :id="product.productId" />
		</v-lazy>

		<v-dialog v-model="afterAddToCartModal" max-width="800">
			<v-card>
				<!-- show success message and sho bought together items -->
				<v-card-title>{{ $t("product.product-added-to-cart") }}</v-card-title>
				<v-card-text>
					<div class="d-flex align-center">
						<v-icon large color="success">
							mdi-check-circle
						</v-icon>
						<span class="text-h5 mx-4">{{ $t("product.you-have-successfully-added-the-product-to-your-cart")
						}}</span>
					</div>
					<v-sheet class="mx-auto" max-width="800">
						<v-lazy>
							<v-slide-group class="pa-4" active-class="success" show-arrows>
								<v-slide-item v-for="(product, productIndex) in boughtTogetherItems"
									:key="`bought-together-${productIndex}`" v-slot="{
										/*active, toggle*/
									}" class="me-4">
									<product-card width="250" :item="product" :loading="isGettingBoughtTogetherItems" @click.native="handleProductCardClick(product)" />
								</v-slide-item>
							</v-slide-group>
						</v-lazy>
					</v-sheet>
				</v-card-text>
				<v-card-actions>
					<v-spacer />

					<v-btn color="primary" text @click="afterAddToCartModal = false">
						{{ $t("product.continue-shopping")
						}}
					</v-btn>
					<v-btn color="primary" text @click="viewCart">
						{{ $t("product.view-cart") }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</v-container>
</template>

<script>
import productMixins from "~/mixins/product.js"
export default {
	name: "ProductDetails",
	key: route => "product-page",
	mixins: [productMixins],
	layout: "website",
	data() {
		return {
			afterAddToCartModal: false,
			isAddingToCart: false,
			isBuyingNow: false,
			isGettingBoughtTogetherItems: false,
			boughtTogetherItems: [],
			quantity: 1,
			isSwitchingVariance: false,
			isWishlisting: false,
			selectedVarianceOptionsIds: [],
			ratings: [],
			product: {},
		}
	},

	async fetch() {
		const { productSlug, varianceSlug } = this.$route.params
		await this.$axios
			.$get(`/v1/products/${productSlug}${varianceSlug ? "/" + varianceSlug : ""}`)
			.then((res) => {
				this.product = res
				this.selectedVarianceOptionsIds = this.product.variationAttributes?.map((attribute) => {
					return attribute?.value?.attributeOptionId
				})
			})
			.catch((errors) => {
				if (errors.response.status === 301 && errors.response?.data?.details?.redirectUrl) {
					const path = this.localePath({ name: 'product', params: { productSlug: errors.response?.data?.details?.redirectUrl } })
					// this.$router.replace({ path, query: { status: 301 } })
					// this.$router.push(path)
					if (process.server) {
						this.$nuxt.context.redirect(301, path)
					} else {
						this.$router.replace(path)
					}
				} else {
					this.pageErrorHandler(errors)
				}
			})
	},

	head() {
		return {
			script: [
				{
					type: "application/ld+json",
					innerHTML: JSON.stringify(this.jsonld), // <- set jsonld object in data or wherever you want
				},
			],
			__dangerouslyDisableSanitizers: ["script"], // <- this is important
			titleTemplate: (titleChunk) => {
				const appName = this.$applicationName()
				return titleChunk ? `${titleChunk} | ${appName} ` : appName
			},
			title: this.metaTitle,
			meta: [
				{
					hid: "description",
					name: "description",
					content: this.metaDescription,
				},
				{
					hid: "og:title",
					property: "og:title",
					content: this.metaTitle,
				},
				{
					hid: "og:description",
					property: "og:description",
					content: this.stripTags(this.product?.description),
				},
				{
					hid: "og:image",
					property: "og:image",
					content:
						this.currentVariance?.media?.gallery?.[0]?.preview ||
						this.product.media?.cover?.[0]?.preview ||
						"/images/product-placeholder.webp",
				},
				{
					hid: "og:url",
					property: "og:url",
					content: this.$route.fullPath,
				},
				{
					hid: "og:type",
					property: "og:type",
					content: "product",
				},
				{
					hid: "og:site_name",
					property: "og:site_name",
					content: "Action.jo",
				},
				{
					hid: "twitter:title",
					name: "twitter:title",
					content: this.metaTitle,
				},
				{
					hid: "twitter:description",
					name: "twitter:description",
					content: this.metaDescription,
				},
				{
					hid: "twitter:image",
					name: "twitter:image",
					content: this.currentVariance?.media?.gallery?.[0] || this.product.media?.covers?.[0],
				},
				{
					hid: "twitter:card",
					name: "twitter:card",
					content: "summary_large_image",
				},
			],
			link: this.generateLinks,
		}
	},

	computed: {
		generateLinks() {
			if (this.$fetchState.pending) { return [] }
			const links = [
				{
					rel: "canonical",
					href: `${process.env.NUXT_ENV_BASE_URL}/${this.$i18n.locale}/product/${this.productSlug}`,
				},
			]
			const varianceSlug = this.varianceSlug
			if (varianceSlug !== undefined) {
				return links
			}
			links.push(
				{
					rel: "alternate",
					hreflang: `ar-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/ar/product/${this.productSlug}`,
				},
				{
					rel: "alternate",
					hreflang: `en-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/en/product/${this.productSlug}`,
				},
				{
					rel: "alternate",
					hreflang: "x-default",
					href: `${process.env.NUXT_ENV_BASE_URL}/en/product/${this.productSlug}`,
				},
			)
			return links
		},
		metaTitle() {
			const varianceSlug = this.varianceSlug
			if (varianceSlug !== undefined) {
				return this.currentVariance?.metaTitle || ''
			}
			return this.product?.metaTitle || ''
		},
		metaDescription() {
			const varianceSlug = this.varianceSlug
			if (varianceSlug !== undefined) {
				return this.currentVariance?.metaDescription || this.stripTags(this.product?.description) || ''
			}
			return this.product?.metaDescription || ''
		},
		varianceSlug() {
			const { varianceSlug } = this.$route.params
			return varianceSlug
		},
		jsonld() {
			const json = {
				"@context": "https://schema.org/",
				"@type": "Product",
				telephone: "+962791009595",
				name: this.title,
				image: this.product?.media?.cover?.[0]?.preview || this.product?.media?.gallery?.[0]?.preview,
				description: this.stripTags(this.product?.description),
				// mpn: "5607",
				address: {
					"@type": "PostalAddress",
					streetAddress: "buliding No.4 Ibn Al Fata St.,7th circle",
					addressLocality: "Amman",
					addressRegion: "AM",
					postalCode: "11181",
					addressCountry: "JO",
				},
				brand: {
					"@type": "Brand",
					name: this.product?.brand?.name,
				},
				aggregateRating: {
					"@type": "AggregateRating",
					ratingValue: "4",
					reviewCount: "1",
				},
				review: [
					{
						"@type": "Review",
						reviewRating: {
							"@type": "Rating",
							ratingValue: "4",
						},
						author: {
							"@type": "Person",
							name: "Action_5607",
						},
					},
				],
			}

			if (this.isItemAvailable) {
				json.offers = {
					"@type": "Offer",
					priceCurrency: "JOD",
					price: this.isTypeBundle ? this.product?.stock?.price?.value : this.product?.variance?.stock?.price?.value,
					priceValidUntil: "2024-04-20",
					availability: "http://schema.org/InStock",
					seller: {
						"@type": "Organization",
						name: "Action JO",
					},
				}
			}
			return json
		},
		title() {
			return this.product?.name
		},
		hasRatings() {
			return !!this.ratings
		},

		// hasCodPayment(){
		// 	return this.product?.paymentMethods?.find((method) => method.module === "Cod");
		// },
		// hasCreditCardPayment(){
		// 	return this.product?.paymentMethods?.find((method) => method.module === "CreditCard");
		// },

		variancesOptions() {
			if (this.isTypeAlternative) {
				// variancesOptions represents row
				return this.product.variationAttributes.map((attribute) => {
					return {
						...attribute,
						// options: attribute.options.map((option) => {
						// 	option.prefix = attribute.prefix;
						// 	option.suffix = attribute.suffix;

						// 	const attributeOptionsArray = this.generateAttributeOptionsArray(option) || [];
						// 	return {
						// 		...option,
						// 		attributeOptionsArray,
						// 		slug: this.generateOptionSlugFromAttributeOptionsArray(attributeOptionsArray),
						// 	};
						// }),
					}
				})
			}
			return null
		},

		discount() {
			//	updated it with "?" to recover if the object/value is null
			if (this.isTypeBundle && this?.stock?.isOffer) {
				return {
					value: this.stock.priceBeforeOffer?.value - this.stock.price?.value,
					currency: this.stock.price.currency,
				}
			}
			if (this.currentVariance?.stock?.isOffer) {
				return {
					value: this.currentVariance.stock.priceBeforeOffer?.value - this.currentVariance.stock.price?.value,
					currency: this.currentVariance.stock.price.currency,
				}
			}

			return null
		},

		isPreOrder() {
			return this.stock?.isPreOrder ?? false
		},
		notePreOrder() {
			if (this.isTypeBundle) {
				return !!this.product.stock?.note ?? ''
			}
			return this.currentVariance?.stock?.note ?? ''
		},
		breadcrumbItems() {
			return [
				{
					text: "Home",
					disabled: false,
					to: this.localePath({
						name: "index",
					}),
					link: true,
					nuxt: true,
					exactPath: true,
				},

				{
					text: this.title,
					disabled: true,
				},
			]
		},
		isTypeSimple() {
			return this.product.type === "simple"
		},
		isTypeBundle() {
			return this.product.type === "bundle"
		},
		isTypeAlternative() {
			return this.product.type === "alternative"
		},
		currentVariance() {
			// if (!this.isTypeAlternative) return null;
			// if (!this.$route.params.varianceSlug) {
			return this.product.variance
			// }
			// return this.product.alternatives.find((alternative) => alternative.slug === this.$route.params.varianceSlug);
		},
		productSlug() {
			return this.product.slug
		},
		gallery() {
			const arr = []
			if (this.currentVariance?.media?.gallery) {
				arr.push(...this.currentVariance?.media?.gallery)
			}
			if (this.product?.media?.gallery) {
				arr.push(...this.product?.media?.gallery)
			}
			return arr
		},
		isItemAvailable() {
			if (this.isTypeBundle) {
				return this.stock?.quantity && this.stock.quantity > 0
			}
			return this.currentVariance?.stock?.quantity && this.currentVariance.stock.quantity > 0
		},
		combinedAttributes() {
			try {
				if (this.isTypeBundle) {
					const attributes = this.product.bundles.flatMap((bundle) => {
						return bundle.attributes.map((attribute) => {
							return attribute
						})
					})
					return attributes
				} else {
					return [...this.currentVariance?.attributes, ...this.product?.attributes]
				}
			} catch (e) {
				// console.log(this.currentVariance, this.product);
				return []
			}
		},
		stock() {
			if (this.isTypeBundle) {
				return this.product.stock
			}
			if (this.isTypeAlternative || this.isTypeSimple) {
				return this.currentVariance?.stock
			}

			return false
		},
		hasStock() {
			return !!this.stock && this.stock.quantity > 0
		},
		hasDiscount() {
			if (this.hasStock) {
				return this.stock.isOffer
			}

			return false
		},
		discountExpiresIn() {
			if (this.hasDiscount) {
				return this.stock.unPublishedAt
			}
			return 0
		},
	},
	methods: {
		fullOptionSlug(newOptionSlug, oldOptionSlug) {
			// const currentSlug = this.currentVariance.slug;
			// const newSlug = currentSlug.replace(oldOptionSlug, newOptionSlug);

			// if (newSlug === currentSlug) {
			// 	console.error("newSlug === currentSlug", newOptionSlug, oldOptionSlug, currentSlug, newSlug);
			// 	return currentSlug;
			// }

			// // check if newSlug is valid
			// const newAlternative = this.product.alternatives.find((alternative) => alternative.slug === newSlug);
			// if (newAlternative) {
			// 	return newSlug;
			// }
			return "currentSlug"
		},
		addToCart() {
			if (!this.isItemAvailable) { return }
			this.isAddingToCart = true
			this.isGettingBoughtTogetherItems = true
			this.$store
				.dispatch("cart/add", {
					name: this.title,
					sku: !this.isTypeBundle ? this.currentVariance.SKU : null,
					media: !this.isTypeBundle ? this.currentVariance?.media : this.product?.media,
					productId: this.product.productId,
					varianceId: !this.isTypeBundle ? this.currentVariance.varianceId : null,
					quantity: this.quantity,
					price: !this.isTypeBundle ? this.currentVariance.stock.price : this.product.stock.price,
				})
				.then(() => {
					this.afterAddToCartModal = true
					this.getBoughtTogetherItems()
					this.$fb.track('AddToCart', {
						content_ids: [this.product.productId],
						content_name: this.title,
						content_type: 'product',
						value: this.currentVariance.stock.price.value,
						currency: this.currentVariance.stock.price.currency,
						quantity: this.quantity,
					})
				})
				.catch(() => {
					this.$toast.error("Couldn't add to cart right now. Please try again later.")
				})
				.finally(() => {
					this.isAddingToCart = false
				})

			// this.$store
			// 	.dispatch("cart/add", {
			// 		name: this.title,
			// 		sku: this.currentVariance.SKU,
			// 		media: image,
			// 		image,
			// 		productId: this.product.productId,
			// 		varianceId: this.currentVariance.varianceId,
			// 		quantity: this.quantity,
			// 		price: this.currentVariance.stock.price,
			// 	})
			// 	.then(() => {
			// 		this.afterAddToCartModal = true;
			// 	})
			// 	.catch(() => {
			// 		this.$toast.error("Couldn't add to cart right now. Please try again later.");
			// 	})
			// 	.finally(() => {
			// 		this.isAddingToCart = false;
			// 	});
		},
		incrementQuantity() {
			if (this.quantity < this.currentVariance.stock.quantity) {
				this.quantity++
			}
		},
		decrementQuantity() {
			if (this.quantity > 1) {
				this.quantity--
			}
		},
		handleProductCardClick(product) {
			const path = this.localePath({
				name: "product",
				params: { productSlug: product.slug },
			})
			window.open(path, '_blank')
		},
		async getBoughtTogetherItems() {
			this.isGettingBoughtTogetherItems = true
			try {
				const resp = await this.$axios.$get(`/v1/suggested-product/${this.product.productId}`)
				console.log("resp", resp)
				this.boughtTogetherItems = resp
				console.log("boughtTogetherItems", this.boughtTogetherItems)
			} catch (error) {
				console.error("Error fetching suggested products:", error)
				// Handle the error, such as displaying an error message to the user
			} finally {
				this.isGettingBoughtTogetherItems = false
			}
			// this.isGettingBoughtTogetherItems = true;

			// this.$axios
			// 	.$get(`/v1/suggested-product/${this.product.productId}`)
			// 	.then((resp) => {
			// 		this.boughtTogetherItems = resp.items;

			// 		console.log("boughtTogetherItems", this.boughtTogetherItems);
			// 	})
			// 	.finally(() => {
			// 		this.isGettingBoughtTogetherItems = false;
			// 	});
		},

		viewCart() {
			this.afterAddToCartModal = false
			this.$nuxt.$emit("cart.show")
		},
		buyNow() {
			this.isBuyingNow = true
			this.$axios
				.$post("/v1/orders/buy-now", {
					productId: this.product.productId,
					varianceId: this.product.type === "bundle" ? null : this.currentVariance.varianceId,
					quantity: this.quantity,
				})
				.then((resp) => {
					this.$router.push(
						this.localePath({
							name: "checkout",
							params: {
								orderId: resp.orderId,
								step: 1,
							},
						}),
					)
				})
				.finally(() => {
					this.isBuyingNow = false
				})
		},
		replaceSelectedVarianceOptionId(attributeOptionId, newOptionId) {
			// console.log("replaceSelectedVarianceOptionId");
			// console.log(attributeOptionId, newOptionId);
			const index = this.selectedVarianceOptionsIds.indexOf(attributeOptionId)
			if (index > -1) {
				this.selectedVarianceOptionsIds.splice(index, 1, newOptionId)
			}
			// console.log(this.selectedVarianceOptionsIds);
		},
		switchVariance() {
			this.isSwitchingVariance = true

			// get all selected options by looping through all attribute options buttons and get the value of the active one
			const attributesOptions = this.selectedVarianceOptionsIds // this.$refs.attributeOption.filter((option) => option.isActive).map((option) => option.value);
			// let product;
			this.$axios
				.$post("/v1/products/" + this.product.slug, {
					productId: this.product.productId,
					attributesOptions,
				})
				.then(async (resp) => {
					await this.$router.replace({
						...this.$router.currentRoute,
						params: {
							productSlug: resp.slug,
							varianceSlug: resp.variance?.slug,
						},
					})

					this.product = resp
					this.selectedVarianceOptionsIds = this.product.variationAttributes.map((attribute) => {
						return attribute.value.attributeOptionId
					})
				})
				.finally(() => {
					this.isSwitchingVariance = false
				})
		},
		toggleWishlist() {
			this.isWishlisting = true
			this.$store.dispatch("wishlist/toggle", this.product).finally(() => {
				this.isWishlisting = false
			})
		},
	},
}
</script>
<style lang="scss">
.pre-order-label-detail {
	background-color: red !important; // Choose your pre-order label color
	color: white !important;
	padding: 8px;
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 14px;
	font-weight: bold;
	color: #fff;
	display: inline-block;
	position: absolute;

	.pre-order-text {
		color: white;
		font-weight: bold;
		text-transform: uppercase;
		font-size: 12px;
	}
}
</style>
