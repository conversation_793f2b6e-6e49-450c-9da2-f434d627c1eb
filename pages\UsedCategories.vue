<template>
	<div>
		<slide-show />
		<v-container class="mt-n8">
			<v-row dense>
				<v-col v-for="(card, i) in serverData.cards" :key="i" cols="12" md="4">
					<v-img cover class="rounded-lg" :src="card" height="156" />
				</v-col>
			</v-row>
			<v-row class="mt-6">
				<v-col v-for="(usedCategory, index) in usedCategories" :key="index" cols="12" sm="3" md="4" lg="4" xl="3">
					<used-category-card :data="usedCategory" />
				</v-col>
			</v-row>
		</v-container>
	</div>
</template>

<script>
export default {
	name: "UsedCategories",
	layout: "website",
	data() {
		return {
			usedCategories: [],
			admins: [
				["Management", "mdi-account-multiple-outline"],
				["Settings", "mdi-cog-outline"],
			],
			cruds: [
				["Create", "mdi-plus-outline"],
				["Read", "mdi-file-outline"],
				["Update", "mdi-update"],
				["Delete", "mdi-delete"],
			],

			data: {
				products: [
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						discountExpiry: new Date(),
						isNew: true,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						discountExpiry: new Date(),
						isNew: true,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						discountExpiry: new Date(),
						isNew: true,
					},
				],
			},
			serverData: {
				cards: [
					require("~/assets/uploads/card-1.jpg"),
					require("~/assets/uploads/card-2.jpg"),
					require("~/assets/uploads/card-3.jpg"),
				],
				mostOrdered: [
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						discountExpiry: new Date(),
						isNew: true,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 700,
						priceBeforeDiscount: null,
						currency: "JOD",
						rating: 2,
						cashback: 5,
						discount: null,
						isNew: false,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						isNew: true,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						isNew: true,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						isNew: true,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						isNew: true,
					},
					{
						id: 1,
						slug: "iphone-13-pro",
						title: "IPhone 13 Pro",
						images: [require("~/assets/uploads/iphone13.png")],
						brand: "Apple",
						category: "Mobiles",
						price: 1100,
						priceBeforeDiscount: 1200,
						currency: "JOD",
						rating: 4,
						cashback: 5,
						discount: 100 - Math.round((1100 / 1200) * 100),
						isNew: true,
					},
				],
			},
		}
	},

	async fetch() {
		await this.$axios.$get(`v1/used-categories`).then((resp) => {
			this.usedCategories = resp.items
		})
	},
}
</script>

<style lang="scss">
@import "~/assets/mixins.scss";

.circle {
	width: 5px;
	/* Adjust the width and height to your desired size */
	height: 5px;
	margin-left: 2px;
	border-radius: 50%;
	/* Set the border-radius to 50% to create a circle */
	background-color: #29a6df;
	/* Set the background color to your desired color */
}

.flex-row {
	flex-wrap: wrap;
}

.item {
	flex-grow: 0;
	flex-basis: 100%;
}

.filter {
	width: 300px;
	@include responsive("width", 0, 300px);
	@include responsive("flex-shrink", 1, 0);
	@include responsive("margin-inline-end", 0, 16px);
	padding-inline-start: 8px;
	flex-shrink: 0;
}

.view {
	flex-grow: 1;
}

.toolbar {
	.icon {
		width: 36px;
		height: 36px;
		min-width: 0 !important;
		padding: 0 !important;
	}
}
</style>
