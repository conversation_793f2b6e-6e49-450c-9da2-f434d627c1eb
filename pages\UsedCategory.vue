<template>
	<v-container>
		<div class="d-flex mx-auto align-stretch">
			<v-navigation-drawer :value="true" :right="isRTL" width="300" class="filter">
				<div class="pe-1 pb-2">
					<component
						:is="`filters-${filter.type}`"
						v-for="(filter, objectKey) in filters"
						:key="objectKey"
						v-model="filter.value"
						:items="filter.options"
						:label="filter.label"
						@change="filterHandler"
					/>
				</div>
			</v-navigation-drawer>
			<div class="view">
				<v-skeleton-loader type="heading" min-height="48" :loading="isFetching && !isFiltering">
					<h1 class="mb-2">
{{ usedCategory.name }}
</h1>
				</v-skeleton-loader>
				<breadcrumbs :items="breadcrumbItems" />

				<v-toolbar flat>
					<!-- <v-btn icon>

<v-icon>mdi-view-list</v-icon>

</v-btn>

<v-btn icon>

<v-icon>mdi-view-grid</v-icon>

</v-btn> -->
					<v-spacer />
					<v-select
						v-model="orderBy"
						:items="OrderByItems"
						:label="$t('common.order-by')"
						hide-details
						style="max-width: 300px"
						@change="orderByHandler"
					/>
				</v-toolbar>

				<div class="mx-4 pb-2">
					<v-card-actions class="d-flex justify-end">
						<v-btn
							color="primary"
							:to="
								localePath({
									name: 'add-used-product',
								})
							"
						>
							{{ $t("common.post-ad-now") }}
						</v-btn>
					</v-card-actions>
				</div>

				<v-row class="mt-6">
					<v-col v-for="usedProduct in usedProducts" :key="usedProduct.key" cols="12" sm="3" md="4" lg="4" xl="3">
						<used-product-card :item="usedProduct" :loading="isFetching" />
					</v-col>
				</v-row>

				<v-pagination
					v-if="pagination.total > pagination.perPage"
					v-model="pagination.page"
					:total-visible="9"
					:disabled="isFetching"
					:items-per-page="pagination.perPage"
					:length="pagination.total"
					@input="paginationHandler"
				/>
			</div>
		</div>
	</v-container>
</template>

<script>
export default {
	name: "UsedCategory",
	layout: "website",
	data() {
		return {
			usedProducts: {},
			usedCategory: {},
			filters: null,
			orderBy: this.$route.query.orderBy || null,
			pagination: {
				total: null,
				count: 0,
				perPage: 16,
				page: Number(this.$route.query.page) || 1,
				lastPage: null,
			},
			isFiltering: false,
		}
	},
	async fetch() {
		// url can be any number of categories slug in path
		this.usedProducts = this.pagination.perPage

		const query = {
			...this.$route.query,
			perPage: this.pagination.perPage,
		}
		const promises = []
		promises.push(
			this.$axios.$get(`/v1/used-categories/${this.$route.params.usedCategory}`).then((resp) => {
				this.usedCategory = resp
			}),
		)
		promises.push(
			this.$axios
				.$get(`/v1/used-products`, {
					params: {
						usedCategory: this.$route.params.usedCategory,
						orderBy: this.orderBy,
						...query,
					},
				})
				.then((resp) => {
					this.usedProducts = resp.items
					this.filters = resp.filters
					this.pagination = resp.pagination
				}),
		)

		await Promise.all(promises).catch((error) => {
			this.genericErrorHandler(error)
		})
	},

	computed: {
		OrderByItems() {
			return [
				{
					text: this.$t("common.date-from-recent-to-oldest"),
					value: "createdAt,desc",
				},
				{
					text: this.$t("common.avg-customer-review"),
					value: "avgRate,desc",
				},
				{
					text: this.$t("common.avg-customer-review"),
					value: "avgRate,asc",
				},
				{
					text: this.$t("common.date-from-oldest-to-recent"),
					value: "createdAt,asc",
				},
				{
					text: this.$t("common.price-from-high-to-low"),
					value: "price,desc",
				},
				{
					text: this.$t("common.price-from-low-to-high"),
					value: "price,asc",
				},
			]
		},

		breadcrumbItems() {
			const items = [
				{
					text: this.$t("common.home"),
					disabled: false,
					to: this.localePath({
						name: "index",
					}),
					link: true,
					nuxt: true,
					exactPath: true,
				},
			]

			return items
		},
	},

	methods: {
		filterHandler() {
			this.isFiltering = true
			const query = {}
			for (const key in this.filters) {
				if (this.filters[key].value) {
					query[key] = this.filters[key].value
				}
			}

			this.$router
				.push({
					name: this.$route.name,
					params: this.$route.params,
					query,
				})
				.then(() => {
					this.$vuetify.goTo(0, {
						duration: 200,
					})
					this.$fetch()
				})
		},
		orderByHandler() {
			this.isFiltering = true
			const query = {
				...this.$route.query,
			}
			if (this.orderBy) {
				query.orderBy = this.orderBy
			}
			this.$router
				.push({
					name: this.$route.name,
					params: this.$route.params,
					query,
				})
				.then(() => {
					this.$fetch()
				})
		},
		paginationHandler(page) {
			this.isFiltering = true
			const query = {
				...this.$route.query,
			}
			if (page > 1) {
				query.page = page
			} else {
				delete query.page
			}

			this.$router
				.push({
					name: this.$route.name,
					params: this.$route.params,
					query,
				})
				.then(() => {
					this.$fetch()
				})
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/mixins.scss";

.filter {
	// width: 300px;
	@include responsive("width", 0, 300px);
	@include responsive("flex-shrink", 1, 0);
	@include responsive("margin-inline-end", 0, 16px);
	padding-inline-start: 10px;
	flex-shrink: 0;
	padding: 10px;
}

.view {
	flex-grow: 1;
}

.toolbar {
	.icon {
		width: 36px;
		height: 36px;
		min-width: 0 !important;
		padding: 0 !important;
	}
}
</style>
