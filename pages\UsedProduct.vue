<template>
	<v-container>
		<div class="my-2">
			<breadcrumbs :items="breadcrumbItems" />
		</div>
		<v-row>
			<v-col cols="5">
				<product-gallery :items="gallery" />
			</v-col>
			<v-col>
				<v-btn plain class="px-0" :to="localePath({ name: 'brands' })">
					{{ usedProduct.brand.name }}
				</v-btn>
				<h1>{{ title }}</h1>
				<div class="py-4">
					<div>
						{{ usedProduct }}
						<span class="text-h5 price--text">
							Price
							{{ usedProduct.price.price | money }}
						</span>

						<div>{{ usedProduct.user.phone.number }}</div>

						<div>{{ usedProduct.description }}</div>
					</div>

					<!--	<div v-for="(varianceOption, varianceOptionIndex) in variancesOptions" :key="`variance-option-${varianceOptionIndex}`">
						<div class="font-weight-bold text-subtitle-1">{{ varianceOption.name }}</div>
				<pre>{{ varianceOption.options }}</pre>
						<v-row>
							<v-col
								v-for="(option, optionIndex) in varianceOption.options"
								:key="`variance-option-${varianceOptionIndex}-option-${optionIndex}`"
							>
							<pre> {{ option }}</pre>
								<v-card
									outlined
									active-class="primary--border"
									exact-path
									:to="
										localePath({
											name: 'product',
											params: {
												productSlug: $route.params.productSlug,
												varianceSlug: fullOptionSlug(option.meta.slug, varianceOption.value.meta.slug),
											},
										})
									"
								>
									{{ fullOptionSlug(option.meta.slug, varianceOption.value.meta.slug) }}
									<v-card-title> {{ varianceOption.prefix }} {{ option.text }} {{ varianceOption.suffix }}</v-card-title>
								</v-card>
							</v-col>
						</v-row>
					</div>-->
				</div>
			</v-col>
		</v-row>
	</v-container>
</template>

<script>
export default {
	name: "UsedProduct",
	layout: "website",
	data() {
		return {
			usedProduct: {},
			media: {
				gallery: [
					{
						file_name: "0088ee.png",
						mime_type: "image/png",
						size: 7324,
						alt: null,
						url: "/images/demo1.png",
						preview: "",
					},
					{
						file_name: "0088ee.png",
						mime_type: "image/png",
						size: 7324,
						alt: null,
						url: "/images/demo2.png",
						preview: "",
					},
					{
						file_name: "0088ee.png",
						mime_type: "image/png",
						size: 7324,
						alt: null,
						url: "/images/demo3.png",
						preview: "",
					},
					{
						file_name: "0088ee.png",
						mime_type: "image/png",
						size: 7324,
						alt: null,
						url: "/images/demo1.png",
						preview: "",
					},
					{
						file_name: "0088ee.png",
						mime_type: "image/png",
						size: 7324,
						alt: null,
						url: "/images/demo2.png",
						preview: "",
					},
				],
			},
		}
	},
	async fetch() {
		await this.$axios.$get(`/v1/used-products/details/${this.$route.params.usedProduct}`).then((resp) => {
			this.usedProduct = resp
		})
	},
	computed: {
		title() {
			return this.usedProduct.name
		},

		breadcrumbItems() {
			return [
				{
					text: "Home",
					disabled: false,
					to: this.localePath({
						name: "action-souq",
					}),
					link: true,
					nuxt: true,
					exactPath: true,
				},

				{
					text: this.usedProduct.name,
					disabled: true,
				},
			]
		},

		gallery() {
			return this.usedProduct.media?.gallery || this.media.gallery
		},
	},
}
</script>
