<template>
	<div>
		<slide-show class="mt-4" />
		<v-container class="pe-0 pe-md-3 overflow-x-hidden">
			<v-row>
				<v-col>
					<h1 style="display: none;">{{ titleH1 }}</h1>
				</v-col>
				<v-col>
					<v-slide-group class="hide-disabled-arrows">
						<v-slide-item v-for="(item, index) in categories" :key="index">
							<category-card :item="item" />
						</v-slide-item>
					</v-slide-group>
				</v-col>
			</v-row>
			<v-row>
				<v-col>
					<products-slider :title="$t('common.offers')" :to="localePath({ name: 'offers' })" :loading="isFetching"
						:items="offers" />
				</v-col>
			</v-row>
			<v-row>
				<v-col>
					<brands-slider :title="$t('common.brands')" :loading="isFetching" :items="brands" />
				</v-col>
			</v-row>
			<v-row>
				<v-col>
					<products-slider :title="$t('common.new-arrival')" :loading="isFetching" :items="newArrival" />
				</v-col>
			</v-row>
		</v-container>
		<v-container>
			<v-row dense justify="center">
				<v-col v-for="(banner, i) in banners" :key="i" cols="12" sm="4">
					<template v-if="banner.url">
						<a :href="localePath(banner.url)" rel="noopener noreferrer" style="text-decoration: none;">
							<nuxt-img class="rounded-lg" :src="banner?.media?.image?.preview || '/images/banners/04.jpg'"
								sizes="xs:100vw md:286px lg:458px" width="100%" format="webp" style="max-width:100%" />
						</a>
					</template>
					<template v-else>
						<nuxt-img class="rounded-lg" :src="banner?.media?.image?.preview || '/images/banners/04.jpg'"
							sizes="xs:100vw md:286px lg:458px" width="100%" format="webp" style="max-width:100%" />
					</template>
				</v-col>
			</v-row>
		</v-container>
		<v-container class="pe-0 pe-md-3 overflow-x-hidden">
			<v-row>
				<v-col>
					<products-slider :title="$t('common.most-ordered')" :loading="isFetching" :items="mostPopular" />
				</v-col>
			</v-row>
		</v-container>
	</div>
</template>

<script>

export default {
	name: "Home",
	layout: "website",
	data() {
		return {
			data: {},
			serverData: {
				cards: [
					// "/images/banners/01.jpg",
					// "/images/banners/02.jpg",
					// "/images/banners/03.jpg",

					"/images/banners/04.jpg",
					"/images/banners/05.jpg",
					"/images/banners/06.jpg",
				],
				mostOrdered: [],
			},
			mostPopular: [],
			newArrival: [],
			offers: [],
			brands: [],
			banners: [],
		}
	},
	async fetch() {
		const promises = [
			this.$axios.$get(`v1/most-popular`).then((resp) => {
				this.mostPopular = resp
			}),
			this.$axios.$get(`v1/new-arrival`).then((resp) => {
				this.newArrival = resp
			}),
			this.$axios.$get(`v1/home-offers`).then((resp) => {
				this.offers = resp
			}),
			this.$axios.$get(`v1/lookups-website/brands?inHomePage=1`).then((resp) => {
				this.brands = resp
			}),
			this.$axios.$get(`v1/banners`).then((resp) => {
				this.banners = resp
			}),
		]

		await Promise.all(promises)
	},
	head() {
		return {
			script: [
				{
					type: "application/ld+json",
					innerHTML: JSON.stringify(this.jsonld), // <- set jsonld object in data or wherever you want
				},
			],
			__dangerouslyDisableSanitizers: ["script"], // <- this is important

			title: this.pageTitle,
			titleTemplate: (titleChunk) => {
				const appName = this.$applicationName()
				return titleChunk ? `${appName} | ${titleChunk}` : appName
			},
			meta: [
				{
					hid: "description",
					name: "description",
					content: this.getDescription,
				},
				{
					hid: "og:title",
					property: "og:title",
					content: this.pageTitle,
				},
				{
					hid: "og:description",
					property: "og:description",
					content: this.getOgDescription,
				},
				{
					hid: "og:image",
					property: "og:image",
					content:
						"/images/logo.png",

				},
				{
					hid: "og:url",
					property: "og:url",
					content: this.$route.fullPath,
				},
				{
					hid: "og:type",
					property: "og:type",
					content: "home",
				},
				{
					hid: "og:site_name",
					property: "og:site_name",
					content: "Action.jo",
				},
				{
					hid: "twitter:title",
					name: "twitter:title",
					content: this.pageTitle,
				},
				{
					hid: "twitter:description",
					name: "twitter:description",
					content: this.getDescription,
				},
				{
					hid: "twitter:image",
					name: "twitter:image",
					content: "/images/logo.png",
				},
				{
					hid: "twitter:card",
					name: "twitter:card",
					content: "summary_large_image",
				},
			],
			link: this.generateLinks,
		}
	},

	computed: {
		categories() {
			return this.$store.state.categories
		},
		getDescription() {
			return this.$i18n.locale === 'ar' ? "تسوق كل ما تحتاجه أونلاين في الأردن عبر أكشن موبايل ✔️ موبايلات، إلكترونيات، أكسسوارات، أجهزة منزلية والمزيد ✔️ أفضل الأسعار والجودة مضمونة!" : "Shop everything you need online in Jordan at Action Mobile ✔️ Electronics, smartphones, accessories, home appliances, and more ✔️ Best prices and top quality guaranteed!"
		},

		titleH1() {
			return this.$i18n.locale === 'ar' ? "اكشن موبايل" : "Action Mobile"
		},

		getOgDescription() {
			return this.$i18n.locale === 'ar' ? "تسوق كل ما تحتاجه أونلاين في الأردن عبر أكشن موبايل ✔️ موبايلات، إلكترونيات، أكسسوارات، أجهزة منزلية والمزيد ✔️ أفضل الأسعار والجودة مضمونة!" : "Shop everything you need online in Jordan at Action Mobile ✔️ Electronics, smartphones, accessories, home appliances, and more ✔️ Best prices and top quality guaranteed!"
		},
		pageTitle() {
			return this.$i18n.locale === 'ar' ? ' تسوق كل ما تحتاجه أونلاين في الأردن' : 'Shop Everything You Need Online in Jordan'
		},

		generateLinks() {
			if (this.$fetchState.pending) { return [] }

			const links = [
				{
					rel: "canonical",
					href: `${process.env.NUXT_ENV_BASE_URL}/${this.$i18n.locale}`,
				},
				{
					rel: "alternate",
					hreflang: `ar-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/ar`,
				},
				{
					rel: "alternate",
					hreflang: `en-jo`,
					href: `${process.env.NUXT_ENV_BASE_URL}/en`,
				},
				{
					rel: "alternate",
					hreflang: "x-default",
					href: `${process.env.NUXT_ENV_BASE_URL}/en`,
				},
			]
			return links
		},
	},
}
</script>

<style lang="scss">
@import "~/assets/mixins.scss";

.circle {
	width: 5px;
	/* Adjust the width and height to your desired size */
	height: 5px;
	margin-left: 2px;
	border-radius: 50%;
	/* Set the border-radius to 50% to create a circle */
	background-color: #29a6df;
	/* Set the background color to your desired color */
}

.flex-row {
	flex-wrap: wrap;
}

.item {
	flex-grow: 0;
	flex-basis: 100%;
}

.filter {
	width: 300px;
	@include responsive("width", 0, 300px);
	@include responsive("flex-shrink", 1, 0);
	@include responsive("margin-inline-end", 0, 16px);
	padding-inline-start: 8px;
	flex-shrink: 0;
}

.view {
	flex-grow: 1;
}

.toolbar {
	.icon {
		width: 36px;
		height: 36px;
		min-width: 0 !important;
		padding: 0 !important;
	}
}
</style>
