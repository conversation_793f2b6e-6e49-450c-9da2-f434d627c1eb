import { initializeApp } from "firebase/app"
import { getMessaging, getToken, onMessage, isSupported } from "firebase/messaging"
// import { onBackgroundMessage } from "firebase/messaging/sw";
const firebaseConfig = {
	apiKey: process.env.NUXT_ENV_FIREBASE_apiKey,
	authDomain: process.env.NUXT_ENV_FIREBASE_authDomain,
	databaseURL: process.env.NUXT_ENV_FIREBASE_databaseURL,
	projectId: process.env.NUXT_ENV_FIREBASE_projectId,
	storageBucket: process.env.NUXT_ENV_FIREBASE_storageBucket,
	messagingSenderId: process.env.NUXT_ENV_FIREBASE_messagingSenderId,
	appId: process.env.NUXT_ENV_FIREBASE_appId,
}
const firebaseApp = initializeApp(firebaseConfig)
export default (_context, inject) => {
	const messaging = getMessaging(firebaseApp)
	inject("firebase", {
		app: firebaseApp,
		messaging: {
			getMessaging: () => messaging,
			getToken: () => getToken(messaging),
			onMessage: callback => onMessage(messaging, callback),
			// onBackgroundMessage: (callback) => onBackgroundMessage(messaging, callback),
			isSupported: () => isSupported(),
		},
	})
}
