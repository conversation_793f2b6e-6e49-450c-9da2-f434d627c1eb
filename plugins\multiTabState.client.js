import shareMutations from "vuex-shared-mutations"

export default ({ store }) => {
	window.onNuxtReady((nuxt) => {
		shareMutations({
			predicate: [
				// "messaging/toggleSidebar",
				// "messaging/toggleSidebarMini",
				// "messaging/openThread",
				// "messaging/closeThread",
				// "messaging/minimizeThread",
				// "messaging/maximizeThread",
				// "messaging/resetUnreadNotifications",
				// "messaging/markNotificationAsRead",
				// "messaging/insertThreadData",
				// "messaging/pushMsg",
				// "messaging/unshiftMsg",
				// "messaging/setThreadReadMore",
				// "messaging/addThreadUnread",
				// "messaging/setState",
				// "messaging/markThreadAsRead",
				// "messaging/updateThread",
				// "messaging/msgSent",
				// "messaging/setMsgId",
				// "messaging/blockUser",
				// "messaging/unblockUser",
				// "messaging/sendMsg",
				// "messaging/insertNotifications",
				// "messaging/resetUnreadNotifications",
				// "messaging/addUnreadNotification",
				// "messaging/markNotificationAsRead",
				// "messaging/resetAll",
			],
		})(store)
	})
}
