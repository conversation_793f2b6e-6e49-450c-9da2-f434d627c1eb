export default [
	{
		path: "/",
		name: "index",
		component: () => import(/* webpackChunkName: "home" */ "~/pages/index").then(m => m.default || m),
	},

	{
		path: "/search",
		name: "search",
		component: () => import(/* webpackChunkName: "category" */ "~/pages/Listing.vue").then(m => m.default || m),
	},

	{
		path: "/category/:slugs(.*)",
		name: "category",
		component: () => import(/* webpackChunkName: "category" */ "~/pages/Listing.vue").then(m => m.default || m),
	},
	{
		path: "/brands/:slug(.*)",
		name: "brands",
		component: () => import(/* webpackChunkName: "category" */ "~/pages/Brands.vue").then(m => m.default || m),
	},
	{
		path: "/offers",
		name: "offers",
		component: () => import(/* webpackChunkName: "category" */ "~/pages/Offers.vue").then(m => m.default || m),
	},
	{
		path: "/action-souq",
		name: "action-souq",
		component: () => import(/* webpackChunkName: "used-categories" */ "~/pages/UsedCategories.vue").then(m => m.default || m),
	},

	{
		path: "/action-souq/used-category/:usedCategory",
		name: "used-category",
		component: () => import(/* webpackChunkName: "used-category" */ "~/pages/UsedCategory.vue").then(m => m.default || m),
	},
	{
		path: "/action-souq/used-product/:usedProduct",
		name: "used-product",
		component: () => import(/* webpackChunkName: "used-product" */ "~/pages/UsedProduct.vue").then(m => m.default || m),
	},
	{
		path: "/product/:productId(\\d+)/:varianceSlug",
		name: "old-product",
		component: () => import(/* webpackChunkName: "old-product" */ "~/pages/OldProduct.vue").then(m => m.default || m),
	},

	{
		path: "/product/:productSlug/:varianceSlug?",
		name: "product",
		component: () => import(/* webpackChunkName: "product" */ "~/pages/Product").then(m => m.default || m),
		props: true,
	},
	{
		path: "/checkout/:orderId(\\d+)/:step(\\d+)",
		name: "checkout-index",
		component: () => import(/* webpackChunkName: "checkout" */ "~/pages/Checkout/index").then(m => m.default || m),
		children: [
			{
				path: "/checkout/:orderId(\\d+)/:step(\\d+)",
				name: "checkout",
				component: () => import(/* webpackChunkName: "checkout" */ "~/pages/Checkout/Step").then(m => m.default || m),
			},
		],
	},
	{
		path: "/checkout/:orderId(\\d+)/:step(3)/verify",
		name: "payment-verify-index",
		component: () => import(/* webpackChunkName: "checkout" */ "~/pages/Checkout/index").then(m => m.default || m),
		children: [
			{
				path: "/checkout/:orderId(\\d+)/:step(3)/verify",
				name: "payment-verify",
				component: () => import(/* webpackChunkName: "checkout" */ "~/pages/Checkout/Step").then(m => m.default || m),
			},
		],
	},

	{
		path: "/branches",
		name: "branch",
		component: () => import(/* webpackChunkName: "branch" */ "~/pages/Branches").then(m => m.default || m),
	},
	{
		path: "/contact-us",
		name: "contact",
		component: () => import(/* webpackChunkName: "contact-us" */ "~/pages/ContactUs").then(m => m.default || m),
	},
	{
		path: "/page/:slug",
		name: "page",
		component: () => import(/* webpackChunkName: "page" */ "~/pages/Page").then(m => m.default || m),
	},

	{
		path: "/login",
		name: "login",
		component: () => import(/* webpackChunkName: "product" */ "~/pages/Login").then(m => m.default || m),
	},

	{
		path: "/my",
		name: "my-index",
		component: () => import(/* webpackChunkName: "profile" */ "~/pages/My/Index.vue").then(m => m.default || m),

		children: [
			// {
			// 	path: "/profile",
			// 	name: "my-profile",
			// 	  component: () => import(/* webpackChunkName: "profile" */ "~/pages/My/AccountDashboard.vue").then((m) => m.default || m),
			// 		hidden for now. waiting for Recent activity implementation
			// 	component: () => import(/* webpackChunkName: "profile" */ "~/pages/My/AccountInformation.vue").then((m) => m.default || m),
			// },
			{
				path: "/my/account",
				name: "my-account",
				component: () => import(/* webpackChunkName: "account" */ "~/pages/My/AccountInformation.vue").then(m => m.default || m),
			},
			{
				path: "/my/wishlist",
				name: "my-wishlist",
				component: () => import(/* webpackChunkName: "wishlist" */ "~/pages/My/Wishlist.vue").then(m => m.default || m),
			},
			{
				path: "/my/addresses",
				name: "my-addresses",
				component: () => import(/* webpackChunkName: "addresses" */ "~/pages/My/Addresses/Index.vue").then(m => m.default || m),
			},
			{
				path: "/my/address/new",
				name: "address-create",
				component: () =>
					import(/* webpackChunkName: "address-create" */ "~/pages/My/Addresses/Create.vue").then(m => m.default || m),
			},
			{
				path: "/my/address/edit/:id",
				name: "address-edit",
				component: () => import(/* webpackChunkName: "address-edit" */ "~/pages/My/Addresses/Edit.vue").then(m => m.default || m),
			},

			{
				path: "/my/orders",
				name: "my-orders",
				component: () => import(/* webpackChunkName: "orders" */ "~/pages/My/Orders/Index.vue").then(m => m.default || m),
			},
			{
				path: "/my/orders/:order",
				name: "my-order",
				component: () => import(/* webpackChunkName: "orders" */ "~/pages/My/Orders/Order.vue").then(m => m.default || m),
			},
			{
				path: "/my/action-souq/ads",
				name: "my-ads",
				component: () => import(/* webpackChunkName: "ads" */ "~/pages/My/UsedProducts/Index.vue").then(m => m.default || m),
			},
			{
				path: "/my/action-souq/new-used-product",
				name: "add-used-product",
				component: () =>
					import(/* webpackChunkName: "add-used-categories" */ "~/pages/My/UsedProducts/Create.vue").then(m => m.default || m),
			},

			{
				path: "/my/action-souq/edit-used-product/:usedProduct",
				name: "edit-used-product",
				component: () =>
					import(/* webpackChunkName: "edit-used-product" */ "~/pages/My/UsedProducts/Edit.vue").then(m => m.default || m),
			},

			{
				path: "/my/reviews",
				name: "my-reviews",
				component: () => import(/* webpackChunkName: "reviews" */ "~/pages/My/Reviews.vue").then(m => m.default || m),
			},
			{
				path: "/my/wallet",
				name: "my-wallet",
				component: () => import(/* webpackChunkName: "wallet" */ "~/pages/My/Wallet.vue").then(m => m.default || m),
			},
		],
	},

	{
		path: "/admin",
		name: "admin",
		component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/Index").then(m => m.default || m),
		children: [
			{
				path: "/dashboard",
				name: "dashboard",
				component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/Dashboard").then(m => m.default || m),
			},

			{
				path: "/change-password",
				name: "admin-change-password",
				component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/ChangePassword").then(m => m.default || m),
			},

			{
				path: "/profile",
				name: "admin-profile",
				component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/Profile").then(m => m.default || m),
			},

			{
				path: "/admin/categories-and-filters",
				name: "CategoriesAndFilters",
				component: () =>
					import(/* webpackChunkName: "categories-and-filters" */ "~/pages/Admin/CategoriesAndFilters/Index").then(
						m => m.default || m,
					),
				children: [
					{
						path: "/admin/categories-and-filters/filters",
						name: "filters",
						component: () =>
							import(/* webpackChunkName: "filters" */ "~/pages/Admin/CategoriesAndFilters/Filters").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin/categories-and-filters/categories",
						name: "categories",
						component: () =>
							import(/* webpackChunkName: "categories" */ "~/pages/Admin/CategoriesAndFilters/Categories").then(
								m => m.default || m,
							),
					},
				],
			},

			{
				path: "/admin/action-used-souq",
				name: "ActionUsedSouq",
				component: () =>
					import(/* webpackChunkName: "ActionUsedSouq" */ "~/pages/Admin/ActionUsedSouq/Index").then(m => m.default || m),
				children: [
					{
						path: "/admin/used-categories",
						name: "used-categories",
						component: () =>
							import(/* webpackChunkName: "used-categories" */ "~/pages/Admin/ActionUsedSouq/UsedCategories").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin/used-products",
						name: "used-products",
						component: () =>
							import(/* webpackChunkName: "used-products" */ "~/pages/Admin/ActionUsedSouq/UsedProducts").then(
								m => m.default || m,
							),
					},
				],
			},

			{
				path: "/admin/product-groups",
				name: "groups",
				component: () =>
					import(/* webpackChunkName: "product-groups" */ "~/pages/Admin/ProductGroups/Index").then(m => m.default || m),
			},
			{
				path: "/admin/products",
				name: "products",
				component: () => import(/* webpackChunkName: "products" */ "~/pages/Admin/Products/Index").then(m => m.default || m),
			},
			{
				path: "/admin/brands",
				name: "admin-brands",
				component: () => import(/* webpackChunkName: "brands" */ "~/pages/Admin/Brands/Index").then(m => m.default || m),
			},
			{
				path: "/admin/attributes",
				name: "attributes",
				component: () => import(/* webpackChunkName: "attributes" */ "~/pages/Admin/Attributes/Index").then(m => m.default || m),
			},
			{
				path: "/admin/labels",
				name: "labels",
				component: () => import(/* webpackChunkName: "labels" */ "~/pages/Admin/Labels/Index").then(m => m.default || m),
			},
			{
				path: "/admin/shippings",
				name: "Shippings",
				component: () => import(/* webpackChunkName: "SystemConfig" */ "~/pages/Admin/Shipping/Index").then(m => m.default || m),
				children: [

					{
						path: "/admin/shippings/shipping-carriers",
						name: "shippings-carriers",
						component: () =>
							import(/* webpackChunkName: "shippings-carriers" */ "~/pages/Admin/Shipping/ShippingsCarriers").then(
								m => m.default || m,
							),
					},
				],
			},

			{
				path: "/admin/ai",
				name: "ai",
				component: () => import(/* webpackChunkName: "ai" */ "~/pages/Admin/AI/Index").then(m => m.default || m),
			},
			{
				path: "/admin/system-configuration",
				name: "SystemConfiguration",
				component: () =>
					import(/* webpackChunkName: "SystemConfig" */ "~/pages/Admin/SystemConfiguration/Index").then(m => m.default || m),
				children: [
					{
						path: "/admin/system-configuration/currencies",
						name: "currencies",
						component: () =>
							import(/* webpackChunkName: "currencies" */ "~/pages/Admin/SystemConfiguration/Currencies").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin/system-configuration/payments-methods",
						name: "paymentsMethods",
						component: () =>
							import(/* webpackChunkName: "paymentsMethods" */ "~/pages/Admin/SystemConfiguration/PaymentsMethods").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin/system-configuration/countries",
						name: "countries",
						component: () =>
							import(/* webpackChunkName: "countries" */ "~/pages/Admin/SystemConfiguration/Countries").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin/system-configuration/cities",
						name: "cities",
						component: () =>
							import(/* webpackChunkName: "cities" */ "~/pages/Admin/SystemConfiguration/Cities").then(m => m.default || m),
					},
					{
						path: "/admin/system-configuration/banners",
						name: "banners",
						component: () =>
							import(/* webpackChunkName: "cities" */ "~/pages/Admin/SystemConfiguration/Banners").then(m => m.default || m),
					},
					{
						path: "/admin/system-configuration/mailsearch",
						name: "mailsearch",
						component: () =>
							import(/* webpackChunkName: "cities" */ "~/pages/Admin/SystemConfiguration/Mailsearch").then(m => m.default || m),
					},
				],
			},

			{
				path: "/admin/suppliers",
				name: "suppliers",
				component: () => import(/* webpackChunkName: "suppliers" */ "~/pages/Admin/Suppliers/Index").then(m => m.default || m),
			},
			{
				path: "/admin/orders",
				name: "orders-index",
				component: () => import(/* webpackChunkName: "orders" */ "~/pages/Admin/Orders/Index").then(m => m.default || m),
				children: [
					{
						path: "/admin/orders/:status?",
						name: "orders",
						component: () => import(/* webpackChunkName: "order" */ "~/pages/Admin/Orders/Orders").then(m => m.default || m),
					},
				],
			},
			{
				path: "/admin/ratings",
				name: "ratings",
				component: () => import(/* webpackChunkName: "ratings" */ "~/pages/Admin/Ratings/Index").then(m => m.default || m),
			},
			{
				path: "/admin/auctions",
				name: "auctions",
				component: () => import(/* webpackChunkName: "auctions" */ "~/pages/Admin/Auctions/Index").then(m => m.default || m),
			},
			{
				path: "/admin/users",
				name: "users",
				component: () => import(/* webpackChunkName: "users" */ "~/pages/Admin/Users/<USER>").then(m => m.default || m),
				children: [
					{
						path: "/admin/customers",
						name: "customers",
						component: () =>
							import(/* webpackChunkName: "customers" */ "~/pages/Admin/Users/<USER>").then(m => m.default || m),
					},
					{
						path: "/admin/request-change-name",
						name: "requestChangeName",
						component: () =>
							import(/* webpackChunkName: "requestChangeName" */ "~/pages/Admin/Users/<USER>").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin/deleted",
						name: "deletedUsers",
						component: () =>
							import(/* webpackChunkName: "deletedUsers" */ "~/pages/Admin/Users/<USER>").then(m => m.default || m),
					},
				],
			},
			{
				path: "/admin/pages",
				name: "pages",
				component: () => import(/* webpackChunkName: "pages" */ "~/pages/Admin/Pages/Index").then(m => m.default || m),
			},
			{
				path: "/admin/contact-us",
				name: "contact-us",
				component: () => import(/* webpackChunkName: "contact-us" */ "~/pages/Admin/ContactUs/Index").then(m => m.default || m),
			},
			{
				path: "/admin/translation",
				name: "translations",
				component: () =>
					import(/* webpackChunkName: "translation" */ "~/pages/Admin/Translations/Static").then(m => m.default || m),
			},
			{
				path: "/admin/translationTags",
				name: "translationTags",
				component: () =>
					import(/* webpackChunkName: "translationTags" */ "~/pages/Admin/Translations/Tags").then(m => m.default || m),
			},
		],
	},
	// admin v2----------------------------------------------------------------------------------------------------
	{
		path: "/admin-v2",
		name: "admin",
		component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/Index").then(m => m.default || m),
		children: [
			{
				path: "/dashboard",
				name: "dashboard",
				component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/Dashboard").then(m => m.default || m),
			},

			{
				path: "/change-password",
				name: "admin-change-password",
				component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/ChangePassword").then(m => m.default || m),
			},

			{
				path: "/profile",
				name: "admin-profile",
				component: () => import(/* webpackChunkName: "admin" */ "~/pages/Admin/Profile").then(m => m.default || m),
			},

			{
				path: "/admin-v2/categories-and-filters",
				name: "CategoriesAndFilters",
				component: () =>
					import(/* webpackChunkName: "categories-and-filters" */ "~/pages/Admin/CategoriesAndFilters/Index").then(
						m => m.default || m,
					),
				children: [
					{
						path: "/admin-v2/categories-and-filters/filters",
						name: "filters",
						component: () =>
							import(/* webpackChunkName: "filters" */ "~/pages/Admin/CategoriesAndFilters/Filters").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin-v2/categories-and-filters/categories",
						name: "categories",
						component: () =>
							import(/* webpackChunkName: "categories" */ "~/pages/Admin/CategoriesAndFilters/Categories").then(
								m => m.default || m,
							),
					},
				],
			},

			{
				path: "/admin-v2/action-used-souq",
				name: "ActionUsedSouq",
				component: () =>
					import(/* webpackChunkName: "ActionUsedSouq" */ "~/pages/Admin/ActionUsedSouq/Index").then(m => m.default || m),
				children: [
					{
						path: "/admin-v2/used-categories",
						name: "used-categories",
						component: () =>
							import(/* webpackChunkName: "used-categories" */ "~/pages/Admin/ActionUsedSouq/UsedCategories").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin-v2/used-products",
						name: "used-products",
						component: () =>
							import(/* webpackChunkName: "used-products" */ "~/pages/Admin/ActionUsedSouq/UsedProducts").then(
								m => m.default || m,
							),
					},
				],
			},

			{
				path: "/admin-v2/product-groups",
				name: "groups",
				component: () =>
					import(/* webpackChunkName: "product-groups" */ "~/pages/Admin/ProductGroups/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/products",
				name: "products",
				component: () => import(/* webpackChunkName: "products" */ "~/pages/Admin/Products/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/brands",
				name: "admin-brands",
				component: () => import(/* webpackChunkName: "brands" */ "~/pages/Admin/Brands/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/attributes",
				name: "attributes",
				component: () => import(/* webpackChunkName: "attributes" */ "~/pages/Admin/Attributes/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/labels",
				name: "labels",
				component: () => import(/* webpackChunkName: "labels" */ "~/pages/Admin/Labels/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/shippings",
				name: "Shippings",
				component: () => import(/* webpackChunkName: "SystemConfig" */ "~/pages/Admin/Shipping/Index").then(m => m.default || m),
				children: [

					{
						path: "/admin-v2/shippings/shipping-carriers",
						name: "shippings-carriers",
						component: () =>
							import(/* webpackChunkName: "shippings-carriers" */ "~/pages/Admin/Shipping/ShippingsCarriers").then(
								m => m.default || m,
							),
					},
				],
			},

			{
				path: "/admin-v2/ai",
				name: "ai",
				component: () => import(/* webpackChunkName: "ai" */ "~/pages/Admin/AI/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/system-configuration",
				name: "SystemConfiguration",
				component: () =>
					import(/* webpackChunkName: "SystemConfig" */ "~/pages/Admin/SystemConfiguration/Index").then(m => m.default || m),
				children: [
					{
						path: "/admin-v2/system-configuration/currencies",
						name: "currencies",
						component: () =>
							import(/* webpackChunkName: "currencies" */ "~/pages/Admin/SystemConfiguration/Currencies").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin-v2/system-configuration/payments-methods",
						name: "paymentsMethods",
						component: () =>
							import(/* webpackChunkName: "paymentsMethods" */ "~/pages/Admin/SystemConfiguration/PaymentsMethods").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin-v2/system-configuration/countries",
						name: "countries",
						component: () =>
							import(/* webpackChunkName: "countries" */ "~/pages/Admin/SystemConfiguration/Countries").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin-v2/system-configuration/cities",
						name: "cities",
						component: () =>
							import(/* webpackChunkName: "cities" */ "~/pages/Admin/SystemConfiguration/Cities").then(m => m.default || m),
					},
					{
						path: "/admin-v2/system-configuration/banners",
						name: "banners",
						component: () =>
							import(/* webpackChunkName: "cities" */ "~/pages/Admin/SystemConfiguration/Banners").then(m => m.default || m),
					},
					{
						path: "/admin-v2/system-configuration/mailsearch",
						name: "mailsearch",
						component: () =>
							import(/* webpackChunkName: "cities" */ "~/pages/Admin/SystemConfiguration/Mailsearch").then(m => m.default || m),
					},
				],
			},

			{
				path: "/admin-v2/suppliers",
				name: "suppliers",
				component: () => import(/* webpackChunkName: "suppliers" */ "~/pages/Admin/Suppliers/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/orders",
				name: "orders-index",
				component: () => import(/* webpackChunkName: "orders" */ "~/pages/Admin/Orders/Index").then(m => m.default || m),
				children: [
					{
						path: "/admin-v2/orders/:status?",
						name: "orders",
						component: () => import(/* webpackChunkName: "order" */ "~/pages/Admin/Orders/Orders").then(m => m.default || m),
					},
				],
			},
			{
				path: "/admin-v2/ratings",
				name: "ratings",
				component: () => import(/* webpackChunkName: "ratings" */ "~/pages/Admin/Ratings/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/auctions",
				name: "auctions",
				component: () => import(/* webpackChunkName: "auctions" */ "~/pages/Admin/Auctions/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/users",
				name: "users",
				component: () => import(/* webpackChunkName: "users" */ "~/pages/Admin/Users/<USER>").then(m => m.default || m),
				children: [
					{
						path: "/admin-v2/customers",
						name: "customers",
						component: () =>
							import(/* webpackChunkName: "customers" */ "~/pages/Admin/Users/<USER>").then(m => m.default || m),
					},
					{
						path: "/admin-v2/request-change-name",
						name: "requestChangeName",
						component: () =>
							import(/* webpackChunkName: "requestChangeName" */ "~/pages/Admin/Users/<USER>").then(
								m => m.default || m,
							),
					},
					{
						path: "/admin-v2/deleted",
						name: "deletedUsers",
						component: () =>
							import(/* webpackChunkName: "deletedUsers" */ "~/pages/Admin/Users/<USER>").then(m => m.default || m),
					},
				],
			},
			{
				path: "/admin-v2/pages",
				name: "pages",
				component: () => import(/* webpackChunkName: "pages" */ "~/pages/Admin/Pages/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/contact-us",
				name: "contact-us",
				component: () => import(/* webpackChunkName: "contact-us" */ "~/pages/Admin/ContactUs/Index").then(m => m.default || m),
			},
			{
				path: "/admin-v2/translation",
				name: "translations",
				component: () =>
					import(/* webpackChunkName: "translation" */ "~/pages/Admin/Translations/Static").then(m => m.default || m),
			},
			{
				path: "/admin-v2/translationTags",
				name: "translationTags",
				component: () =>
					import(/* webpackChunkName: "translationTags" */ "~/pages/Admin/Translations/Tags").then(m => m.default || m),
			},
		],
	},
]
