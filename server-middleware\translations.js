import dot from "dot-object"
// const fs = require("fs");
// const path = require("path");
const express = require("express")
const bodyParser = require("body-parser")

// const oldLocales = {
// 	en: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/en.json"))),
// 	ar: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/ar.json"))),
// 	fa: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/fa.json"))),
// 	vi: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/vi.json"))),
// 	th: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/th.json"))),
// };

const oldLocales = require("../old-locale/index.js")
const app = express()
app.use(
	bodyParser.urlencoded({
		extended: true,
	}),
)
app.use(express.json())

app.get("/local/translations/old", (req, res) => {
	const result = {}
	for (const locale in oldLocales) {
		result[locale] = dot.dot(oldLocales[locale])
	}
	return res.status(200).json(result)
})

// app.post("/local/set-translations", async (req, res) => {
// 	console.log(req.body);
// 	const { oldKey, key, value } = req.body;
// 	for (const locale in locales) {
// 		const translation = value[locale] || oldLocales[locale][oldKey];
// 		if (translation) {
// 			_.set(locales[locale], key, translation);
// 			await fs.writeFileSync(path.join(__dirname, "../locales/" + locale + ".json"), JSON.stringify(locales[locale], null, 4));
// 		}
// 	}

// 	// console.log(oldLocales);
// 	res.json({ locales });
// });
// app.post("/local/translations", async (req, res) => {

// 	const { oldKey, key, value } = req.body;

// 	if (oldKey) {
// 		// for (const locale in locales) {
// 		// 	const translation = value[locale] || oldLocales[locale][oldKey];
// 		// 	if (translation) {
// 		// 		_.set(locales[locale], key, translation);
// 		// 		await fs.writeFileSync(path.join(__dirname, "../locales/" + locale + ".json"), JSON.stringify(locales[locale], null, 4));
// 		// 	}
// 		// }
// 	}

// 	const data = [];

// 	for (const locale in value) {
// 		if (value[locale]) {
// 			const result = await prisma.locales.create({
// 				data: {
// 					key,
// 					value: value[locale],
// 					locale,
// 				},
// 			});
// 			data.push(result);
// 		}
// 	}

// 	res.json(data);
// });

// app.get("/local/translations/file-format/:locale", async (req, res) => {
// 	const items = await prisma.locales.findMany({
// 		where: { locale: req.params.locale },
// 		select: {
// 			key: true,
// 			value: true,
// 		},
// 	});

// 	const result = {};
// 	for (const k in items) {
// 		result[items[k].key] = items[k].value;
// 	}

// 	res.json(result);
// });

// app.get("/local/translations/missing(/locale-:locale)?", async (req, res) => {
// 	let result =
// 		await prisma.$queryRaw`SELECT *,group_concat(locale) as locales FROM "locales" GROUP BY key HAVING COUNT(*) < ${locales.length}`;

// 	result = result.map((item) => {
// 		item.locales = item.locales.split(",");
// 		item.missing = locales.filter((locale) => !item.locales.includes(locale.code)).map((locale) => locale.code);
// 		return item;
// 	});

// 	if (req.params.locale) {
// 		result = result.filter((item) => item.missing.includes(req.params.locale));
// 	}

// 	res.json(result);
// });

// app.get("/local/translations(/locale-:locale)?", async (req, res) => {
// 	const result = await prisma.locales.findMany({
// 		where: { locale: req.params.locale },
// 	});
// 	res.json(result);
// });

// app.get("/local/translations/:key", async (req, res) => {
// 	const { key } = req.params;
// 	let result = await prisma.locales.findMany({
// 		where: { key },
// 	});

// 	result = {
// 		key,
// 		value: result.reduce((acc, cur) => {
// 			acc[cur.locale] = cur.value;
// 			return acc;
// 		}, {}),
// 	};
// 	res.json(result);
// });

// app.put("/local/translations/:key", async (req, res) => {
// 	const { key } = req.params;
// 	const { value, editKey, newKey } = req.body;

// 	const data = [];

// 	for (const locale in value) {

// 		if (value[locale]) {
// 			// check if record exist
// 			const record = await prisma.locales.findFirst({
// 				where: { key, locale },
// 			});
// 			if (record) {
// 				// update record
// 				const result = await prisma.locales.update({
// 					where: { id: record.id },
// 					data: {
// 						value: value[locale],
// 						key: editKey ? Object.values(newKey).join(".") : undefined,
// 					},
// 				});
// 				data.push(result);
// 			} else {
// 				// create record
// 				const result = await prisma.locales.create({
// 					data: {
// 						key,
// 						value: value[locale],
// 						locale,
// 					},
// 				});
// 				data.push(result);
// 			}
// 		}
// 	}

// 	res.json(data);
// });

// app.delete("/local/translations/:key", async (req, res) => {
// 	const { key } = req.params;

// 	const record = await prisma.locales.deleteMany({
// 		where: { key },
// 	});

// 	res.json(record);
// });

module.exports = app
