console.log("messaging", firebase)
messaging.setBackgroundMessageHandler(function (payload) {
	console.log("[firebase-messaging-sw.js] Received background message ")
	self.registration.hideNotification()

	return null
})

self.addEventListener("push", function (e) {
	data = e.data.json()
	const options = {
		tag: "notification-1",
		body: data.notification.body,
		vibrate: [100, 50, 100],
		data: {
			dateOfArrival: Date.now(),
			primaryKey: "1",
		},
	}
	self.registration.showNotification(data.notification.title, options)
})

self.addEventListener(
	"notificationclick",
	function (event) {
		event.notification.close()
		const url = "home"
		event.waitUntil(
			self.clients.matchAll({ type: "window" }).then((windowClients) => {
				// Check if there is already a window/tab open with the target URL
				for (let i = 0; i < windowClients.length; i++) {
					const client = windowClients[i]
					// If so, just focus it.

					if (client.url === url && "focus" in client) {
						return client.focus()
					}
				}

				if (self.clients.openWindow) {
					console.log("open window")
				}
			}),
		)
	},
	false,
)

console.log("fcm handler")
