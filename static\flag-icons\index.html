<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Country Flags in SVG | flag-icons</title>
    <!-- Bootstrap -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3"
      crossorigin="anonymous"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <!-- Docs -->
    <link href="./assets/docs.css" rel="stylesheet" />

    <!-- Analytics Code -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=UA-42119746-2"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());
      gtag('config', 'UA-42119746-2');
    </script>
  </head>
  <body>
    <div class="header">
      <div class="container-md">
        <h1>Country Flags in SVG</h1>
        <p class="lead">
          A curated collection of all country flags in SVG — plus the CSS for
          easier integration.
        </p>
        <p class="lead">
          All flags are in <strong>1x1</strong> and <strong>4x3</strong> formats
          only.
        </p>
        <pre class="install">yarn add flag-icons</pre>
        <p>For more instructions check out GitHub.</p>
        <a class="btn btn-light" href="https://github.com/lipis/flag-icons"
          ><i class="fab fa-github"></i> View on GitHub</a
        >
        <a
          class="btn btn-light"
          href="https://github.com/lipis/flag-icons/archive/main.zip"
          ><i class="fas fa-download"></i> Download Flags</a
        >
      </div>
    </div>
    <div class="container-md">
      <h2>ISO 3166-1-alpha-2 Flags</h2>
      <div id="iso-flags" class="row"></div>
      <h2>Other Flags</h2>
      <div id="non-iso-flags" class="row"></div>
    </div>
    <script src="./assets/docs.js"></script>
  </body>
</html>
