require('dotenv').config() // Load environment variables from .env file
const util = require('util')
const redis = require('redis')

console.log(
	'REDIS_HOST:', process.env.NUXT_ENV_REDIS_HOST,
	'REDIS_PORT:', process.env.NUXT_ENV_REDIS_PORT,
	'REDIS_DATABASE:', process.env.NUXT_ENV_REDIS_DATABASE,
)

const redisUrl = `redis://${process.env.NUXT_ENV_REDIS_HOST}:${process.env.NUXT_ENV_REDIS_PORT}`

// Validate Redis URL
if (!process.env.NUXT_ENV_REDIS_HOST || !process.env.NUXT_ENV_REDIS_PORT) {
	throw new Error('Missing required Redis environment variables.')
}

const client = redis.createClient({
	url: redisUrl,
	database: process.env.NUXT_ENV_REDIS_DATABASE,
})

client.get = util.promisify(client.get)
client.set = util.promisify(client.set)
client.del = util.promisify(client.del)

client.on('error', (error) => {
	console.error(error)
})

client.on('ready', () => {
	console.log('Redis Ready')
})

client.on('connect', () => {
	console.log('Redis Connected')
})

// client.connect().catch(console.error)
